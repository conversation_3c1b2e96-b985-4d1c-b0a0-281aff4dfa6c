{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}لوحة تحكم الحجوزات{% endblock %}

{% block extra_css %}
<style>
.reservation-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.reservation-stat {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    margin-bottom: 15px;
}

.reservation-stat h3 {
    font-size: 2rem;
    margin-bottom: 5px;
    color: #fff;
}

.confirmed-card {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.pending-card {
    background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
}

.cancelled-card {
    background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
}

.revenue-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.departure-item {
    background: white;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 4px solid #667eea;
}

.departure-item.urgent {
    border-left-color: #ff6b6b;
    background: linear-gradient(135deg, rgba(255,107,107,0.1) 0%, rgba(255,107,107,0.05) 100%);
}

.chart-container {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.btn-reservation {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-reservation:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">📅 لوحة تحكم الحجوزات</h1>
                    <p class="text-muted">إدارة شاملة لجميع الحجوزات والرحلات</p>
                </div>
                <div>
                    <a href="{% url 'reservations:reservation_list' %}" class="btn btn-reservation me-2">
                        <i class="fas fa-list"></i> جميع الحجوزات
                    </a>
                    <a href="{% url 'reservations:calendar' %}" class="btn btn-outline-primary">
                        <i class="fas fa-calendar"></i> التقويم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="reservation-card confirmed-card">
                <div class="reservation-stat">
                    <h3>{{ stats.confirmed_reservations }}</h3>
                    <p><i class="fas fa-check-circle"></i> حجوزات مؤكدة</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="reservation-card pending-card">
                <div class="reservation-stat">
                    <h3>{{ stats.pending_reservations }}</h3>
                    <p><i class="fas fa-clock"></i> حجوزات معلقة</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="reservation-card cancelled-card">
                <div class="reservation-stat">
                    <h3>{{ stats.cancelled_reservations }}</h3>
                    <p><i class="fas fa-times-circle"></i> حجوزات ملغية</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="reservation-card revenue-card">
                <div class="reservation-stat">
                    <h3>{{ stats.total_participants }}</h3>
                    <p><i class="fas fa-users"></i> إجمالي المشاركين</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Statistics -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="reservation-card">
                <h5><i class="fas fa-calendar-month"></i> إحصائيات الشهر الحالي</h5>
                <div class="row">
                    <div class="col-6">
                        <div class="reservation-stat">
                            <h4>{{ stats.monthly_reservations }}</h4>
                            <p>حجوزات جديدة</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="reservation-stat">
                            <h4>{{ stats.total_revenue|floatformat:0 }} درهم</h4>
                            <p>إجمالي الإيرادات</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="reservation-card">
                <h5><i class="fas fa-plane-departure"></i> الرحلات القادمة</h5>
                <div class="row">
                    <div class="col-6">
                        <div class="reservation-stat">
                            <h4>{{ stats.upcoming_departures }}</h4>
                            <p>رحلات هذا الأسبوع</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="reservation-stat">
                            <h4>{{ stats.total_reservations }}</h4>
                            <p>إجمالي الحجوزات</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Lists -->
    <div class="row">
        <!-- Booking Trends Chart -->
        <div class="col-lg-8">
            <div class="chart-container">
                <h5><i class="fas fa-chart-line"></i> اتجاه الحجوزات الشهرية</h5>
                <canvas id="bookingChart" height="100"></canvas>
            </div>
        </div>

        <!-- Recent Reservations -->
        <div class="col-lg-4">
            <div class="chart-container">
                <h5><i class="fas fa-bookmark"></i> أحدث الحجوزات</h5>
                <div class="list-group list-group-flush">
                    {% for reservation in recent_reservations %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">{{ reservation.reservation_number }}</h6>
                            <p class="mb-1 text-muted">{{ reservation.client.full_name_ar }}</p>
                            <small>{{ reservation.package.title_ar|truncatechars:30 }}</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-primary">{{ reservation.total_amount }} درهم</span>
                            <br>
                            <small class="badge bg-{{ reservation.status|yesno:'success,warning,danger' }}">
                                {{ reservation.get_status_display }}
                            </small>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p>لا توجد حجوزات حديثة</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Upcoming Departures -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="chart-container">
                <h5><i class="fas fa-plane-departure"></i> الرحلات القادمة (الأسبوعين القادمين)</h5>
                {% if upcoming_departures %}
                <div class="row">
                    {% for departure in upcoming_departures %}
                    <div class="col-lg-6 col-md-12">
                        <div class="departure-item {% if departure.departure_date|timeuntil|slice:':1' == '1' %}urgent{% endif %}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ departure.package.title_ar }}</h6>
                                    <p class="mb-1 text-muted">{{ departure.client.full_name_ar }}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar"></i> {{ departure.departure_date }}
                                        <span class="ms-2">
                                            <i class="fas fa-users"></i> {{ departure.total_participants }} مشارك
                                        </span>
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-info">{{ departure.reservation_number }}</span>
                                    <br>
                                    <small class="text-muted">{{ departure.departure_date|timeuntil }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-plane fa-3x mb-3"></i>
                    <h5>لا توجد رحلات قادمة</h5>
                    <p>لا توجد رحلات مجدولة للأسبوعين القادمين</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Payment Status Distribution -->
    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="chart-container">
                <h5><i class="fas fa-credit-card"></i> توزيع حالة المدفوعات</h5>
                <canvas id="paymentChart" height="150"></canvas>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="chart-container">
                <h5><i class="fas fa-chart-pie"></i> إحصائيات سريعة</h5>
                <div class="row">
                    {% for stat in payment_stats %}
                    <div class="col-6 mb-3">
                        <div class="text-center">
                            <h4 class="text-primary">{{ stat.count }}</h4>
                            <p class="text-muted mb-0">{{ stat.payment_status|default:"غير محدد" }}</p>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Booking Trends Chart
const bookingCtx = document.getElementById('bookingChart').getContext('2d');
const bookingChart = new Chart(bookingCtx, {
    type: 'line',
    data: {
        labels: [{% for data in monthly_data %}'{{ data.month }}'{% if not forloop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'عدد الحجوزات',
            data: [{% for data in monthly_data %}{{ data.bookings }}{% if not forloop.last %},{% endif %}{% endfor %}],
            borderColor: 'rgb(102, 126, 234)',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'الإيرادات (درهم)',
            data: [{% for data in monthly_data %}{{ data.revenue }}{% if not forloop.last %},{% endif %}{% endfor %}],
            borderColor: 'rgb(56, 239, 125)',
            backgroundColor: 'rgba(56, 239, 125, 0.1)',
            tension: 0.4,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                beginAtZero: true
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                beginAtZero: true,
                grid: {
                    drawOnChartArea: false,
                }
            }
        }
    }
});

// Payment Status Chart
const paymentCtx = document.getElementById('paymentChart').getContext('2d');
const paymentChart = new Chart(paymentCtx, {
    type: 'doughnut',
    data: {
        labels: [{% for stat in payment_stats %}'{{ stat.payment_status|default:"غير محدد" }}'{% if not forloop.last %},{% endif %}{% endfor %}],
        datasets: [{
            data: [{% for stat in payment_stats %}{{ stat.count }}{% if not forloop.last %},{% endif %}{% endfor %}],
            backgroundColor: [
                '#667eea',
                '#764ba2',
                '#11998e',
                '#38ef7d',
                '#fdbb2d',
                '#22c1c3'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endblock %}
