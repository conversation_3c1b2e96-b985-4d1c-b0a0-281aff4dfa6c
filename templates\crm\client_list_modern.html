{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}
{% load static %}

{% block title %}{% trans "قائمة العملاء" %}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/datatables.net-bs5@1.13.4/css/dataTables.bootstrap5.min.css" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/datatables.net-responsive-bs5@2.4.1/css/responsive.bootstrap5.min.css" rel="stylesheet">
<style>
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-5px);
    }

    .stats-card .stats-icon {
        font-size: 3rem;
        opacity: 0.8;
        margin-bottom: 15px;
    }

    .stats-card .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .stats-card .stats-label {
        font-size: 1rem;
        opacity: 0.9;
    }

    .search-container {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    }

    .table-container {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    }

    .btn-action {
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.875rem;
        margin: 2px;
        transition: all 0.3s ease;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .client-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.2rem;
    }

    .vip-badge {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .client-type-badge {
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .client-type-individual {
        background: #e3f2fd;
        color: #1976d2;
    }

    .client-type-family {
        background: #f3e5f5;
        color: #7b1fa2;
    }

    .client-type-group {
        background: #e8f5e8;
        color: #388e3c;
    }

    .client-type-corporate {
        background: #fff3e0;
        color: #f57c00;
    }

    .table-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .bulk-actions {
        display: none;
        background: #f8f9fa;
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 20px;
    }

    .bulk-actions.show {
        display: block;
    }

    .floating-add-btn {
        position: fixed;
        bottom: 30px;
        left: 30px;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        border: none;
        font-size: 1.5rem;
        box-shadow: 0 10px 30px rgba(17, 153, 142, 0.4);
        transition: all 0.3s ease;
        z-index: 1000;
    }

    .floating-add-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 15px 40px rgba(17, 153, 142, 0.6);
    }

    @media (max-width: 768px) {
        .stats-card {
            text-align: center;
            margin-bottom: 15px;
        }

        .search-container,
        .table-container {
            padding: 15px;
            margin-bottom: 15px;
        }

        .floating-add-btn {
            bottom: 20px;
            left: 20px;
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{% url 'core:dashboard' %}">
                    <i class="fas fa-home"></i> {% trans "الرئيسية" %}
                </a>
            </li>
            <li class="breadcrumb-item active">{% trans "العملاء" %}</li>
        </ol>
    </nav>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stats-number">{{ stats.total_clients|default:0 }}</div>
                <div class="stats-label">{% trans "إجمالي العملاء" %}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="stats-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="stats-number">{{ stats.vip_clients|default:0 }}</div>
                <div class="stats-label">{% trans "عملاء مميزون" %}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                <div class="stats-icon">
                    <i class="fas fa-user"></i>
                </div>
                <div class="stats-number">{{ stats.individual_clients|default:0 }}</div>
                <div class="stats-label">{% trans "عملاء أفراد" %}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333;">
                <div class="stats-icon">
                    <i class="fas fa-building"></i>
                </div>
                <div class="stats-number">{{ stats.corporate_clients|default:0 }}</div>
                <div class="stats-label">{% trans "عملاء شركات" %}</div>
            </div>
        </div>
    </div>

    <!-- Search Container -->
    <div class="search-container">
        <h5 class="mb-3">
            <i class="fas fa-search me-2"></i>
            {% trans "البحث والتصفية" %}
        </h5>

        <form method="get" id="searchForm">
            {% crispy search_form %}

            <div class="row mt-3">
                <div class="col-md-6">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>
                        {% trans "بحث" %}
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetSearch()">
                        <i class="fas fa-undo me-2"></i>
                        {% trans "إعادة تعيين" %}
                    </button>
                </div>
                <div class="col-md-6 text-end">
                    <a href="{% url 'crm:client_export' %}?{{ request.GET.urlencode }}" class="btn btn-success">
                        <i class="fas fa-download me-2"></i>
                        {% trans "تصدير Excel" %}
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Table Container -->
    <div class="table-container">
        <!-- Table Actions -->
        <div class="table-actions">
            <div>
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    {% trans "قائمة العملاء" %}
                    <span class="badge bg-primary ms-2">{{ total_results|default:0 }}</span>
                </h5>
            </div>
            <div>
                <button class="btn btn-outline-primary btn-sm" onclick="toggleBulkActions()">
                    <i class="fas fa-check-square me-1"></i>
                    {% trans "تحديد متعدد" %}
                </button>
            </div>
        </div>

        <!-- Bulk Actions -->
        <div class="bulk-actions" id="bulkActions">
            <div class="d-flex align-items-center">
                <span class="me-3">
                    <strong id="selectedCount">0</strong> {% trans "عنصر محدد" %}
                </span>
                <button class="btn btn-danger btn-sm me-2" onclick="bulkDelete()">
                    <i class="fas fa-trash me-1"></i>
                    {% trans "حذف المحدد" %}
                </button>
                <button class="btn btn-info btn-sm" onclick="bulkExport()">
                    <i class="fas fa-download me-1"></i>
                    {% trans "تصدير المحدد" %}
                </button>
            </div>
        </div>

        <!-- Table -->
        <div class="table-responsive">
            <table class="table table-hover" id="clientsTable">
                <thead class="table-light">
                    <tr>
                        <th width="40">
                            <input type="checkbox" id="selectAll" class="form-check-input">
                        </th>
                        <th>{% trans "العميل" %}</th>
                        <th>{% trans "معلومات الاتصال" %}</th>
                        <th>{% trans "النوع" %}</th>
                        <th>{% trans "الحالة" %}</th>
                        <th>{% trans "تاريخ الإنشاء" %}</th>
                        <th width="150">{% trans "الإجراءات" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for client in clients %}
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input client-checkbox" value="{{ client.pk }}">
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="client-avatar me-3">
                                    {{ client.first_name_ar|first }}{{ client.last_name_ar|first }}
                                </div>
                                <div>
                                    <div class="fw-bold">{{ client.full_name_ar }}</div>
                                    <small class="text-muted">{{ client.client_code }}</small>
                                    {% if client.full_name_fr %}
                                        <br><small class="text-muted">{{ client.full_name_fr }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            <div>
                                {% if client.email %}
                                    <div><i class="fas fa-envelope me-1"></i> {{ client.email }}</div>
                                {% endif %}
                                {% if client.phone %}
                                    <div><i class="fas fa-phone me-1"></i> {{ client.phone }}</div>
                                {% endif %}
                                {% if client.whatsapp %}
                                    <div><i class="fab fa-whatsapp me-1"></i> {{ client.whatsapp }}</div>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <span class="client-type-badge client-type-{{ client.client_type }}">
                                {{ client.get_client_type_display }}
                            </span>
                        </td>
                        <td>
                            {% if client.vip_status %}
                                <span class="vip-badge">
                                    <i class="fas fa-crown me-1"></i>
                                    {% trans "مميز" %}
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">{% trans "عادي" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div>{{ client.created_at|date:"Y-m-d" }}</div>
                            <small class="text-muted">{{ client.created_at|date:"H:i" }}</small>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'crm:client_detail' client.pk %}"
                                   class="btn btn-outline-primary btn-action btn-sm"
                                   title="{% trans 'عرض التفاصيل' %}">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'crm:client_edit' client.pk %}"
                                   class="btn btn-outline-warning btn-action btn-sm"
                                   title="{% trans 'تعديل' %}">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button"
                                        class="btn btn-outline-danger btn-action btn-sm"
                                        onclick="deleteClient({{ client.pk }}, '{{ client.full_name_ar|escapejs }}')"
                                        title="{% trans 'حذف' %}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center py-5">
                            <div class="text-muted">
                                <i class="fas fa-users fa-3x mb-3"></i>
                                <h5>{% trans "لا توجد عملاء" %}</h5>
                                <p>{% trans "لم يتم العثور على أي عملاء مطابقين لمعايير البحث" %}</p>
                                <a href="{% url 'crm:client_add' %}" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>
                                    {% trans "إضافة عميل جديد" %}
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="{% trans 'تنقل الصفحات' %}" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>

<!-- Floating Add Button -->
<button class="floating-add-btn" onclick="window.location.href='{% url 'crm:client_add' %}'" title="{% trans 'إضافة عميل جديد' %}">
    <i class="fas fa-plus"></i>
</button>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select all checkbox functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const clientCheckboxes = document.querySelectorAll('.client-checkbox');
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');

    // Bind event listeners
    function bindEventListeners() {
        // Select all checkbox
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                clientCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateBulkActions();
            });
        }

        // Individual checkboxes
        clientCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateBulkActions);
        });
    }

    // Update bulk actions visibility
    function updateBulkActions() {
        const selectedCheckboxes = document.querySelectorAll('.client-checkbox:checked');

        if (selectedCheckboxes.length > 0) {
            bulkActions.classList.add('show');
            selectedCount.textContent = selectedCheckboxes.length;
        } else {
            bulkActions.classList.remove('show');
        }

        // Update select all checkbox state
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = selectedCheckboxes.length === clientCheckboxes.length;
            selectAllCheckbox.indeterminate = selectedCheckboxes.length > 0 && selectedCheckboxes.length < clientCheckboxes.length;
        }
    }

    // Toggle bulk actions
    window.toggleBulkActions = function() {
        if (bulkActions.classList.contains('show')) {
            // Hide bulk actions and uncheck all
            bulkActions.classList.remove('show');
            document.querySelectorAll('.client-checkbox, #selectAll').forEach(checkbox => {
                checkbox.checked = false;
                checkbox.indeterminate = false;
            });
        } else {
            // Show bulk actions
            bulkActions.classList.add('show');
        }
    };

    // Reset search form
    window.resetSearch = function() {
        document.getElementById('searchForm').reset();
        window.location.href = window.location.pathname;
    };

    // Delete single client
    window.deleteClient = function(clientId, clientName) {
        if (confirm(`هل أنت متأكد من حذف العميل "${clientName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            // Show loading state
            const deleteBtn = event.target.closest('button');
            const originalContent = deleteBtn.innerHTML;
            deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            deleteBtn.disabled = true;

            fetch(`/crm/clients/${clientId}/delete/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCsrfToken(),
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                    // Remove the row from table
                    deleteBtn.closest('tr').remove();
                    // Update statistics if needed
                    updateStatistics();
                } else {
                    showAlert('danger', data.message || 'حدث خطأ أثناء حذف العميل');
                    // Restore button
                    deleteBtn.innerHTML = originalContent;
                    deleteBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('danger', 'حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى');
                // Restore button
                deleteBtn.innerHTML = originalContent;
                deleteBtn.disabled = false;
            });
        }
    };

    // Bulk delete
    window.bulkDelete = function() {
        const selectedCheckboxes = document.querySelectorAll('.client-checkbox:checked');
        const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);

        if (selectedIds.length === 0) {
            showAlert('warning', 'يرجى تحديد عملاء للحذف');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف ${selectedIds.length} عميل؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            showAlert('info', 'جاري تنفيذ العملية...');

            // Send bulk delete request
            fetch('/crm/clients/bulk-delete/', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCsrfToken(),
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ ids: selectedIds })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', `تم حذف ${data.deleted_count} عميل بنجاح`);
                    // Reload page after delay
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showAlert('danger', data.message || 'حدث خطأ أثناء حذف العملاء');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('danger', 'حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى');
            });
        }
    };

    // Bulk export
    window.bulkExport = function() {
        const selectedCheckboxes = document.querySelectorAll('.client-checkbox:checked');
        const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);

        if (selectedIds.length === 0) {
            showAlert('warning', 'يرجى تحديد عملاء للتصدير');
            return;
        }

        // Create export URL with selected IDs
        const exportUrl = new URL('{% url "crm:client_export" %}', window.location.origin);
        exportUrl.searchParams.append('ids', selectedIds.join(','));

        // Show loading message
        showAlert('info', 'جاري تحضير ملف التصدير...');

        // Open export URL
        window.open(exportUrl.toString(), '_blank');
    };

    // Get CSRF token
    function getCsrfToken() {
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
        return csrfToken ? csrfToken.value : '';
    }

    // Show alert message
    function showAlert(type, message) {
        // Remove existing alerts
        document.querySelectorAll('.alert').forEach(alert => alert.remove());

        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 1050; min-width: 300px;">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // Insert alert
        document.body.insertAdjacentHTML('beforeend', alertHtml);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            const alert = document.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    // Update statistics after operations
    function updateStatistics() {
        // This would typically fetch updated stats from the server
        // For now, we'll just reload the page
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }

    // Initialize event listeners
    bindEventListeners();

    // Search form enhancements
    const searchForm = document.getElementById('searchForm');
    if (searchForm) {
        // Auto-submit on select changes
        searchForm.querySelectorAll('select').forEach(select => {
            select.addEventListener('change', function() {
                if (this.value !== '') {
                    searchForm.submit();
                }
            });
        });

        // Enter key submission for search input
        const searchInput = searchForm.querySelector('input[type="text"]');
        if (searchInput) {
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchForm.submit();
                }
            });
        }
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+A or Cmd+A to select all
        if ((e.ctrlKey || e.metaKey) && e.key === 'a' && !e.target.matches('input, textarea')) {
            e.preventDefault();
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = !selectAllCheckbox.checked;
                selectAllCheckbox.dispatchEvent(new Event('change'));
            }
        }

        // Delete key to delete selected
        if (e.key === 'Delete' && !e.target.matches('input, textarea')) {
            const selectedCheckboxes = document.querySelectorAll('.client-checkbox:checked');
            if (selectedCheckboxes.length > 0) {
                bulkDelete();
            }
        }

        // Escape to clear selection
        if (e.key === 'Escape') {
            if (bulkActions.classList.contains('show')) {
                toggleBulkActions();
            }
        }
    });
});
</script>
{% endblock %}
