"""
Admin configuration for core models.
"""
from django.contrib import admin
from .models import Country, City, Currency, SystemSettings


@admin.register(Country)
class CountryAdmin(admin.ModelAdmin):
    list_display = ('name_ar', 'name_fr', 'code', 'phone_code', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name_ar', 'name_fr', 'name_en', 'code')
    ordering = ('name_ar',)


@admin.register(City)
class CityAdmin(admin.ModelAdmin):
    list_display = ('name_ar', 'name_fr', 'country', 'is_active')
    list_filter = ('country', 'is_active')
    search_fields = ('name_ar', 'name_fr', 'name_en')
    ordering = ('country__name_ar', 'name_ar')


@admin.register(Currency)
class CurrencyAdmin(admin.ModelAdmin):
    list_display = ('name_ar', 'code', 'symbol', 'exchange_rate', 'is_base', 'is_active')
    list_filter = ('is_base', 'is_active')
    search_fields = ('name_ar', 'name_fr', 'code')
    ordering = ('name_ar',)


@admin.register(SystemSettings)
class SystemSettingsAdmin(admin.ModelAdmin):
    list_display = ('key', 'value', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('key', 'description')
    ordering = ('key',)
