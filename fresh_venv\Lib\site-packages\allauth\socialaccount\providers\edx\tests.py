from allauth.socialaccount.tests import OAuth2TestsMixin
from allauth.tests import MockedResponse, TestCase

from .provider import EdxProvider


class EdxTests(OAuth2TestsMixin, TestCase):
    provider_id = EdxProvider.id

    def get_mocked_response(self):
        print(self)
        return MockedResponse(
            200,
            """{
"username":"k<PERSON><PERSON><PERSON><PERSON><PERSON>",
"bio":null,
"requires_parental_consent":true,
"language_proficiencies":[

],
"name":"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
"country":null,
"social_links":[

],
"is_active":true,
"profile_image":{
  "image_url_small":"http://draft.navoica.pl/static/images/profiles/default_30.png",
  "image_url_full":"http://draft.navoica.pl/static/images/profiles/default_500.png",
  "image_url_large":"http://draft.navoica.pl/static/images/profiles/default_120.png",
  "image_url_medium":"http://draft.navoica.pl/static/images/profiles/default_50.png",
  "has_image":false
},
"extended_profile":[

],
"year_of_birth":null,
"level_of_education":null,
"goals":"",
"accomplishments_shared":false,
"gender":null,
"date_joined":"2019-09-21T07:48:31Z",
"mailing_address":"",
"email":"<EMAIL>",
"account_privacy":"private"
}""",
        )
