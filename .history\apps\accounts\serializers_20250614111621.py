"""
Serializers for accounts app models.
"""
from rest_framework import serializers
from django.contrib.auth.models import Permission
from django.contrib.auth import get_user_model, authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from apps.accounts.models import Role, UserSession

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    """
    Serializer for User model.
    """
    class Meta:
        """Meta class for UserSerializer configuration."""
        model = User
        fields = [
            'id', 'username', 'email', 'first_name',
            'last_name', 'is_active', 'is_staff'
        ]
        read_only_fields = ['id']


class RoleSerializer(serializers.ModelSerializer):
    """
    Serializer for Role model.
    """
    class Meta:
        """Meta class for RoleSerializer configuration."""
        model = Role
        fields = ['id', 'name', 'description', 'permissions']
        read_only_fields = ['id']


class PermissionSerializer(serializers.ModelSerializer):
    """
    Serializer for Permission model.
    """
    class Meta:
        """Meta class for PermissionSerializer configuration."""
        model = Permission
        fields = ['id', 'name', 'codename', 'content_type']
        read_only_fields = ['id']


class UserSessionSerializer(serializers.ModelSerializer):
    """
    Serializer for UserSession model.
    """
    class Meta:
        model = UserSession
        fields = ['id', 'user', 'session_key', 'ip_address',
                 'user_agent', 'login_time', 'last_activity',
                 'is_active', 'logout_time']
        read_only_fields = ['id', 'login_time', 'last_activity']


class LoginSerializer(TokenObtainPairSerializer):
    """Custom login serializer with additional validation."""
    def validate(self, attrs):
        data = super().validate(attrs)

        # Add extra data to response
        data['user_id'] = self.user.id
        data['username'] = self.user.username
        data['email'] = self.user.email
        data['first_name'] = self.user.first_name
        data['last_name'] = self.user.last_name
        data['is_staff'] = self.user.is_staff

        return data


class ProfileSerializer(serializers.ModelSerializer):
    """Serializer for user profile."""
    current_password = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'current_password', 'password', 'is_active', 'is_staff',
            'date_joined', 'last_login'
        ]
        read_only_fields = ['id', 'is_active', 'is_staff',
                           'date_joined', 'last_login']
        extra_kwargs = {
            'password': {'write_only': True, 'required': False}
        }

    def validate(self, attrs):
        """Validate the profile update data."""
        if 'password' in attrs and not attrs.get('current_password'):
            raise serializers.ValidationError({
                'current_password': _('Current password is required to set a new password.')
            })

        if ('password' in attrs and
            not self.instance.check_password(attrs['current_password'])):
            raise serializers.ValidationError({
                'current_password': _('Current password is incorrect.')
            })

        return attrs

    def update(self, instance, validated_data):
        """Update user profile."""
        validated_data.pop('current_password', None)
        password = validated_data.pop('password', None)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        if password:
            instance.set_password(password)

        instance.save()
        return instance


class PasswordChangeSerializer(serializers.Serializer):
    """Serializer for password change endpoint."""
    current_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)

    def validate_current_password(self, value):
        """Validate current password."""
        if not self.context['request'].user.check_password(value):
            raise serializers.ValidationError(_('Current password is incorrect.'))
        return value

    def validate_new_password(self, value):
        """Validate new password."""
        try:
            validate_password(value, self.context['request'].user)
        except ValidationError as e:
            raise serializers.ValidationError(str(e))
        return value

    def save(self):
        """Save new password."""
        user = self.context['request'].user
        user.set_password(self.validated_data['new_password'])
        user.save()


class PasswordResetSerializer(serializers.Serializer):
    """Serializer for password reset endpoint."""
    email = serializers.EmailField(required=True)

    def validate_email(self, value):
        """Validate that user exists with this email."""
        try:
            self.user = User.objects.get(email=value)
        except User.DoesNotExist:
            raise serializers.ValidationError(_('No user found with this email address.'))
        return value

    def save(self):
        """Send password reset email."""
        # TODO: Implement password reset email sending
        pass


class PasswordResetConfirmSerializer(serializers.Serializer):
    """Serializer for password reset confirmation endpoint."""
    token = serializers.CharField(required=True)
    uid = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)

    def validate_new_password(self, value):
        """Validate new password."""
        try:
            validate_password(value)
        except ValidationError as e:
            raise serializers.ValidationError(str(e))
        return value

    def validate(self, attrs):
        """Validate reset token."""
        # TODO: Implement token validation
        return attrs

    def save(self):
        """Reset user password."""
        # TODO: Implement password reset
        pass
