"""
Management command to populate the system with comprehensive demo data.
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from django.core.management import call_command


class Command(BaseCommand):
    help = 'Populate the system with comprehensive demo data'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 بدء تحميل البيانات التجريبية الشاملة...'))

        with transaction.atomic():
            # Load initial data
            self.stdout.write('📊 تحميل البيانات الأساسية...')
            call_command('load_initial_data')

            # Create sample clients
            self.stdout.write('👥 إنشاء عملاء تجريبيين...')
            call_command('create_sample_clients')

            # Create sample packages
            self.stdout.write('🏖️ إنشاء باقات سياحية تجريبية...')
            call_command('create_simple_packages')

            # Create additional demo data
            self.create_additional_demo_data()

        self.stdout.write(self.style.SUCCESS('✅ تم تحميل جميع البيانات التجريبية بنجاح!'))
        self.print_summary()

    def create_additional_demo_data(self):
        """Create additional demo data."""
        from apps.crm.models import Client
        from apps.tours.models import TourPackage, Destination
        from apps.core.models import Country
        from decimal import Decimal
        import random

        # Create more clients
        morocco = Country.objects.get(code='MA')
        france = Country.objects.get(code='FR')

        additional_clients = [
            {
                'first_name_ar': 'خديجة',
                'last_name_ar': 'العلوي',
                'first_name_fr': 'Khadija',
                'last_name_fr': 'Alaoui',
                'email': '<EMAIL>',
                'phone': '+212665123456',
                'gender': 'female',
                'nationality': morocco,
                'client_type': 'individual',
                'preferred_language': 'ar',
                'loyalty_points': random.randint(50, 500),
                'vip_status': random.choice([True, False]),
                'marketing_consent': True,
                'notes': 'عميلة مهتمة بالسياحة الثقافية والتراثية'
            },
            {
                'first_name_ar': 'يوسف',
                'last_name_ar': 'بنعلي',
                'first_name_fr': 'Youssef',
                'last_name_fr': 'Benali',
                'email': '<EMAIL>',
                'phone': '+212666789012',
                'gender': 'male',
                'nationality': morocco,
                'client_type': 'individual',
                'preferred_language': 'ar',
                'loyalty_points': random.randint(50, 500),
                'vip_status': random.choice([True, False]),
                'marketing_consent': True,
                'notes': 'عميل يفضل رحلات المغامرات والأنشطة الخارجية'
            },
            {
                'first_name_ar': 'بيير',
                'last_name_ar': 'مارتن',
                'first_name_fr': 'Pierre',
                'last_name_fr': 'Martin',
                'email': '<EMAIL>',
                'phone': '+33987654321',
                'gender': 'male',
                'nationality': france,
                'client_type': 'individual',
                'preferred_language': 'fr',
                'loyalty_points': random.randint(50, 500),
                'vip_status': random.choice([True, False]),
                'marketing_consent': True,
                'notes': 'Client français intéressé par les circuits culturels'
            }
        ]

        for client_data in additional_clients:
            client_data['client_code'] = self.generate_client_code()
            client, created = Client.objects.get_or_create(
                email=client_data['email'],
                defaults=client_data
            )
            if created:
                self.stdout.write(f'  ✅ تم إنشاء عميل: {client.full_name_ar}')

        # Create more destinations
        destinations_data = [
            {
                'name_ar': 'الرباط العاصمة',
                'name_fr': 'Rabat la Capitale',
                'name_en': 'Rabat the Capital',
                'description_ar': 'العاصمة الإدارية للمغرب مع معالمها التاريخية والحديثة',
                'description_fr': 'La capitale administrative du Maroc avec ses monuments historiques et modernes',
                'description_en': 'The administrative capital of Morocco with its historical and modern landmarks',
                'country': morocco,
                'is_featured': False
            },
            {
                'name_ar': 'أكادير الشاطئية',
                'name_fr': 'Agadir Balnéaire',
                'name_en': 'Agadir Beach',
                'description_ar': 'مدينة ساحلية جميلة مع شواطئ رملية ذهبية ومنتجعات فاخرة',
                'description_fr': 'Belle ville côtière avec des plages de sable doré et des complexes de luxe',
                'description_en': 'Beautiful coastal city with golden sandy beaches and luxury resorts',
                'country': morocco,
                'is_featured': True
            }
        ]

        for dest_data in destinations_data:
            destination, created = Destination.objects.get_or_create(
                name_ar=dest_data['name_ar'],
                defaults=dest_data
            )
            if created:
                self.stdout.write(f'  ✅ تم إنشاء وجهة: {destination.name_ar}')

    def generate_client_code(self):
        """Generate unique client code."""
        import string
        import random
        from datetime import date

        year = date.today().year
        random_num = random.randint(1, 999)
        return f"CL{year}{random_num:03d}"

    def print_summary(self):
        """Print summary of created data."""
        from apps.crm.models import Client
        from apps.tours.models import TourPackage, Destination, TourCategory
        from apps.core.models import Country, City
        from apps.accounts.models import User

        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.SUCCESS('📊 ملخص البيانات المُحملة:'))
        self.stdout.write('='*50)

        self.stdout.write(f'🌍 الدول: {Country.objects.count()}')
        self.stdout.write(f'🏙️ المدن: {City.objects.count()}')
        self.stdout.write(f'🎯 الوجهات السياحية: {Destination.objects.count()}')
        self.stdout.write(f'📂 فئات الباقات: {TourCategory.objects.count()}')
        self.stdout.write(f'🏖️ الباقات السياحية: {TourPackage.objects.count()}')
        self.stdout.write(f'👥 العملاء: {Client.objects.count()}')
        self.stdout.write(f'👤 المستخدمين: {User.objects.count()}')

        # VIP clients
        vip_count = Client.objects.filter(vip_status=True).count()
        self.stdout.write(f'⭐ عملاء VIP: {vip_count}')

        # Featured packages
        featured_count = TourPackage.objects.filter(is_featured=True).count()
        self.stdout.write(f'🌟 باقات مميزة: {featured_count}')

        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.SUCCESS('🎉 النظام جاهز للاستخدام!'))
        self.stdout.write('='*50)

        self.stdout.write('\n📱 الروابط المهمة:')
        self.stdout.write('🏠 الصفحة الرئيسية: http://127.0.0.1:8000/')
        self.stdout.write('⚙️ لوحة الإدارة: http://127.0.0.1:8000/admin/')
        self.stdout.write('📊 الإحصائيات: http://127.0.0.1:8000/stats/')

        self.stdout.write('\n🔐 بيانات الدخول:')
        self.stdout.write('👤 المدير: admin / admin123')
        self.stdout.write('👤 مدير الوكالة: manager / password123')
        self.stdout.write('👤 موظف مبيعات: sales1 / password123')
        self.stdout.write('👤 محاسب: accountant / password123')
