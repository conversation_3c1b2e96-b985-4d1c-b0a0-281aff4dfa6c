from allauth.socialaccount.tests import OAuth2TestsMixin
from allauth.tests import MockedResponse, TestCase

from .provider import OktaProvider


class OktaTests(OAuth2TestsMixin, TestCase):
    provider_id = OktaProvider.id

    def get_mocked_response(self):
        return MockedResponse(
            200,
            """
            {
                "sub": "00u33ow83pjQpCQJr1j8",
                "name": "<PERSON>",
                "locale": "AE",
                "email": "<EMAIL>",
                "nickname": "<PERSON>",
                "preferred_username": "<EMAIL>",
                "given_name": "<PERSON>",
                "family_name": "<PERSON>",
                "zoneinfo": "America/Los_Angeles",
                "updated_at": **********,
                "email_verified": true
            }
        """,
        )
