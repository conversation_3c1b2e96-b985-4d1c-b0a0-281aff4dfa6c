/* Offline mode styles */
body.offline-mode {
    position: relative;
}

body.offline-mode::before {
    content: 'وضع عدم الاتصال';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: #ff9800;
    color: white;
    text-align: center;
    padding: 0.5rem;
    z-index: 1050;
    font-weight: bold;
}

body.offline-mode form[data-offline-form] {
    position: relative;
}

body.offline-mode form[data-offline-form]::after {
    content: 'سيتم حفظ النموذج ومزامنته عند استعادة الاتصال';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 152, 0, 0.1);
    color: #ff9800;
    text-align: center;
    padding: 0.5rem;
    border: 1px solid #ff9800;
    border-radius: 0.25rem;
    margin: 1rem 0;
}

/* Form validation styles */
.needs-validation .form-control:invalid,
.needs-validation .form-select:invalid {
    border-color: #dc3545;
}

.needs-validation .form-control:invalid:focus,
.needs-validation .form-select:invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.needs-validation .form-control:valid,
.needs-validation .form-select:valid {
    border-color: #28a745;
}

.needs-validation .form-control:valid:focus,
.needs-validation .form-select:valid:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.needs-validation .invalid-feedback {
    display: none;
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.needs-validation .was-validated .form-control:invalid ~ .invalid-feedback,
.needs-validation .was-validated .form-select:invalid ~ .invalid-feedback {
    display: block;
}

/* Phone number input mask styles */
.phone-input {
    direction: ltr;
    text-align: left;
}

.phone-input::placeholder {
    direction: rtl;
    text-align: right;
}

/* Tab styles */
.nav-tabs {
    border-bottom: 2px solid #dee2e6;
    margin-bottom: 1rem;
}

.nav-tabs .nav-link {
    border: none;
    border-bottom: 2px solid transparent;
    color: #6c757d;
    padding: 0.75rem 1.25rem;
    font-weight: 500;
    margin-bottom: -2px;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: #495057;
}

.nav-tabs .nav-link.active {
    color: #0d6efd;
    border-bottom-color: #0d6efd;
    background-color: transparent;
}

/* Form sections styles */
.form-section {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-section-title {
    color: #495057;
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.form-section-title i {
    margin-left: 0.5rem;
    color: #0d6efd;
}

/* Form actions styles */
.form-actions {
    position: sticky;
    bottom: 0;
    background-color: white;
    padding: 1rem;
    border-top: 1px solid #dee2e6;
    margin-top: 2rem;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .nav-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        overflow-y: hidden;
        -webkit-overflow-scrolling: touch;
        -ms-overflow-style: -ms-autohiding-scrollbar;
    }

    .nav-tabs::-webkit-scrollbar {
        display: none;
    }

    .nav-tabs .nav-link {
        white-space: nowrap;
    }

    .form-section {
        padding: 1rem;
    }
}
