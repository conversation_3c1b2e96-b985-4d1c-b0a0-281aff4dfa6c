"""Enhanced user and authentication models for the Moroccan ERP system."""

from typing import cast
from django.contrib.auth.models import AbstractUser, Permission
from django.db import models
from django.utils.translation import gettext_lazy as _
from phonenumber_field.modelfields import PhoneNumberField
from apps.core.models import TimeStampedModel
from django.db.models.fields.related_descriptors import (
    ReverseManyToOneDescriptor,
)


class Role(TimeStampedModel):
    """Role model for user permissions."""

    name = models.CharField(_('اسم الدور'), max_length=50, unique=True)
    description = models.TextField(_('الوصف'), blank=True)
    permissions = models.ManyToManyField(
        Permission,
        verbose_name=_('الصلاحيات'),
        blank=True,
        related_name='roles'
    )
    is_active = models.BooleanField(_('نشط'), default=True)

    class Meta:
        verbose_name = _('دور')
        verbose_name_plural = _('الأدوار')
        ordering = ['name']

    def __str__(self) -> str:
        return self.name


class User(AbstractUser):
    """Custom user model with additional fields for travel agency staff."""

    # System Roles
    SYSTEM_ROLE_CHOICES = [
        ('admin', cast(str, _('مدير النظام'))),
        ('manager', cast(str, _('مدير الوكالة'))),
        ('sales', cast(str, _('موظف مبيعات'))),
        ('accountant', cast(str, _('محاسب'))),
        ('guide', cast(str, _('مرشد سياحي'))),
        ('driver', cast(str, _('سائق'))),
        ('receptionist', cast(str, _('موظف استقبال'))),
    ]

    # Personal Information
    first_name_ar: models.CharField = models.CharField(
        _('الاسم الأول بالعربية'),
        max_length=50,
        blank=True
    )
    last_name_ar: models.CharField = models.CharField(
        _('اسم العائلة بالعربية'),
        max_length=50,
        blank=True
    )
    phone: PhoneNumberField = PhoneNumberField(
        _('رقم الهاتف'),
        blank=True
    )
    avatar: models.ImageField = models.ImageField(
        _('الصورة الشخصية'),
        upload_to='avatars/',
        blank=True
    )

    # Work Information
    roles = models.ManyToManyField(
        Role,
        verbose_name=_('الأدوار'),
        blank=True,
        related_name='users'
    )
    system_role: models.CharField = models.CharField(
        _('دور النظام'),
        max_length=20,
        choices=SYSTEM_ROLE_CHOICES,
        default='sales'
    )
    employee_id: models.CharField = models.CharField(
        _('رقم الموظف'),
        max_length=20,
        unique=True,
        null=True,
        blank=True
    )
    department: models.CharField = models.CharField(
        _('القسم'),
        max_length=50,
        blank=True
    )
    hire_date: models.DateField = models.DateField(
        _('تاريخ التوظيف'),
        null=True,
        blank=True
    )
    salary: models.DecimalField = models.DecimalField(
        _('الراتب'),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )

    # Contact Information
    emergency_contact_name: models.CharField = models.CharField(
        _('اسم جهة الاتصال الطارئ'),
        max_length=100,
        blank=True
    )
    emergency_contact_phone: PhoneNumberField = PhoneNumberField(
        _('هاتف جهة الاتصال الطارئ'),
        blank=True
    )

    # Preferences
    preferred_language: models.CharField = models.CharField(
        _('اللغة المفضلة'),
        max_length=5,
        choices=[('ar', 'العربية'), ('fr', 'Français'), ('en', 'English')],
        default='ar'
    )

    # Related managers for type checking
    reservations: ReverseManyToOneDescriptor
    reservationdocuments: ReverseManyToOneDescriptor
    invoices: ReverseManyToOneDescriptor
    payments: ReverseManyToOneDescriptor
    submitted_expenses: ReverseManyToOneDescriptor
    approved_expenses: ReverseManyToOneDescriptor
    managed_departments: ReverseManyToOneDescriptor
    leaves: ReverseManyToOneDescriptor
    approved_leaves: ReverseManyToOneDescriptor
    attendances: ReverseManyToOneDescriptor
    recorded_attendances: ReverseManyToOneDescriptor
    payrolls: ReverseManyToOneDescriptor
    approved_payrolls: ReverseManyToOneDescriptor
    supplier_contracts: ReverseManyToOneDescriptor
    approved_contracts: ReverseManyToOneDescriptor
    supplier_evaluations: ReverseManyToOneDescriptor

    class Meta:
        verbose_name = _('مستخدم')
        verbose_name_plural = _('المستخدمون')

    def __str__(self) -> str:
        return (
            f"{self.first_name_ar} {self.last_name_ar}"
            if self.first_name_ar
            else self.username
        )

    @property
    def full_name_ar(self) -> str:
        """Return full name in Arabic."""
        return f"{self.first_name_ar} {self.last_name_ar}".strip()

    def get_system_role_display_ar(self) -> str:
        """Get system role display in Arabic."""
        role_dict = dict(self.SYSTEM_ROLE_CHOICES)
        return role_dict.get(self.system_role, self.system_role)


class UserSession(TimeStampedModel):
    """Track user sessions for security monitoring."""

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        verbose_name=_('المستخدم')
    )
    session_key = models.CharField(
        _('مفتاح الجلسة'),
        max_length=40,
        unique=True
    )
    ip_address = models.GenericIPAddressField(_('عنوان IP'))
    user_agent = models.TextField(_('معلومات المتصفح'))
    login_time = models.DateTimeField(
        _('وقت تسجيل الدخول'),
        auto_now_add=True
    )
    last_activity = models.DateTimeField(_('آخر نشاط'), auto_now=True)
    is_active = models.BooleanField(_('نشط'), default=True)
    logout_time = models.DateTimeField(
        _('وقت تسجيل الخروج'),
        null=True,
        blank=True
    )

    class Meta:
        verbose_name = _('جلسة مستخدم')
        verbose_name_plural = _('جلسات المستخدمين')
        ordering = ['-login_time']

    def __str__(self) -> str:
        if hasattr(self.user, 'username'):
            return f"{self.user.username} - {self.ip_address}"
        return f"Session {self.session_key}"


class ActivityLog(TimeStampedModel):
    """Log user activities for audit trail."""

    ACTION_CHOICES = [
        ('login', _('تسجيل دخول')),
        ('logout', _('تسجيل خروج')),
        ('create', _('إنشاء')),
        ('update', _('تحديث')),
        ('delete', _('حذف')),
        ('view', _('عرض')),
        ('export', _('تصدير')),
        ('import', _('استيراد')),
        ('backup', _('نسخ احتياطي')),
        ('restore', _('استعادة')),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('المستخدم')
    )
    action = models.CharField(
        _('الإجراء'),
        max_length=20,
        choices=ACTION_CHOICES
    )
    content_type = models.CharField(
        _('نوع المحتوى'),
        max_length=100,
        blank=True
    )
    object_id = models.CharField(
        _('معرف الكائن'),
        max_length=100,
        blank=True
    )
    object_repr = models.CharField(
        _('تمثيل الكائن'),
        max_length=200,
        blank=True
    )
    changes = models.JSONField(
        _('التغييرات'),
        default=dict,
        blank=True
    )
    ip_address = models.GenericIPAddressField(
        _('عنوان IP'),
        null=True,
        blank=True
    )
    user_agent = models.TextField(
        _('معلومات المتصفح'),
        blank=True
    )
    session_key = models.CharField(
        _('مفتاح الجلسة'),
        max_length=40,
        blank=True
    )

    class Meta:
        verbose_name = _('سجل النشاط')
        verbose_name_plural = _('سجلات النشاط')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'action']),
            models.Index(fields=['created_at']),
            models.Index(fields=['content_type', 'object_id']),
        ]

    def __str__(self) -> str:
        action_dict = dict(self.ACTION_CHOICES)
        action_display = action_dict.get(self.action, self.action)
        return (
            f"{self.user} - {action_display} - "
            f"{self.created_at}"
        )


class UserPermissionGroup(TimeStampedModel):
    """Custom permission groups for role-based access control."""

    name = models.CharField(
        _('اسم المجموعة'),
        max_length=100,
        unique=True
    )
    description = models.TextField(_('الوصف'), blank=True)
    permissions = models.ManyToManyField(
        Permission,
        verbose_name=_('الصلاحيات'),
        blank=True
    )
    is_active = models.BooleanField(_('نشط'), default=True)

    class Meta:
        verbose_name = _('مجموعة صلاحيات')
        verbose_name_plural = _('مجموعات الصلاحيات')
        ordering = ['name']

    def __str__(self) -> str:
        return self.name
