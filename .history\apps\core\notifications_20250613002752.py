"""
Notification system for the Moroccan Travel Agency ERP.
"""
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import timezone
from datetime import datetime, timedelta
import logging

User = get_user_model()
logger = logging.getLogger(__name__)


class NotificationManager:
    """
    Central notification manager for the system.
    """

    @staticmethod
    def send_invoice_notification(invoice, notification_type='created'):
        """
        Send invoice-related notifications.
        """
        try:
            if notification_type == 'created':
                return NotificationManager._send_invoice_created(invoice)
            elif notification_type == 'sent':
                return NotificationManager._send_invoice_sent(invoice)
            elif notification_type == 'overdue':
                return NotificationManager._send_invoice_overdue(invoice)
            elif notification_type == 'paid':
                return NotificationManager._send_invoice_paid(invoice)
        except Exception as e:
            logger.error(f"Error sending invoice notification: {e}")
            return False

    @staticmethod
    def _send_invoice_created(invoice):
        """Send notification when invoice is created."""
        subject = f'تم إنشاء فاتورة جديدة - {invoice.invoice_number}'

        # Notify finance team
        finance_users = User.objects.filter(
            groups__name='Finance',
            is_active=True
        )

        for user in finance_users:
            context = {
                'user': user,
                'invoice': invoice,
                'notification_type': 'created'
            }

            message = f'تم إنشاء فاتورة جديدة {invoice.invoice_number} للعميل {invoice.client.full_name_ar}'

            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
                fail_silently=True
            )

        return True

    @staticmethod
    def send_system_alerts():
        """
        Send system-wide alerts and reminders.
        """
        try:
            # Check for overdue invoices
            NotificationManager._check_overdue_invoices()

            # Check for upcoming departures
            NotificationManager._check_upcoming_departures()

            return True
        except Exception as e:
            logger.error(f"Error sending system alerts: {e}")
            return False

    @staticmethod
    def _check_overdue_invoices():
        """Check and notify about overdue invoices."""
        from apps.finance.models import Invoice

        today = timezone.now().date()
        overdue_invoices = Invoice.objects.filter(
            due_date__lt=today,
            status__in=['draft', 'sent']
        )

        if overdue_invoices.exists():
            # Notify finance team
            finance_users = User.objects.filter(is_staff=True, is_active=True)

            for user in finance_users:
                subject = 'تنبيه: فواتير متأخرة'
                message = f'يوجد {overdue_invoices.count()} فاتورة متأخرة تحتاج متابعة'

                send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[user.email],
                    fail_silently=True
                )

    @staticmethod
    def _check_upcoming_departures():
        """Check and notify about upcoming departures."""
        from apps.reservations.models import Reservation

        tomorrow = timezone.now().date() + timedelta(days=1)

        # Departures tomorrow
        tomorrow_departures = Reservation.objects.filter(
            departure_date=tomorrow,
            status='confirmed'
        )

        if tomorrow_departures.exists():
            # Notify operations team
            staff_users = User.objects.filter(is_staff=True, is_active=True)

            for user in staff_users:
                subject = 'تذكير: رحلات غداً'
                message = f'يوجد {tomorrow_departures.count()} رحلة مجدولة غداً'

                send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[user.email],
                    fail_silently=True
                )
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import timezone
from datetime import datetime, timedelta

User = get_user_model()


class NotificationManager:
    """Manage system notifications."""

    @staticmethod
    def send_welcome_email(user):
        """Send welcome email to new user."""
        subject = "مرحباً بك في نظام إدارة وكالة السفر المغربية"
        message = f"""
        مرحباً {user.full_name_ar or user.username}،

        تم إنشاء حسابك بنجاح في نظام إدارة وكالة السفر المغربية.

        بيانات الدخول:
        اسم المستخدم: {user.username}
        الدور: {user.get_role_display()}

        يمكنك الآن تسجيل الدخول والبدء في استخدام النظام.

        رابط النظام: http://127.0.0.1:8000/admin/

        مع تحيات فريق العمل
        """

        try:
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
                fail_silently=False,
            )
            return True
        except Exception as e:
            print(f"Failed to send welcome email: {e}")
            return False

    @staticmethod
    def send_client_registration_notification(client):
        """Send notification when new client registers."""
        # Notify sales team
        sales_users = User.objects.filter(role='sales', is_active=True)

        subject = f"عميل جديد: {client.full_name_ar}"
        message = f"""
        تم تسجيل عميل جديد في النظام:

        الاسم: {client.full_name_ar}
        النوع: {client.get_client_type_display()}
        البريد الإلكتروني: {client.email}
        الهاتف: {client.phone}
        الجنسية: {client.nationality.name_ar if client.nationality else 'غير محدد'}

        يرجى المتابعة مع العميل في أقرب وقت ممكن.

        رابط ملف العميل: http://127.0.0.1:8000/admin/crm/client/{client.id}/change/
        """

        for user in sales_users:
            try:
                send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[user.email],
                    fail_silently=True,
                )
            except Exception as e:
                print(f"Failed to send client notification to {user.email}: {e}")

    @staticmethod
    def send_package_inquiry_notification(client, package):
        """Send notification when client inquires about package."""
        # Notify assigned agent or sales team
        if hasattr(client, 'assigned_agent') and client.assigned_agent:
            recipients = [client.assigned_agent]
        else:
            recipients = User.objects.filter(role='sales', is_active=True)

        subject = f"استفسار عن باقة: {package.title_ar}"
        message = f"""
        استفسار جديد عن باقة سياحية:

        العميل: {client.full_name_ar}
        البريد الإلكتروني: {client.email}
        الهاتف: {client.phone}

        الباقة: {package.title_ar}
        المدة: {package.duration_days} أيام / {package.duration_nights} ليالي
        السعر: {package.base_price} درهم

        يرجى التواصل مع العميل لتقديم المزيد من التفاصيل.

        رابط الباقة: http://127.0.0.1:8000/admin/tours/tourpackage/{package.id}/change/
        رابط العميل: http://127.0.0.1:8000/admin/crm/client/{client.id}/change/
        """

        for user in recipients:
            try:
                send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[user.email],
                    fail_silently=True,
                )
            except Exception as e:
                print(f"Failed to send package inquiry notification to {user.email}: {e}")

    @staticmethod
    def send_daily_summary():
        """Send daily summary to managers."""
        from apps.crm.models import Client
        from apps.tours.models import TourPackage

        today = timezone.now().date()
        yesterday = today - timedelta(days=1)

        # Get statistics
        new_clients_today = Client.objects.filter(created_at__date=today).count()
        new_clients_yesterday = Client.objects.filter(created_at__date=yesterday).count()

        total_clients = Client.objects.count()
        vip_clients = Client.objects.filter(vip_status=True).count()

        active_packages = TourPackage.objects.filter(is_active=True).count()

        # Notify managers
        managers = User.objects.filter(role__in=['admin', 'manager'], is_active=True)

        subject = f"ملخص يومي - {today.strftime('%d/%m/%Y')}"
        message = f"""
        ملخص يومي لنظام إدارة وكالة السفر المغربية
        التاريخ: {today.strftime('%d/%m/%Y')}

        📊 إحصائيات العملاء:
        - عملاء جدد اليوم: {new_clients_today}
        - عملاء جدد أمس: {new_clients_yesterday}
        - إجمالي العملاء: {total_clients}
        - عملاء VIP: {vip_clients}

        🏖️ إحصائيات الباقات:
        - باقات نشطة: {active_packages}

        📈 مقارنة مع الأمس:
        العملاء الجدد: {"↗️ زيادة" if new_clients_today > new_clients_yesterday else "↘️ نقصان" if new_clients_today < new_clients_yesterday else "→ نفس العدد"}

        رابط النظام: http://127.0.0.1:8000/
        رابط التقارير: http://127.0.0.1:8000/reports/

        تم إنشاء هذا التقرير تلقائياً
        """

        for manager in managers:
            try:
                send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[manager.email],
                    fail_silently=True,
                )
            except Exception as e:
                print(f"Failed to send daily summary to {manager.email}: {e}")

    @staticmethod
    def send_system_alert(message, alert_type='info', recipients=None):
        """Send system alert to specified users or all admins."""
        if recipients is None:
            recipients = User.objects.filter(role='admin', is_active=True)

        subject = f"تنبيه النظام - {alert_type.upper()}"

        for user in recipients:
            try:
                send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[user.email],
                    fail_silently=True,
                )
            except Exception as e:
                print(f"Failed to send system alert to {user.email}: {e}")


# Convenience functions
def notify_new_client(client):
    """Shortcut to notify about new client."""
    NotificationManager.send_client_registration_notification(client)


def notify_package_inquiry(client, package):
    """Shortcut to notify about package inquiry."""
    NotificationManager.send_package_inquiry_notification(client, package)


def send_welcome_email(user):
    """Shortcut to send welcome email."""
    return NotificationManager.send_welcome_email(user)
