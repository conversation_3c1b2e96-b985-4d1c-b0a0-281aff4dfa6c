"""
Core models for the Moroccan Travel Agency ERP system.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
# from simple_history.models import HistoricalRecords


class TimeStampedModel(models.Model):
    """
    Abstract base class that provides self-updating 'created_at' and 'updated_at' fields.
    """
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        abstract = True


class SoftDeleteModel(models.Model):
    """
    Abstract base class that provides soft delete functionality.
    """
    is_deleted = models.BooleanField(_('محذوف'), default=False)
    deleted_at = models.DateTimeField(_('تاريخ الحذف'), null=True, blank=True)

    class Meta:
        abstract = True

    def soft_delete(self):
        """Soft delete the object."""
        from django.utils import timezone
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save()

    def restore(self):
        """Restore a soft deleted object."""
        self.is_deleted = False
        self.deleted_at = None
        self.save()


class AuditModel(TimeStampedModel, SoftDeleteModel):
    """
    Abstract base class that combines timestamp and soft delete functionality
    with audit trail support.
    """
    history = HistoricalRecords(inherit=True)

    class Meta:
        abstract = True


class Country(models.Model):
    """Country model for storing country information."""
    name_ar = models.CharField(_('الاسم بالعربية'), max_length=100)
    name_fr = models.CharField(_('الاسم بالفرنسية'), max_length=100)
    name_en = models.CharField(_('الاسم بالإنجليزية'), max_length=100)
    code = models.CharField(_('الرمز'), max_length=3, unique=True)
    phone_code = models.CharField(_('رمز الهاتف'), max_length=5)
    is_active = models.BooleanField(_('نشط'), default=True)

    class Meta:
        verbose_name = _('دولة')
        verbose_name_plural = _('الدول')
        ordering = ['name_ar']

    def __str__(self):
        return self.name_ar


class City(models.Model):
    """City model for storing city information."""
    name_ar = models.CharField(_('الاسم بالعربية'), max_length=100)
    name_fr = models.CharField(_('الاسم بالفرنسية'), max_length=100)
    name_en = models.CharField(_('الاسم بالإنجليزية'), max_length=100)
    country = models.ForeignKey(Country, on_delete=models.CASCADE, verbose_name=_('الدولة'))
    is_active = models.BooleanField(_('نشط'), default=True)

    class Meta:
        verbose_name = _('مدينة')
        verbose_name_plural = _('المدن')
        ordering = ['name_ar']

    def __str__(self):
        return f"{self.name_ar}, {self.country.name_ar}"


class Currency(models.Model):
    """Currency model for multi-currency support."""
    name_ar = models.CharField(_('الاسم بالعربية'), max_length=50)
    name_fr = models.CharField(_('الاسم بالفرنسية'), max_length=50)
    code = models.CharField(_('الرمز'), max_length=3, unique=True)
    symbol = models.CharField(_('الرمز المختصر'), max_length=5)
    exchange_rate = models.DecimalField(_('سعر الصرف'), max_digits=10, decimal_places=4, default=1.0)
    is_base = models.BooleanField(_('العملة الأساسية'), default=False)
    is_active = models.BooleanField(_('نشط'), default=True)

    class Meta:
        verbose_name = _('عملة')
        verbose_name_plural = _('العملات')
        ordering = ['name_ar']

    def __str__(self):
        return f"{self.name_ar} ({self.code})"


class SystemSettings(models.Model):
    """System-wide settings."""
    key = models.CharField(_('المفتاح'), max_length=100, unique=True)
    value = models.TextField(_('القيمة'))
    description = models.TextField(_('الوصف'), blank=True)
    is_active = models.BooleanField(_('نشط'), default=True)

    class Meta:
        verbose_name = _('إعداد النظام')
        verbose_name_plural = _('إعدادات النظام')

    def __str__(self):
        return self.key
