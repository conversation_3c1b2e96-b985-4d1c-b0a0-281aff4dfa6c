{% extends 'base.html' %}
{% load static %}

{% block content %}
<!-- إضافة قسم الفلترة -->
<div class="container-fluid mb-4">
    <div class="card">
        <div class="card-body">
            <form id="filterForm" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">الفترة الزمنية</label>
                    <select class="form-select" name="time_period">
                        <option value="this_month">الشهر الحالي</option>
                        <option value="last_month">الشهر الماضي</option>
                        <option value="this_year">السنة الحالية</option>
                        <option value="custom">فترة مخصصة</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">نوع الباقة</label>
                    <select class="form-select" name="package_type">
                        <option value="all">الكل</option>
                        {% for category in tour_categories %}
                            <option value="{{ category.id }}">{{ category.name_ar }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">طريقة الدفع</label>
                    <select class="form-select" name="payment_method">
                        <option value="all">الكل</option>
                        <option value="cash">نقداً</option>
                        <option value="card">بطاقة بنكية</option>
                        <option value="transfer">تحويل بنكي</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">تطبيق الفلتر</button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <!-- ملخص مالي -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">الملخص المالي للعام {{ current_year }}</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="metric">
                                <h6>إجمالي الإيرادات</h6>
                                <h3 data-summary="revenue">{{ yearly_summary.total_revenue|floatformat:2 }} درهم</h3>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metric">
                                <h6>عدد الحجوزات</h6>
                                <h3 data-summary="bookings">{{ yearly_summary.total_bookings }}</h3>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metric">
                                <h6>متوسط قيمة الحجز</h6>
                                <h3 data-summary="average">{{ yearly_summary.average_booking_value|floatformat:2 }} درهم</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الرسم البياني للإيرادات الشهرية -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">الإيرادات الشهرية</h5>
                    <canvas id="monthlyRevenueChart"></canvas>
                </div>
            </div>
        </div>

        <!-- تحليل طرق الدفع -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">طرق الدفع</h5>
                    <canvas id="paymentMethodsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تكوين الرسم البياني للإيرادات الشهرية
    const monthlyRevenueChart = new Chart(
        document.getElementById('monthlyRevenueChart').getContext('2d'),
        {
            type: 'line',
            data: {
                labels: {{ monthly_revenue|safe }}.map(item => item.month),
                datasets: [{
                    label: 'الإيرادات',
                    data: {{ monthly_revenue|safe }}.map(item => item.total_revenue),
                    borderColor: '#4e73df',
                    tension: 0.1,
                    fill: true
                }, {
                    label: 'عدد الحجوزات',
                    data: {{ monthly_revenue|safe }}.map(item => item.booking_count),
                    borderColor: '#1cc88a',
                    tension: 0.1,
                    yAxisID: 'bookings'
                }]
            },
            options: {
                responsive: true,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    y: {
                        type: 'linear',
                        position: 'left',
                        title: {
                            display: true,
                            text: 'الإيرادات (درهم)'
                        }
                    },
                    bookings: {
                        type: 'linear',
                        position: 'right',
                        title: {
                            display: true,
                            text: 'عدد الحجوزات'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        rtl: true,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.datasetIndex === 0) {
                                    label += context.parsed.y.toFixed(2) + ' درهم';
                                } else {
                                    label += context.parsed.y + ' حجز';
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        }
    );

    // معالجة نموذج الفلترة
    document.getElementById('filterForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        const formData = new FormData(this);

        try {
            const response = await fetch('/api/reports/financial/?' + new URLSearchParams(formData));
            const data = await response.json();

            // تحديث البيانات في الرسوم البيانية
            monthlyRevenueChart.data.labels = data.monthly_revenue.map(item => item.month);
            monthlyRevenueChart.data.datasets[0].data = data.monthly_revenue.map(item => item.total_revenue);
            monthlyRevenueChart.data.datasets[1].data = data.monthly_revenue.map(item => item.booking_count);
            monthlyRevenueChart.update();

            // تحديث الملخص
            updateSummary(data.yearly_summary);
        } catch (error) {
            console.error('Error fetching data:', error);
            alert('حدث خطأ أثناء تحديث البيانات');
        }
    });
});

function updateSummary(summary) {
    document.querySelector('[data-summary="revenue"]').textContent =
        `${summary.total_revenue.toFixed(2)} درهم`;
    document.querySelector('[data-summary="bookings"]').textContent =
        summary.total_bookings;
    document.querySelector('[data-summary="average"]').textContent =
        `${summary.average_booking_value.toFixed(2)} درهم`;
}
</script>
{% endblock %}
