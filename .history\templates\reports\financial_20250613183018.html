{% extends 'base.html' %}
{% load static %}

{% block content %}
<!-- إضافة قسم الفلترة -->
<div class="container-fluid mb-4">
    <div class="card">
        <div class="card-body">
            <form id="filterForm" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">الفترة الزمنية</label>
                    <select class="form-select" name="time_period">
                        <option value="this_month">الشهر الحالي</option>
                        <option value="last_month">الشهر الماضي</option>
                        <option value="this_year">السنة الحالية</option>
                        <option value="custom">فترة مخصصة</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">نوع الباقة</label>
                    <select class="form-select" name="package_type">
                        <option value="all">الكل</option>
                        {% for category in tour_categories %}
                            <option value="{{ category.id }}">{{ category.name_ar }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">طريقة الدفع</label>
                    <select class="form-select" name="payment_method">
                        <option value="all">الكل</option>
                        <option value="cash">نقداً</option>
                        <option value="card">بطاقة بنكية</option>
                        <option value="transfer">تحويل بنكي</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">تطبيق الفلتر</button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <!-- ملخص مالي -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">الملخص المالي للعام {{ current_year }}</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="metric">
                                <h6>إجمالي الإيرادات</h6>
                                <h3 data-summary="revenue">{{ yearly_summary.total_revenue|floatformat:2 }} درهم</h3>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metric">
                                <h6>عدد الحجوزات</h6>
                                <h3 data-summary="bookings">{{ yearly_summary.total_bookings }}</h3>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metric">
                                <h6>متوسط قيمة الحجز</h6>
                                <h3 data-summary="average">{{ yearly_summary.average_booking_value|floatformat:2 }} درهم</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الرسم البياني للإيرادات الشهرية -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">الإيرادات الشهرية</h5>
                    <canvas id="monthlyRevenueChart"></canvas>
                </div>
            </div>
        </div>

        <!-- تحليل طرق الدفع -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">طرق الدفع</h5>
                    <canvas id="paymentMethodsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار التصدير -->
    <div class="card mt-3">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">خيارات التصدير</h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary" onclick="exportReport('pdf')">
                        <i class="fas fa-file-pdf"></i> PDF
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="exportReport('excel')">
                        <i class="fas fa-file-excel"></i> Excel
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="exportReport('csv')">
                        <i class="fas fa-file-csv"></i> CSV
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- مؤشرات أداء إضافية -->
    <div class="row mt-3">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h6>معدل النمو الشهري</h6>
                    <h3 data-summary="growth">{{ monthly_growth|floatformat:1 }}%</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h6>نسبة الإشغال</h6>
                    <h3 data-summary="occupancy">{{ occupancy_rate|floatformat:1 }}%</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h6>معدل التحصيل</h6>
                    <h3 data-summary="collection">{{ collection_rate|floatformat:1 }}%</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h6>متوسط مدة الرحلة</h6>
                    <h3 data-summary="duration">{{ avg_trip_duration }} يوم</h3>
                </div>
            </div>
        </div>
    </div>

    <div class="card mt-4">
    <div class="card-body">
        <h5 class="card-title mb-3">تفاصيل المعاملات</h5>
        <div class="table-responsive">
            <table class="table table-hover" id="transactionsTable">
                <thead>
                    <tr>
                        <th>رقم الحجز</th>
                        <th>العميل</th>
                        <th>الباقة</th>
                        <th>تاريخ الحجز</th>
                        <th>المبلغ</th>
                        <th>طريقة الدفع</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for transaction in transactions %}
                    <tr>
                        <td>{{ transaction.reservation_number }}</td>
                        <td>{{ transaction.client_name }}</td>
                        <td>{{ transaction.package_name }}</td>
                        <td>{{ transaction.booking_date|date:"Y-m-d" }}</td>
                        <td>{{ transaction.amount|floatformat:2 }} درهم</td>
                        <td>{{ transaction.payment_method }}</td>
                        <td>
                            <span class="badge bg-{{ transaction.status_color }}">
                                {{ transaction.status }}
                            </span>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- إضافة بعد قسم الرسوم البيانية الحالية -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">التحليل التنبؤي للمبيعات</h5>
                <div class="d-flex justify-content-between mb-3">
                    <div class="btn-group">
                        <button class="btn btn-outline-primary active" data-forecast-period="3">3 أشهر</button>
                        <button class="btn btn-outline-primary" data-forecast-period="6">6 أشهر</button>
                        <button class="btn btn-outline-primary" data-forecast-period="12">سنة</button>
                    </div>
                    <select class="form-select" style="width: 200px" id="forecastModel">
                        <option value="linear">نموذج خطي</option>
                        <option value="seasonal">نموذج موسمي</option>
                        <option value="weighted">المتوسط المرجح</option>
                    </select>
                </div>
                <canvas id="forecastChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تكوين الرسم البياني للإيرادات الشهرية
    const monthlyRevenueChart = new Chart(
        document.getElementById('monthlyRevenueChart').getContext('2d'),
        {
            type: 'line',
            data: {
                labels: {{ monthly_revenue|safe }}.map(item => item.month),
                datasets: [{
                    label: 'الإيرادات',
                    data: {{ monthly_revenue|safe }}.map(item => item.total_revenue),
                    borderColor: '#4e73df',
                    tension: 0.1,
                    fill: true
                }, {
                    label: 'عدد الحجوزات',
                    data: {{ monthly_revenue|safe }}.map(item => item.booking_count),
                    borderColor: '#1cc88a',
                    tension: 0.1,
                    yAxisID: 'bookings'
                }]
            },
            options: {
                responsive: true,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    y: {
                        type: 'linear',
                        position: 'left',
                        title: {
                            display: true,
                            text: 'الإيرادات (درهم)'
                        }
                    },
                    bookings: {
                        type: 'linear',
                        position: 'right',
                        title: {
                            display: true,
                            text: 'عدد الحجوزات'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        rtl: true,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.datasetIndex === 0) {
                                    label += context.parsed.y.toFixed(2) + ' درهم';
                                } else {
                                    label += context.parsed.y + ' حجز';
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        }
    );

    // معالجة نموذج الفلترة
    document.getElementById('filterForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        const formData = new FormData(this);

        try {
            const response = await fetch('/api/reports/financial/?' + new URLSearchParams(formData));
            const data = await response.json();

            // تحديث البيانات في الرسوم البيانية
            monthlyRevenueChart.data.labels = data.monthly_revenue.map(item => item.month);
            monthlyRevenueChart.data.datasets[0].data = data.monthly_revenue.map(item => item.total_revenue);
            monthlyRevenueChart.data.datasets[1].data = data.monthly_revenue.map(item => item.booking_count);
            monthlyRevenueChart.update();

            // تحديث الملخص
            updateSummary(data.yearly_summary);
        } catch (error) {
            console.error('Error fetching data:', error);
            alert('حدث خطأ أثناء تحديث البيانات');
        }
    });
});

// دالة تصدير التقارير
function exportReport(format) {
    const filters = new FormData(document.getElementById('filterForm'));
    const queryString = new URLSearchParams(filters).toString();

    const exportUrls = {
        'pdf': `/api/reports/export/pdf/?${queryString}`,
        'excel': `/api/reports/export/excel/?${queryString}`,
        'csv': `/api/reports/export/csv/?${queryString}`
    };

    window.location.href = exportUrls[format];
}

// تهيئة جدول البيانات
$(document).ready(function() {
    $('#transactionsTable').DataTable({
        language: {
            url: "{% static 'js/dataTables.arabic.json' %}"
        },
        order: [[3, 'desc']],
        pageLength: 10,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'collection',
                text: 'تصدير',
                buttons: ['copy', 'excel', 'pdf']
            }
        ]
    });
});

function updateSummary(summary) {
    document.querySelector('[data-summary="revenue"]').textContent =
        `${summary.total_revenue.toFixed(2)} درهم`;
    document.querySelector('[data-summary="bookings"]').textContent =
        summary.total_bookings;
    document.querySelector('[data-summary="average"]').textContent =
        `${summary.average_booking_value.toFixed(2)} درهم`;
}

// تحديث المؤشرات الإضافية
function updateKPIs(data) {
    document.querySelector('[data-summary="growth"]').textContent =
        `${data.monthly_growth.toFixed(1)}%`;
    document.querySelector('[data-summary="occupancy"]').textContent =
        `${data.occupancy_rate.toFixed(1)}%`;
    document.querySelector('[data-summary="collection"]').textContent =
        `${data.collection_rate.toFixed(1)}%`;
    document.querySelector('[data-summary="duration"]').textContent =
        `${data.avg_trip_duration} يوم`;
}

// تهيئة الرسم البياني التنبؤي
const forecastChart = new Chart(
    document.getElementById('forecastChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'البيانات الفعلية',
                    data: [],
                    borderColor: '#4e73df',
                    fill: false
                },
                {
                    label: 'التنبؤ',
                    data: [],
                    borderColor: '#1cc88a',
                    borderDash: [5, 5],
                    fill: false
                },
                {
                    label: 'هامش الثقة (95%)',
                    data: [],
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    borderWidth: 0,
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    mode: 'index',
                    intersect: false
                },
                legend: {
                    rtl: true,
                    position: 'top'
                }
            }
        }
    }
);

// دالة تحديث التنبؤات
async function updateForecast(period = 3, model = 'linear') {
    try {
        const response = await fetch(`/api/reports/forecast/?period=${period}&model=${model}`);
        const data = await response.json();

        forecastChart.data.labels = [...data.actual_dates, ...data.forecast_dates];
        forecastChart.data.datasets[0].data = data.actual_values;
        forecastChart.data.datasets[1].data = data.forecast_values;
        forecastChart.data.datasets[2].data = data.confidence_interval;

        forecastChart.update();

        // تحديث مؤشرات التنبؤ
        updateForecastMetrics(data.metrics);
    } catch (error) {
        console.error('خطأ في تحديث التنبؤات:', error);
        notify.error('حدث خطأ أثناء تحديث التنبؤات');
    }
}

// دالة تحديث مؤشرات التنبؤ
function updateForecastMetrics(metrics) {
    const metricsContainer = document.querySelector('#forecastMetrics');
    metricsContainer.innerHTML = `
        <div class="row g-3">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>النمو المتوقع</h6>
                        <h3>${metrics.expected_growth}%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>دقة التنبؤ</h6>
                        <h3>${metrics.accuracy}%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>الإيرادات المتوقعة</h6>
                        <h3>${metrics.expected_revenue} درهم</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>معامل الثقة</h6>
                        <h3>${metrics.confidence_level}%</h3>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// إضافة مستمعي الأحداث
document.querySelectorAll('[data-forecast-period]').forEach(button => {
    button.addEventListener('click', (e) => {
        document.querySelector('[data-forecast-period].active').classList.remove('active');
        e.target.classList.add('active');
        updateForecast(parseInt(e.target.dataset.forecastPeriod),
                      document.getElementById('forecastModel').value);
    });
});

document.getElementById('forecastModel').addEventListener('change', (e) => {
    const activePeriod = document.querySelector('[data-forecast-period].active');
    updateForecast(parseInt(activePeriod.dataset.forecastPeriod), e.target.value);
});

// تحديث التنبؤات عند تحميل الصفحة
updateForecast();
</script>
{% endblock %}
