"""
User and authentication models for the Moroccan Travel Agency ERP system.
"""
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _
# from phonenumber_field.modelfields import PhoneNumberField
from apps.core.models import AuditModel


class User(AbstractUser):
    """Custom user model with additional fields for travel agency staff."""

    ROLE_CHOICES = [
        ('admin', _('مدير النظام')),
        ('manager', _('مدير الوكالة')),
        ('sales', _('موظف مبيعات')),
        ('accountant', _('محاسب')),
        ('guide', _('مرشد سياحي')),
        ('driver', _('سائق')),
        ('receptionist', _('موظف استقبال')),
    ]

    # Personal Information
    first_name_ar = models.CharField(_('الاسم الأول بالعربية'), max_length=50)
    last_name_ar = models.CharField(_('اسم العائلة بالعربية'), max_length=50)
    phone = models.CharField(_('رقم الهاتف'), max_length=20, blank=True)
    whatsapp = models.CharField(_('رقم الواتساب'), max_length=20, blank=True)
    national_id = models.CharField(_('رقم البطاقة الوطنية'), max_length=20, unique=True, null=True, blank=True)

    # Work Information
    role = models.CharField(_('الدور'), max_length=20, choices=ROLE_CHOICES, default='sales')
    employee_id = models.CharField(_('رقم الموظف'), max_length=20, unique=True, null=True, blank=True)
    department = models.CharField(_('القسم'), max_length=50, blank=True)
    hire_date = models.DateField(_('تاريخ التوظيف'), null=True, blank=True)
    salary = models.DecimalField(_('الراتب'), max_digits=10, decimal_places=2, null=True, blank=True)

    # Profile
    avatar = models.ImageField(_('الصورة الشخصية'), upload_to='avatars/', blank=True)
    bio = models.TextField(_('نبذة شخصية'), blank=True)

    # Preferences
    preferred_language = models.CharField(
        _('اللغة المفضلة'),
        max_length=5,
        choices=[('ar', 'العربية'), ('fr', 'Français'), ('en', 'English')],
        default='ar'
    )

    # Status
    is_active_employee = models.BooleanField(_('موظف نشط'), default=True)
    last_activity = models.DateTimeField(_('آخر نشاط'), null=True, blank=True)

    class Meta:
        verbose_name = _('مستخدم')
        verbose_name_plural = _('المستخدمون')

    def __str__(self):
        return f"{self.first_name_ar} {self.last_name_ar}" if self.first_name_ar else self.username

    @property
    def full_name_ar(self):
        """Return full name in Arabic."""
        return f"{self.first_name_ar} {self.last_name_ar}".strip()

    @property
    def full_name_en(self):
        """Return full name in English."""
        return f"{self.first_name} {self.last_name}".strip()

    def get_role_display_ar(self):
        """Get role display in Arabic."""
        role_dict = dict(self.ROLE_CHOICES)
        return role_dict.get(self.role, self.role)


class UserProfile(AuditModel):
    """Extended user profile information."""
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name=_('المستخدم'))

    # Emergency Contact
    emergency_contact_name = models.CharField(_('اسم جهة الاتصال للطوارئ'), max_length=100, blank=True)
    emergency_contact_phone = models.CharField(_('هاتف جهة الاتصال للطوارئ'), max_length=20, blank=True)
    emergency_contact_relation = models.CharField(_('صلة القرابة'), max_length=50, blank=True)

    # Address
    address = models.TextField(_('العنوان'), blank=True)
    city = models.ForeignKey('core.City', on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_('المدينة'))
    postal_code = models.CharField(_('الرمز البريدي'), max_length=10, blank=True)

    # Work Details
    work_schedule = models.JSONField(_('جدول العمل'), default=dict, blank=True)
    skills = models.TextField(_('المهارات'), blank=True)
    certifications = models.TextField(_('الشهادات'), blank=True)
    languages = models.CharField(_('اللغات'), max_length=200, blank=True)

    # Banking Information (for payroll)
    bank_name = models.CharField(_('اسم البنك'), max_length=100, blank=True)
    bank_account = models.CharField(_('رقم الحساب البنكي'), max_length=50, blank=True)

    class Meta:
        verbose_name = _('ملف المستخدم')
        verbose_name_plural = _('ملفات المستخدمين')

    def __str__(self):
        return f"ملف {self.user.full_name_ar}"


class UserSession(models.Model):
    """Track user sessions for security purposes."""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_('المستخدم'))
    session_key = models.CharField(_('مفتاح الجلسة'), max_length=40)
    ip_address = models.GenericIPAddressField(_('عنوان IP'))
    user_agent = models.TextField(_('معلومات المتصفح'))
    login_time = models.DateTimeField(_('وقت تسجيل الدخول'), auto_now_add=True)
    logout_time = models.DateTimeField(_('وقت تسجيل الخروج'), null=True, blank=True)
    is_active = models.BooleanField(_('نشط'), default=True)

    class Meta:
        verbose_name = _('جلسة المستخدم')
        verbose_name_plural = _('جلسات المستخدمين')
        ordering = ['-login_time']

    def __str__(self):
        return f"{self.user.username} - {self.login_time}"


class Permission(models.Model):
    """Custom permissions for fine-grained access control."""
    name = models.CharField(_('الاسم'), max_length=100)
    codename = models.CharField(_('الرمز'), max_length=100, unique=True)
    description = models.TextField(_('الوصف'), blank=True)
    module = models.CharField(_('الوحدة'), max_length=50)

    class Meta:
        verbose_name = _('صلاحية')
        verbose_name_plural = _('الصلاحيات')
        ordering = ['module', 'name']

    def __str__(self):
        return self.name


class Role(models.Model):
    """Role-based access control."""
    name = models.CharField(_('الاسم'), max_length=50, unique=True)
    description = models.TextField(_('الوصف'), blank=True)
    permissions = models.ManyToManyField(Permission, verbose_name=_('الصلاحيات'), blank=True)
    is_active = models.BooleanField(_('نشط'), default=True)

    class Meta:
        verbose_name = _('دور')
        verbose_name_plural = _('الأدوار')
        ordering = ['name']

    def __str__(self):
        return self.name


class UserRole(models.Model):
    """User role assignments."""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_('المستخدم'))
    role = models.ForeignKey(Role, on_delete=models.CASCADE, verbose_name=_('الدور'))
    assigned_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='assigned_roles', verbose_name=_('تم التعيين بواسطة'))
    assigned_at = models.DateTimeField(_('تاريخ التعيين'), auto_now_add=True)
    is_active = models.BooleanField(_('نشط'), default=True)

    class Meta:
        verbose_name = _('دور المستخدم')
        verbose_name_plural = _('أدوار المستخدمين')
        unique_together = ['user', 'role']

    def __str__(self):
        return f"{self.user.username} - {self.role.name}"
