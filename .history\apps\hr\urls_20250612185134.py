"""
URL configuration for HR app.
"""
from django.urls import path
from . import views

app_name = 'hr'

urlpatterns = [
    # Employee URLs
    path('', views.EmployeeListView.as_view(), name='employee_list'),
    path('employees/', views.EmployeeListView.as_view(), name='employee_list'),
    path('employees/<int:pk>/', views.EmployeeDetailView.as_view(), name='employee_detail'),
    path('employees/<int:pk>/edit/', views.EmployeeUpdateView.as_view(), name='employee_edit'),
    
    # Department URLs
    path('departments/', views.DepartmentListView.as_view(), name='department_list'),
    path('departments/add/', views.DepartmentCreateView.as_view(), name='department_add'),
    path('departments/<int:pk>/edit/', views.DepartmentUpdateView.as_view(), name='department_edit'),
    
    # Position URLs
    path('positions/', views.PositionListView.as_view(), name='position_list'),
    path('positions/add/', views.PositionCreateView.as_view(), name='position_add'),
    path('positions/<int:pk>/edit/', views.PositionUpdateView.as_view(), name='position_edit'),
    
    # Schedule URLs
    path('schedules/', views.ScheduleListView.as_view(), name='schedule_list'),
    path('schedules/add/', views.ScheduleCreateView.as_view(), name='schedule_add'),
    path('schedules/<int:pk>/edit/', views.ScheduleUpdateView.as_view(), name='schedule_edit'),
    
    # Leave URLs
    path('leaves/', views.LeaveListView.as_view(), name='leave_list'),
    path('leaves/add/', views.LeaveCreateView.as_view(), name='leave_add'),
    path('leaves/<int:pk>/', views.LeaveDetailView.as_view(), name='leave_detail'),
    path('leaves/<int:pk>/approve/', views.LeaveApproveView.as_view(), name='leave_approve'),
    path('leaves/<int:pk>/reject/', views.LeaveRejectView.as_view(), name='leave_reject'),
    
    # Attendance URLs
    path('attendance/', views.AttendanceListView.as_view(), name='attendance_list'),
    path('attendance/add/', views.AttendanceCreateView.as_view(), name='attendance_add'),
    path('attendance/<int:pk>/edit/', views.AttendanceUpdateView.as_view(), name='attendance_edit'),
    path('attendance/bulk/', views.AttendanceBulkView.as_view(), name='attendance_bulk'),
    
    # Payroll URLs
    path('payroll/', views.PayrollListView.as_view(), name='payroll_list'),
    path('payroll/generate/', views.PayrollGenerateView.as_view(), name='payroll_generate'),
    path('payroll/<int:pk>/', views.PayrollDetailView.as_view(), name='payroll_detail'),
    path('payroll/<int:pk>/approve/', views.PayrollApproveView.as_view(), name='payroll_approve'),
    
    # Reports
    path('reports/attendance/', views.AttendanceReportView.as_view(), name='attendance_report'),
    path('reports/payroll/', views.PayrollReportView.as_view(), name='payroll_report'),
]
