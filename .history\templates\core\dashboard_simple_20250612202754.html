{% load i18n %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% trans "لوحة التحكم" %} - نظام إدارة وكالة السفر المغربية</title>

    <!-- Bootstrap CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background-color: #f8f9fa;
        }
        .navbar {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        .stats-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-plane me-2"></i>
                {% trans "نظام إدارة وكالة السفر المغربية" %}
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            {% trans "لوحة التحكم" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/">
                            <i class="fas fa-cog me-1"></i>
                            {% trans "الإدارة" %}
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/">
                            <i class="fas fa-user me-1"></i>
                            {% trans "تسجيل الدخول" %}
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-4">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    {% trans "لوحة التحكم الرئيسية" %}
                </h1>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-start border-primary border-4 h-100">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <div class="text-xs fw-bold text-primary text-uppercase mb-1">
                                    {% trans "إجمالي العملاء" %}
                                </div>
                                <div class="h5 mb-0 fw-bold text-gray-800">{{ total_clients }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-start border-success border-4 h-100">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <div class="text-xs fw-bold text-success text-uppercase mb-1">
                                    {% trans "الحجوزات النشطة" %}
                                </div>
                                <div class="h5 mb-0 fw-bold text-gray-800">{{ active_bookings }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-calendar-check fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-start border-warning border-4 h-100">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <div class="text-xs fw-bold text-warning text-uppercase mb-1">
                                    {% trans "الباقات السياحية" %}
                                </div>
                                <div class="h5 mb-0 fw-bold text-gray-800">{{ total_packages }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-map-marked-alt fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-start border-info border-4 h-100">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <div class="text-xs fw-bold text-info text-uppercase mb-1">
                                    {% trans "المبيعات هذا الشهر" %}
                                </div>
                                <div class="h5 mb-0 fw-bold text-gray-800">{{ monthly_sales }} MAD</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Welcome Message -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 fw-bold text-primary">
                            <i class="fas fa-star me-2"></i>
                            {% trans "مرحباً بك في نظام إدارة وكالة السفر المغربية" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h5>{% trans "نظام متكامل لإدارة وكالة السفر" %}</h5>
                                <p class="text-muted">
                                    {% trans "يمكنك من خلال هذا النظام إدارة جميع عمليات وكالة السفر بكفاءة عالية، من إدارة العملاء والحجوزات إلى الباقات السياحية والتقارير المالية." %}
                                </p>
                                <div class="row mt-4">
                                    <div class="col-md-3 mb-3">
                                        <a href="/admin/" class="btn btn-primary w-100">
                                            <i class="fas fa-cog fa-2x d-block mb-2"></i>
                                            {% trans "لوحة الإدارة" %}
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <a href="/stats/" class="btn btn-success w-100">
                                            <i class="fas fa-chart-bar fa-2x d-block mb-2"></i>
                                            {% trans "الإحصائيات" %}
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <a href="/reports/" class="btn btn-info w-100">
                                            <i class="fas fa-chart-line fa-2x d-block mb-2"></i>
                                            {% trans "التقارير" %}
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <a href="/api/" class="btn btn-outline-primary w-100">
                                            <i class="fas fa-code fa-2x d-block mb-2"></i>
                                            {% trans "واجهة API" %}
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <i class="fas fa-plane fa-5x text-primary opacity-25"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Clients -->
        {% if recent_clients %}
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 fw-bold text-primary">
                            <i class="fas fa-users me-2"></i>
                            {% trans "العملاء الجدد" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{% trans "الاسم" %}</th>
                                        <th>{% trans "البريد الإلكتروني" %}</th>
                                        <th>{% trans "الهاتف" %}</th>
                                        <th>{% trans "النوع" %}</th>
                                        <th>{% trans "تاريخ التسجيل" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for client in recent_clients %}
                                    <tr>
                                        <td>{{ client.full_name_ar }}</td>
                                        <td>{{ client.email }}</td>
                                        <td>{{ client.phone }}</td>
                                        <td>
                                            {% if client.client_type == 'individual' %}
                                                <span class="badge bg-primary">{% trans "فردي" %}</span>
                                            {% else %}
                                                <span class="badge bg-success">{% trans "شركة" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ client.created_at|date:"d/m/Y" }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Features -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 fw-bold text-primary">
                            <i class="fas fa-list me-2"></i>
                            {% trans "الوحدات المتاحة" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <div class="text-center p-3 border rounded">
                                    <i class="fas fa-users fa-3x text-primary mb-2"></i>
                                    <h6>{% trans "إدارة العملاء" %}</h6>
                                    <small class="text-muted">{% trans "قاعدة بيانات شاملة للعملاء" %}</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="text-center p-3 border rounded">
                                    <i class="fas fa-map-marked-alt fa-3x text-success mb-2"></i>
                                    <h6>{% trans "الباقات السياحية" %}</h6>
                                    <small class="text-muted">{% trans "إدارة الوجهات والباقات" %}</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="text-center p-3 border rounded">
                                    <i class="fas fa-calendar-check fa-3x text-warning mb-2"></i>
                                    <h6>{% trans "الحجوزات" %}</h6>
                                    <small class="text-muted">{% trans "نظام حجوزات متقدم" %}</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="text-center p-3 border rounded">
                                    <i class="fas fa-chart-bar fa-3x text-info mb-2"></i>
                                    <h6>{% trans "التقارير" %}</h6>
                                    <small class="text-muted">{% trans "تقارير وتحليلات شاملة" %}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p>&copy; 2024 {% trans "نظام إدارة وكالة السفر المغربية" %}. {% trans "جميع الحقوق محفوظة" %}.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
