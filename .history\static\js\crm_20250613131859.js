// CRM Module Functions

// Initialize Notifications
const notify = {
    success: (message) => {
        toastr.success(message, '', {
            closeButton: true,
            progressBar: true,
            rtl: true
        });
    },
    error: (message) => {
        toastr.error(message, '', {
            closeButton: true,
            progressBar: true,
            rtl: true,
            timeOut: 0,
            extendedTimeOut: 0
        });
    },
    info: (message) => {
        toastr.info(message, '', {
            closeButton: true,
            progressBar: true,
            rtl: true
        });
    }
};

// Client Input Validation
const validateClient = (client) => {
    const errors = [];

    if (!client.full_name_ar?.trim()) {
        errors.push('الاسم بالعربية مطلوب');
    }

    if (!client.phone?.trim()) {
        errors.push('رقم الهاتف مطلوب');
    } else if (!/^(\+212|0)[567]\d{8}$/.test(client.phone)) {
        errors.push('رقم الهاتف غير صالح');
    }

    if (client.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(client.email)) {
        errors.push('البريد الإلكتروني غير صالح');
    }

    if (client.passport_number && !/^[A-Z0-9]{6,12}$/.test(client.passport_number)) {
        errors.push('رقم جواز السفر غير صالح');
    }

    return errors;
};

// Export Client Data
const exportClientData = async (format) => {
    try {
        const response = await axios.get(`/api/clients/export/?format=${format}`, {
            responseType: 'blob'
        });

        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `clients_${new Date().toISOString().split('T')[0]}.${format}`);
        document.body.appendChild(link);
        link.click();
        link.remove();
    } catch (error) {
        notify.error('حدث خطأ أثناء تصدير البيانات');
        console.error('Export error:', error);
    }
};

// Format Phone Numbers
const formatPhoneNumber = (phone) => {
    if (!phone) return '';

    // Format Moroccan phone numbers
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
        return cleaned.replace(/(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4 $5');
    }
    return phone;
};

// Cache Management
const cacheKey = 'crm_clients_cache';
const cacheExpiry = 5 * 60 * 1000; // 5 minutes

const getCachedClients = () => {
    const cached = localStorage.getItem(cacheKey);
    if (cached) {
        const { timestamp, data } = JSON.parse(cached);
        if (Date.now() - timestamp < cacheExpiry) {
            return data;
        }
    }
    return null;
};

const setCachedClients = (clients) => {
    localStorage.setItem(cacheKey, JSON.stringify({
        timestamp: Date.now(),
        data: clients
    }));
};

// Offline Support
let offlineQueue = [];

const processOfflineQueue = async () => {
    if (navigator.onLine && offlineQueue.length > 0) {
        notify.info(`جاري معالجة ${offlineQueue.length} عملية معلقة...`);

        for (const task of offlineQueue) {
            try {
                await task();
            } catch (error) {
                console.error('Error processing offline task:', error);
            }
        }

        offlineQueue = [];
        notify.success('تم معالجة جميع العمليات المعلقة');
    }
};

window.addEventListener('online', processOfflineQueue);

// Export functions
window.crmUtils = {
    notify,
    validateClient,
    exportClientData,
    formatPhoneNumber,
    getCachedClients,
    setCachedClients
};
