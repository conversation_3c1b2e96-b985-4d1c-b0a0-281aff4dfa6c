import pytest
from django.test import TestCase
from apps.reservations.models import Reservation
from django.utils import timezone

@pytest.mark.django_db
class TestReservationModel(TestCase):
    def test_create_reservation(self):
        reservation = Reservation.objects.create(
            customer_name="Test Customer",
            reservation_date=timezone.now(),
            status="pending"
        )
        assert reservation.customer_name == "Test Customer"
        assert reservation.status == "pending"

    def test_reservation_str(self):
        reservation = Reservation.objects.create(
            customer_name="Test Customer",
            reservation_date=timezone.now(),
        )
        assert str(reservation) == f"Reservation for {reservation.customer_name}"

    def test_reservation_is_active(self):
        reservation = Reservation.objects.create(
            customer_name="Test Customer",
            reservation_date=timezone.now(),
            status="confirmed"
        )
        assert reservation.is_active() is True
