{% extends 'base.html' %}
{% load static %}

{% block title %}الباقات السياحية{% endblock %}

{% block extra_css %}
<style>
    .package-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: none;
        border-radius: 15px;
        overflow: hidden;
        height: 100%;
    }
    
    .package-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }
    
    .package-image {
        height: 200px;
        object-fit: cover;
        width: 100%;
    }
    
    .package-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 2;
    }
    
    .price-tag {
        background: linear-gradient(45deg, #007bff, #0056b3);
        color: white;
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: bold;
    }
    
    .difficulty-badge {
        font-size: 0.8rem;
        padding: 4px 8px;
    }
    
    .stats-row {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .search-form {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-map-marked-alt text-primary me-2"></i>
                        الباقات السياحية
                    </h1>
                    <p class="text-muted mb-0">إدارة وعرض جميع الباقات السياحية</p>
                </div>
                <div>
                    <a href="{% url 'tours:package_add' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة باقة جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Row -->
    <div class="stats-row">
        <div class="row text-center">
            <div class="col-md-3">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="me-3">
                        <i class="fas fa-box-open fa-2x text-primary"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">{{ packages.count }}</h4>
                        <small class="text-muted">إجمالي الباقات</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="me-3">
                        <i class="fas fa-check-circle fa-2x text-success"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">{{ packages|length }}</h4>
                        <small class="text-muted">الباقات النشطة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="me-3">
                        <i class="fas fa-star fa-2x text-warning"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">0</h4>
                        <small class="text-muted">الباقات المميزة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="me-3">
                        <i class="fas fa-fire fa-2x text-danger"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">0</h4>
                        <small class="text-muted">الأكثر مبيعاً</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Form -->
    <div class="search-form">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       placeholder="ابحث في الباقات..." value="{{ request.GET.search }}">
            </div>
            <div class="col-md-3">
                <label for="category" class="form-label">الفئة</label>
                <select class="form-select" id="category" name="category">
                    <option value="">جميع الفئات</option>
                    <!-- Categories will be populated dynamically -->
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>بحث
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Packages Grid -->
    <div class="row">
        {% for package in packages %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card package-card h-100">
                <div class="position-relative">
                    {% if package.main_image %}
                        <img src="{{ package.main_image.url }}" class="package-image" alt="{{ package.title_ar }}">
                    {% else %}
                        <div class="package-image bg-light d-flex align-items-center justify-content-center">
                            <i class="fas fa-image fa-3x text-muted"></i>
                        </div>
                    {% endif %}
                    
                    <!-- Badges -->
                    <div class="package-badge">
                        {% if package.is_featured %}
                            <span class="badge bg-warning text-dark">مميز</span>
                        {% endif %}
                        {% if package.is_bestseller %}
                            <span class="badge bg-danger">الأكثر مبيعاً</span>
                        {% endif %}
                        {% if not package.is_active %}
                            <span class="badge bg-secondary">غير نشط</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="card-body d-flex flex-column">
                    <div class="mb-2">
                        <small class="text-muted">{{ package.package_code }}</small>
                        {% if package.category %}
                            <span class="badge bg-primary ms-2">{{ package.category.name_ar }}</span>
                        {% endif %}
                    </div>
                    
                    <h5 class="card-title">{{ package.title_ar }}</h5>
                    <p class="card-text text-muted small flex-grow-1">
                        {{ package.short_description_ar|truncatewords:15 }}
                    </p>
                    
                    <!-- Package Details -->
                    <div class="row text-center mb-3">
                        <div class="col-4">
                            <small class="text-muted d-block">المدة</small>
                            <strong>{{ package.duration_days }} أيام</strong>
                        </div>
                        <div class="col-4">
                            <small class="text-muted d-block">المشاركين</small>
                            <strong>{{ package.max_participants }}</strong>
                        </div>
                        <div class="col-4">
                            <small class="text-muted d-block">الصعوبة</small>
                            <span class="badge difficulty-badge 
                                {% if package.difficulty_level == 'easy' %}bg-success
                                {% elif package.difficulty_level == 'moderate' %}bg-warning
                                {% elif package.difficulty_level == 'challenging' %}bg-orange
                                {% else %}bg-danger{% endif %}">
                                {{ package.get_difficulty_level_display }}
                            </span>
                        </div>
                    </div>
                    
                    <!-- Price and Actions -->
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="price-tag">
                            {{ package.base_price }} درهم
                        </div>
                        <div class="btn-group" role="group">
                            <a href="{% url 'tours:package_detail' package.pk %}" 
                               class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'tours:package_edit' package.pk %}" 
                               class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-edit"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-box-open fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد باقات سياحية</h4>
                <p class="text-muted">ابدأ بإضافة باقة سياحية جديدة</p>
                <a href="{% url 'tours:package_add' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة باقة جديدة
                </a>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="row">
        <div class="col-12">
            <nav aria-label="صفحات الباقات">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1">الأولى</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                        </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                    </li>
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add any JavaScript functionality here
    console.log('Tours package list loaded');
});
</script>
{% endblock %}
