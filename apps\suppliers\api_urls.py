"""
API URL configuration for suppliers app.
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import api_views

router = DefaultRouter()
router.register(r'suppliers', api_views.SupplierViewSet)
router.register(r'contracts', api_views.SupplierContractViewSet)
router.register(r'evaluations', api_views.SupplierEvaluationViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('suppliers/<int:supplier_id>/contracts/', api_views.SupplierContractListView.as_view(), name='supplier-contracts'),
    path('suppliers/<int:supplier_id>/evaluations/', api_views.SupplierEvaluationListView.as_view(), name='supplier-evaluations'),
    path('search/', api_views.SupplierSearchView.as_view(), name='supplier-search'),
]
