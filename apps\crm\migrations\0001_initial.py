# Generated by Django 4.2.7 on 2025-06-12 17:35

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Client',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('client_code', models.CharField(max_length=20, unique=True, verbose_name='رمز العميل')),
                ('first_name_ar', models.Char<PERSON>ield(max_length=50, verbose_name='الاسم الأول بالعربية')),
                ('last_name_ar', models.CharField(max_length=50, verbose_name='اسم العائلة بالعربية')),
                ('first_name_fr', models.CharField(blank=True, max_length=50, verbose_name='الاسم الأول بالفرنسية')),
                ('last_name_fr', models.CharField(blank=True, max_length=50, verbose_name='اسم العائلة بالفرنسية')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('whatsapp', models.CharField(blank=True, max_length=20, verbose_name='رقم الواتساب')),
                ('secondary_phone', models.CharField(blank=True, max_length=20, verbose_name='هاتف ثانوي')),
                ('gender', models.CharField(blank=True, choices=[('M', 'ذكر'), ('F', 'أنثى')], max_length=1, verbose_name='الجنس')),
                ('date_of_birth', models.DateField(blank=True, null=True, verbose_name='تاريخ الميلاد')),
                ('nationality', models.CharField(blank=True, max_length=50, verbose_name='الجنسية')),
                ('passport_number', models.CharField(blank=True, max_length=20, validators=[django.core.validators.RegexValidator('^[A-Z0-9]+$', 'رقم جواز السفر يجب أن يحتوي على أحرف وأرقام فقط')], verbose_name='رقم جواز السفر')),
                ('passport_expiry', models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء جواز السفر')),
                ('national_id', models.CharField(blank=True, max_length=20, verbose_name='رقم البطاقة الوطنية')),
                ('address', models.TextField(blank=True, verbose_name='العنوان')),
                ('postal_code', models.CharField(blank=True, max_length=10, verbose_name='الرمز البريدي')),
                ('client_type', models.CharField(choices=[('individual', 'فرد'), ('family', 'عائلة'), ('group', 'مجموعة'), ('corporate', 'شركة')], default='individual', max_length=20, verbose_name='نوع العميل')),
                ('company_name', models.CharField(blank=True, max_length=100, verbose_name='اسم الشركة')),
                ('tax_number', models.CharField(blank=True, max_length=20, verbose_name='الرقم الضريبي')),
                ('preferred_language', models.CharField(choices=[('ar', 'العربية'), ('fr', 'Français'), ('en', 'English')], default='ar', max_length=5, verbose_name='اللغة المفضلة')),
                ('special_requirements', models.TextField(blank=True, verbose_name='متطلبات خاصة')),
                ('dietary_restrictions', models.TextField(blank=True, verbose_name='قيود غذائية')),
                ('loyalty_points', models.PositiveIntegerField(default=0, verbose_name='نقاط الولاء')),
                ('vip_status', models.BooleanField(default=False, verbose_name='عميل مميز')),
                ('marketing_consent', models.BooleanField(default=False, verbose_name='موافقة التسويق')),
                ('newsletter_subscription', models.BooleanField(default=False, verbose_name='اشتراك النشرة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('assigned_agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='الوكيل المسؤول')),
                ('city', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.city', verbose_name='المدينة')),
            ],
            options={
                'verbose_name': 'عميل',
                'verbose_name_plural': 'العملاء',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['client_code'], name='crm_client_client__1d99fd_idx'), models.Index(fields=['email'], name='crm_client_email_3c7615_idx'), models.Index(fields=['phone'], name='crm_client_phone_7f59d5_idx'), models.Index(fields=['passport_number'], name='crm_client_passpor_87bffe_idx')],
            },
        ),
    ]
