"""
URL configuration for reports app.
"""
from django.urls import path
from . import views

app_name = 'reports'

urlpatterns = [
    # Dashboard
    path('', views.ReportsView.as_view(), name='dashboard'),

    # Sales Reports
    path('sales/', views.SalesReportView.as_view(), name='sales_report'),
    path('sales/monthly/', views.MonthlySalesReportView.as_view(), name='monthly_sales_report'),
    path('sales/by-package/', views.PackageSalesReportView.as_view(), name='package_sales_report'),
    path('sales/by-agent/', views.AgentSalesReportView.as_view(), name='agent_sales_report'),

    # Client Reports
    path('clients/', views.ClientReportView.as_view(), name='client_report'),
    path('clients/demographics/', views.ClientDemographicsReportView.as_view(), name='client_demographics_report'),
    path('clients/loyalty/', views.ClientLoyaltyReportView.as_view(), name='client_loyalty_report'),

    # Financial Reports
    path('financial/', views.FinancialReportView.as_view(), name='financial_report'),
    path('financial/profit-loss/', views.ProfitLossReportView.as_view(), name='profit_loss_report'),
    path('financial/cash-flow/', views.CashFlowReportView.as_view(), name='cash_flow_report'),
    path('financial/tax/', views.TaxReportView.as_view(), name='tax_report'),

    # Operational Reports
    path('operational/', views.OperationalReportView.as_view(), name='operational_report'),
    path('operational/occupancy/', views.OccupancyReportView.as_view(), name='occupancy_report'),
    path('operational/supplier-performance/', views.SupplierPerformanceReportView.as_view(), name='supplier_performance_report'),

    # HR Reports
    path('hr/', views.HRReportView.as_view(), name='hr_report'),
    path('hr/attendance/', views.AttendanceReportView.as_view(), name='attendance_report'),
    path('hr/payroll/', views.PayrollReportView.as_view(), name='payroll_report'),

    # Custom Reports
    path('custom/', views.CustomReportView.as_view(), name='custom_report'),
    path('custom/builder/', views.ReportBuilderView.as_view(), name='report_builder'),

    # Export
    path('export/<str:report_type>/', views.ReportExportView.as_view(), name='report_export'),
]
