"""
Serializers for accounts app models.
"""
from rest_framework import serializers
from django.contrib.auth.models import Permission
from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from apps.accounts.models import Role, UserSession

User = get_user_model()


class PermissionSerializer(serializers.ModelSerializer):
    """Serializer for Permission model."""
    class Meta:
        model = Permission
        fields = ['id', 'name', 'codename', 'content_type']
        read_only_fields = ['id']


class RoleSerializer(serializers.ModelSerializer):
    """Serializer for Role model."""
    permissions = PermissionSerializer(many=True, read_only=True)

    class Meta:
        model = Role
        fields = ['id', 'name', 'description', 'permissions', 'is_active']
        read_only_fields = ['id']


class UserSerializer(serializers.ModelSerializer):
    """Serializer for User model."""
    roles = RoleSerializer(many=True, read_only=True)

    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'is_active', 'is_staff', 'roles', 'system_role',
        ]
        read_only_fields = ['id']


class UserSessionSerializer(serializers.ModelSerializer):
    """Serializer for UserSession model."""
    class Meta:
        model = UserSession
        fields = [
            'id', 'user', 'session_key', 'ip_address',
            'user_agent', 'login_time', 'last_activity',
            'is_active', 'logout_time',
        ]
        read_only_fields = ['id', 'login_time', 'last_activity']


class LoginSerializer(TokenObtainPairSerializer):
    """Custom login serializer with additional validation."""
    def validate(self, attrs):
        data = super().validate(attrs)
        if not self.user:
            raise serializers.ValidationError(_('Unable to login with provided credentials.'))

        data.update({
            'user_id': self.user.id,
            'username': self.user.username,
            'email': self.user.email,
            'first_name': self.user.first_name,
            'last_name': self.user.last_name,
            'is_staff': self.user.is_staff,
        })
        return data


class ProfileSerializer(serializers.ModelSerializer):
    """Serializer for user profile."""
    current_password = serializers.CharField(write_only=True, required=False)
    roles = RoleSerializer(many=True, read_only=True)

    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'first_name_ar', 'last_name_ar', 'phone',
            'current_password', 'password', 'is_active', 'is_staff',
            'date_joined', 'last_login', 'roles', 'system_role',
            'employee_id', 'department', 'hire_date', 'salary',
            'emergency_contact_name', 'emergency_contact_phone',
            'preferred_language',
        ]
        read_only_fields = [
            'id', 'is_active', 'is_staff',
            'date_joined', 'last_login', 'roles',
        ]
        extra_kwargs = {
            'password': {'write_only': True, 'required': False}
        }

    def validate(self, attrs):
        """Validate the profile update data."""
        if 'password' in attrs and not attrs.get('current_password'):
            msg = _('Current password is required to set a new password.')
            raise serializers.ValidationError({'current_password': msg})

        if ('password' in attrs and
                not self.instance.check_password(attrs['current_password'])):
            msg = _('Current password is incorrect.')
            raise serializers.ValidationError({'current_password': msg})
        return attrs

    def update(self, instance, validated_data):
        """Update user profile."""
        validated_data.pop('current_password', None)
        password = validated_data.pop('password', None)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        if password:
            instance.set_password(password)

        instance.save()
        return instance


class PasswordChangeSerializer(serializers.Serializer):
    """Serializer for password change endpoint."""
    current_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)

    def validate_current_password(self, value):
        """Validate current password."""
        if not self.context['request'].user.check_password(value):
            msg = _('Current password is incorrect.')
            raise serializers.ValidationError(msg)
        return value

    def validate_new_password(self, value):
        """Validate new password."""
        try:
            validate_password(value, self.context['request'].user)
        except ValidationError as e:
            raise serializers.ValidationError(str(e))
        return value

    def create(self, validated_data):
        raise NotImplementedError('Use save() instead.')

    def update(self, instance, validated_data):
        raise NotImplementedError('Use save() instead.')

    def save(self):
        """Save new password."""
        user = self.context['request'].user
        user.set_password(self.validated_data['new_password'])
        user.save()


class PasswordResetSerializer(serializers.Serializer):
    """Serializer for password reset endpoint."""
    email = serializers.EmailField(required=True)

    def validate_email(self, value):
        """Validate that user exists with this email."""
        try:
            user = User.objects.get(email=value)
            setattr(self, '_user', user)
            if not user.is_active:
                msg = _('This account is inactive.')
                raise serializers.ValidationError(msg)
        except User.DoesNotExist as e:
            msg = _('No user found with this email address.')
            raise serializers.ValidationError(msg) from e
        return value

    def create(self, validated_data):
        raise NotImplementedError('Use save() instead.')

    def update(self, instance, validated_data):
        raise NotImplementedError('Use save() instead.')

    def save(self):
        """Send password reset email."""
        from django.contrib.auth.tokens import default_token_generator
        from django.conf import settings
        from .emails import send_password_reset_email

        user = getattr(self, '_user')
        token = default_token_generator.make_token(user)
        reset_url = (f"{settings.FRONTEND_URL}"
                    f"/password-reset/confirm/{user.pk}/{token}/")
        send_password_reset_email(user, reset_url)


class PasswordResetConfirmSerializer(serializers.Serializer):
    """Serializer for password reset confirmation endpoint."""
    token = serializers.CharField(required=True)
    uid = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)

    def validate(self, attrs):
        """Validate reset token and password."""
        from django.contrib.auth.tokens import default_token_generator
        try:
            uid = int(attrs['uid'])
            user = User.objects.get(pk=uid)
            if not user.is_active:
                msg = _('This account is inactive.')
                raise serializers.ValidationError({'uid': msg})
            if not default_token_generator.check_token(user, attrs['token']):
                msg = _('Invalid or expired reset token.')
                raise serializers.ValidationError({'token': msg})
            setattr(self, '_user', user)
        except (TypeError, ValueError, User.DoesNotExist) as e:
            msg = _('Invalid user ID.')
            raise serializers.ValidationError({'uid': msg}) from e

        try:
            validate_password(attrs['new_password'], user)
        except ValidationError as e:
            raise serializers.ValidationError({
                'new_password': list(e.messages)
            })

        return attrs

    def create(self, validated_data):
        raise NotImplementedError('Use save() instead.')

    def update(self, instance, validated_data):
        raise NotImplementedError('Use save() instead.')

    def save(self):
        """Reset user password."""
        user = getattr(self, '_user')
        user.set_password(self.validated_data['new_password'])
        user.save()

        # Invalidate all existing sessions
        UserSession.objects.filter(user=user, is_active=True).update(
            is_active=False
        )
