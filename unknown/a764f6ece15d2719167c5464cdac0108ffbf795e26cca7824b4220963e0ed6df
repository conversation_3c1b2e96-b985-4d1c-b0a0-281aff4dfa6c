"""
Views for the core app.
"""
from django.shortcuts import render
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Count, Sum
from django.utils.translation import gettext_lazy as _


class DashboardView(LoginRequiredMixin, TemplateView):
    """Main dashboard view."""
    template_name = 'core/dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Import here to avoid circular imports
        from apps.crm.models import Client
        from apps.reservations.models import Reservation
        from apps.finance.models import Invoice
        
        # Dashboard statistics
        context.update({
            'total_clients': Client.objects.filter(is_deleted=False).count(),
            'active_reservations': Reservation.objects.filter(
                status='confirmed',
                is_deleted=False
            ).count(),
            'pending_invoices': Invoice.objects.filter(
                status='pending',
                is_deleted=False
            ).count(),
            'recent_clients': Client.objects.filter(is_deleted=False).order_by('-created_at')[:5],
            'recent_reservations': Reservation.objects.filter(is_deleted=False).order_by('-created_at')[:5],
        })
        
        return context


class SettingsView(LoginRequiredMixin, TemplateView):
    """System settings view."""
    template_name = 'core/settings.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from .models import SystemSettings
        
        context['settings'] = SystemSettings.objects.filter(is_active=True)
        return context
