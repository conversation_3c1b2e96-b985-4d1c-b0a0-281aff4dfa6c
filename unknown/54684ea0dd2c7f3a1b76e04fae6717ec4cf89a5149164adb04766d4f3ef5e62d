"""
Management command to create sample clients for the CRM system.
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from apps.crm.models import Client
from apps.core.models import Country
from apps.accounts.models import User
import random
from datetime import date, timedelta


class Command(BaseCommand):
    help = 'Create sample clients for the CRM system'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('بدء إنشاء عملاء تجريبيين...'))
        
        with transaction.atomic():
            self.create_sample_clients()
        
        self.stdout.write(self.style.SUCCESS('تم إنشاء العملاء التجريبيين بنجاح!'))

    def create_sample_clients(self):
        """Create sample clients."""
        morocco = Country.objects.get(code='MA')
        france = Country.objects.get(code='FR')
        spain = Country.objects.get(code='ES')
        
        # Get sales agent
        sales_agent = User.objects.filter(role='sales').first()
        
        clients_data = [
            {
                'first_name_ar': 'أحمد',
                'last_name_ar': 'بن علي',
                'first_name_fr': '<PERSON>',
                'last_name_fr': '<PERSON> <PERSON>',
                'email': '<EMAIL>',
                'phone': '+212661234567',
                'whatsapp': '+212661234567',
                'gender': 'male',
                'date_of_birth': date(1985, 3, 15),
                'nationality': morocco,
                'passport_number': 'M1234567',
                'passport_expiry': date(2028, 3, 15),
                'address': 'حي الرياض، الرباط',
                'client_type': 'individual',
                'preferred_language': 'ar',
                'assigned_agent': sales_agent,
                'loyalty_points': 150,
                'vip_status': False,
                'marketing_consent': True,
                'newsletter_subscription': True,
                'notes': 'عميل مهتم بالسياحة الثقافية'
            },
            {
                'first_name_ar': 'فاطمة',
                'last_name_ar': 'الزهراء',
                'first_name_fr': 'Fatima',
                'last_name_fr': 'Zahra',
                'email': '<EMAIL>',
                'phone': '+212662345678',
                'whatsapp': '+212662345678',
                'gender': 'female',
                'date_of_birth': date(1990, 7, 22),
                'nationality': morocco,
                'passport_number': 'M2345678',
                'passport_expiry': date(2027, 7, 22),
                'address': 'حي المعاريف، الدار البيضاء',
                'client_type': 'individual',
                'preferred_language': 'ar',
                'assigned_agent': sales_agent,
                'loyalty_points': 300,
                'vip_status': True,
                'marketing_consent': True,
                'newsletter_subscription': True,
                'notes': 'عميلة VIP تفضل الرحلات الفاخرة'
            },
            {
                'first_name_ar': 'محمد',
                'last_name_ar': 'الإدريسي',
                'first_name_fr': 'Mohammed',
                'last_name_fr': 'Idrissi',
                'email': '<EMAIL>',
                'phone': '+212663456789',
                'whatsapp': '+212663456789',
                'gender': 'male',
                'date_of_birth': date(1978, 12, 10),
                'nationality': morocco,
                'passport_number': 'M3456789',
                'passport_expiry': date(2026, 12, 10),
                'address': 'المدينة القديمة، فاس',
                'client_type': 'individual',
                'preferred_language': 'ar',
                'assigned_agent': sales_agent,
                'loyalty_points': 75,
                'vip_status': False,
                'marketing_consent': True,
                'newsletter_subscription': False,
                'notes': 'يفضل الرحلات العائلية'
            },
            {
                'first_name_ar': 'جان',
                'last_name_ar': 'دوبون',
                'first_name_fr': 'Jean',
                'last_name_fr': 'Dupont',
                'email': '<EMAIL>',
                'phone': '+33123456789',
                'whatsapp': '+33123456789',
                'gender': 'male',
                'date_of_birth': date(1982, 5, 8),
                'nationality': france,
                'passport_number': 'F1234567',
                'passport_expiry': date(2029, 5, 8),
                'address': 'Paris, France',
                'client_type': 'individual',
                'preferred_language': 'fr',
                'assigned_agent': sales_agent,
                'loyalty_points': 200,
                'vip_status': False,
                'marketing_consent': True,
                'newsletter_subscription': True,
                'notes': 'Intéressé par le tourisme culturel au Maroc'
            },
            {
                'first_name_ar': 'ماريا',
                'last_name_ar': 'غارسيا',
                'first_name_fr': 'Maria',
                'last_name_fr': 'Garcia',
                'email': '<EMAIL>',
                'phone': '+34987654321',
                'whatsapp': '+34987654321',
                'gender': 'female',
                'date_of_birth': date(1988, 9, 18),
                'nationality': spain,
                'passport_number': 'S1234567',
                'passport_expiry': date(2027, 9, 18),
                'address': 'Madrid, Spain',
                'client_type': 'individual',
                'preferred_language': 'en',
                'assigned_agent': sales_agent,
                'loyalty_points': 120,
                'vip_status': False,
                'marketing_consent': True,
                'newsletter_subscription': True,
                'notes': 'Interested in desert tours and adventure tourism'
            },
            {
                'first_name_ar': 'شركة',
                'last_name_ar': 'الأطلس للسياحة',
                'first_name_fr': 'Atlas',
                'last_name_fr': 'Tourism Company',
                'email': '<EMAIL>',
                'phone': '+212664567890',
                'whatsapp': '+212664567890',
                'gender': 'other',
                'date_of_birth': None,
                'nationality': morocco,
                'passport_number': '',
                'passport_expiry': None,
                'address': 'شارع محمد الخامس، مراكش',
                'client_type': 'corporate',
                'company_name': 'شركة الأطلس للسياحة',
                'tax_number': 'TAX123456',
                'preferred_language': 'ar',
                'assigned_agent': sales_agent,
                'loyalty_points': 500,
                'vip_status': True,
                'marketing_consent': True,
                'newsletter_subscription': True,
                'notes': 'شركة سياحة تتعامل مع مجموعات كبيرة'
            }
        ]
        
        for client_data in clients_data:
            # Generate unique client code
            client_data['client_code'] = self.generate_client_code()
            
            client, created = Client.objects.get_or_create(
                email=client_data['email'],
                defaults=client_data
            )
            if created:
                self.stdout.write(f'تم إنشاء عميل: {client.full_name_ar} ({client.client_code})')

    def generate_client_code(self):
        """Generate unique client code."""
        import string
        import random
        
        # Generate code like CL2024001
        year = date.today().year
        random_num = random.randint(1, 999)
        return f"CL{year}{random_num:03d}"
