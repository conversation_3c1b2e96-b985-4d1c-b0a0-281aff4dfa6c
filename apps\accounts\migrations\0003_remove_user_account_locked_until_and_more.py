# Generated by Django 4.2 on 2025-06-14 08:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('accounts', '0002_user_account_locked_until_user_avatar_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='user',
            name='account_locked_until',
        ),
        migrations.RemoveField(
            model_name='user',
            name='failed_login_attempts',
        ),
        migrations.RemoveField(
            model_name='user',
            name='is_active_employee',
        ),
        migrations.RemoveField(
            model_name='user',
            name='last_activity',
        ),
        migrations.RemoveField(
            model_name='user',
            name='last_password_change',
        ),
        migrations.RemoveField(
            model_name='user',
            name='login_count',
        ),
        migrations.RemoveField(
            model_name='user',
            name='notes',
        ),
        migrations.RemoveField(
            model_name='user',
            name='role',
        ),
        migrations.RemoveField(
            model_name='user',
            name='termination_date',
        ),
        migrations.RemoveField(
            model_name='user',
            name='termination_reason',
        ),
        migrations.RemoveField(
            model_name='user',
            name='two_factor_enabled',
        ),
        migrations.AddField(
            model_name='user',
            name='system_role',
            field=models.CharField(choices=[('admin', 'مدير النظام'), ('manager', 'مدير الوكالة'), ('sales', 'موظف مبيعات'), ('accountant', 'محاسب'), ('guide', 'مرشد سياحي'), ('driver', 'سائق'), ('receptionist', 'موظف استقبال')], default='sales', max_length=20, verbose_name='دور النظام'),
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='اسم الدور')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('permissions', models.ManyToManyField(blank=True, related_name='roles', to='auth.permission', verbose_name='الصلاحيات')),
            ],
            options={
                'verbose_name': 'دور',
                'verbose_name_plural': 'الأدوار',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='user',
            name='roles',
            field=models.ManyToManyField(blank=True, related_name='users', to='accounts.role', verbose_name='الأدوار'),
        ),
    ]
