"""
API URL configuration for HR app.
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import api_views

router = DefaultRouter()
router.register(r'departments', api_views.DepartmentViewSet)
router.register(r'positions', api_views.PositionViewSet)
router.register(r'schedules', api_views.ScheduleViewSet)
router.register(r'leaves', api_views.LeaveViewSet)
router.register(r'attendance', api_views.AttendanceViewSet)
router.register(r'payroll', api_views.PayrollViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('employees/<int:employee_id>/leaves/', api_views.EmployeeLeaveListView.as_view(), name='employee-leaves'),
    path('employees/<int:employee_id>/attendance/', api_views.EmployeeAttendanceListView.as_view(), name='employee-attendance'),
    path('employees/<int:employee_id>/payroll/', api_views.EmployeePayrollListView.as_view(), name='employee-payroll'),
    path('reports/attendance-summary/', api_views.AttendanceSummaryView.as_view(), name='attendance-summary'),
]
