# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: django-allauth\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-07 04:22-0500\n"
"PO-Revision-Date: 2023-07-24 22:25+0200\n"
"Last-Translator: jresins <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: account/adapter.py:48
msgid "Username can not be used. Please use other username."
msgstr "此用户名不能使用，请改用其他用户名。"

#: account/adapter.py:54
msgid "Too many failed login attempts. Try again later."
msgstr "登录失败次数过多，请稍后重试。"

#: account/adapter.py:56
msgid "A user is already registered with this email address."
msgstr "此e-mail地址已被其他用户注册。"

#: account/adapter.py:57
#, fuzzy
#| msgid "Current Password"
msgid "Incorrect password."
msgstr "当前密码"

#: account/adapter.py:308
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "密码长度不得少于 {0} 个字符。"

#: account/apps.py:9
#, fuzzy
msgid "Accounts"
msgstr "账号"

#: account/forms.py:58 account/forms.py:432
msgid "You must type the same password each time."
msgstr "每次输入的密码必须相同"

#: account/forms.py:90 account/forms.py:397 account/forms.py:522
#: account/forms.py:658
msgid "Password"
msgstr "密码"

#: account/forms.py:91
msgid "Remember Me"
msgstr "记住我"

#: account/forms.py:95
msgid "This account is currently inactive."
msgstr "此账号当前未激活。"

#: account/forms.py:97
msgid "The email address and/or password you specified are not correct."
msgstr "您提供的e-mail地址或密码不正确。"

#: account/forms.py:100
msgid "The username and/or password you specified are not correct."
msgstr "您提供的用户名或密码不正确。"

#: account/forms.py:111 account/forms.py:271 account/forms.py:459
#: account/forms.py:540
msgid "Email address"
msgstr "E-mail地址"

#: account/forms.py:115 account/forms.py:316 account/forms.py:456
#: account/forms.py:535
msgid "Email"
msgstr "E-mail"

#: account/forms.py:118 account/forms.py:121 account/forms.py:261
#: account/forms.py:264
msgid "Username"
msgstr "用户名"

#: account/forms.py:131
msgid "Username or email"
msgstr "用户名或e-mail"

#: account/forms.py:134
msgctxt "field label"
msgid "Login"
msgstr "账号"

#: account/forms.py:307
#, fuzzy
#| msgid "Email (optional)"
msgid "Email (again)"
msgstr "E-mail （选填项）"

#: account/forms.py:311
#, fuzzy
#| msgid "email confirmation"
msgid "Email address confirmation"
msgstr "e-mail确认"

#: account/forms.py:319
msgid "Email (optional)"
msgstr "E-mail （选填项）"

#: account/forms.py:368
#, fuzzy
#| msgid "You must type the same password each time."
msgid "You must type the same email each time."
msgstr "每次输入的密码必须相同"

#: account/forms.py:401 account/forms.py:523
msgid "Password (again)"
msgstr "密码（重复）"

#: account/forms.py:470
msgid "This email address is already associated with this account."
msgstr "此e-mail地址已关联到这个账号。"

#: account/forms.py:472
#, fuzzy, python-format
#| msgid "Your account has no verified email address."
msgid "You cannot add more than %d email addresses."
msgstr "您的账号下无任何验证过的e-mail地址。"

#: account/forms.py:503
msgid "Current Password"
msgstr "当前密码"

#: account/forms.py:505 account/forms.py:607
msgid "New Password"
msgstr "新密码"

#: account/forms.py:506 account/forms.py:608
msgid "New Password (again)"
msgstr "新密码（重复）"

#: account/forms.py:514
msgid "Please type your current password."
msgstr "请输入您的当前密码"

#: account/forms.py:552
msgid "The email address is not assigned to any user account"
msgstr "此e-mail地址未分配给任何用户账号"

#: account/forms.py:628
msgid "The password reset token was invalid."
msgstr "重设密码的token不合法。"

#: account/models.py:21
msgid "user"
msgstr "用户"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "e-mail地址"

#: account/models.py:28
msgid "verified"
msgstr "已验证"

#: account/models.py:29
#, fuzzy
msgid "primary"
msgstr "首选e-mail"

#: account/models.py:35
msgid "email addresses"
msgstr "e-mail地址"

#: account/models.py:141
msgid "created"
msgstr "已建立"

#: account/models.py:142
msgid "sent"
msgstr "已发送"

#: account/models.py:143 socialaccount/models.py:62
msgid "key"
msgstr "key"

#: account/models.py:148
msgid "email confirmation"
msgstr "e-mail确认"

#: account/models.py:149
msgid "email confirmations"
msgstr "e-mail确认"

#: mfa/adapter.py:19
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""

#: mfa/adapter.py:22
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""

#: mfa/adapter.py:24
msgid "Incorrect code."
msgstr ""

#: mfa/apps.py:7
msgid "MFA"
msgstr ""

#: mfa/forms.py:12
msgid "Code"
msgstr ""

#: mfa/forms.py:29
msgid "Authenticator code"
msgstr ""

#: mfa/models.py:15
msgid "Recovery codes"
msgstr ""

#: mfa/models.py:16
msgid "TOTP Authenticator"
msgstr ""

#: socialaccount/adapter.py:30
#, fuzzy, python-format
#| msgid ""
#| "An account already exists with this e-mail address. Please sign in to "
#| "that account first, then connect your %s account."
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr "已有一个账号与此e-mail地址关联，请先登录该账号，然后连接你的 %s 账号。"

#: socialaccount/adapter.py:136
msgid "Your account has no password set up."
msgstr "您的账号未设置密码。"

#: socialaccount/adapter.py:143
msgid "Your account has no verified email address."
msgstr "您的账号下无任何验证过的e-mail地址。"

#: socialaccount/apps.py:7
#, fuzzy
msgid "Social Accounts"
msgstr "账号"

#: socialaccount/models.py:36 socialaccount/models.py:97
msgid "provider"
msgstr "提供商"

#: socialaccount/models.py:45
#, fuzzy
#| msgid "provider"
msgid "provider ID"
msgstr "提供商"

#: socialaccount/models.py:49
#, fuzzy
msgid "name"
msgstr "用户名"

#: socialaccount/models.py:51
msgid "client id"
msgstr "客户端 id"

#: socialaccount/models.py:53
msgid "App ID, or consumer key"
msgstr ""

#: socialaccount/models.py:56
msgid "secret key"
msgstr ""

#: socialaccount/models.py:59
msgid "API secret, client secret, or consumer secret"
msgstr ""

#: socialaccount/models.py:62
#, fuzzy
msgid "Key"
msgstr "key"

#: socialaccount/models.py:81
msgid "social application"
msgstr ""

#: socialaccount/models.py:82
msgid "social applications"
msgstr ""

#: socialaccount/models.py:117
msgid "uid"
msgstr ""

#: socialaccount/models.py:119
msgid "last login"
msgstr "最后登录"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "注册日期"

#: socialaccount/models.py:121
msgid "extra data"
msgstr ""

#: socialaccount/models.py:125
msgid "social account"
msgstr "社交账号"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "社交账号"

#: socialaccount/models.py:160
msgid "token"
msgstr ""

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr ""

#: socialaccount/models.py:165
msgid "token secret"
msgstr ""

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr ""

#: socialaccount/models.py:169
msgid "expires at"
msgstr ""

#: socialaccount/models.py:174
msgid "social application token"
msgstr ""

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr ""

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr ""

#: socialaccount/providers/oauth/client.py:85
#, fuzzy, python-format
#| msgid "Invalid response while obtaining request token from \"%s\"."
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "Invalid response while obtaining request token from \"%s\"."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:78
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Invalid response while obtaining access token from \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "No request token saved for \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "No access token saved for \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "无权访问私有资源 \"%s\"。"

#: socialaccount/providers/pocket/client.py:37
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Invalid response while obtaining request token from \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:8
msgid "Account Inactive"
msgstr "账号未激活"

#: templates/account/account_inactive.html:10
msgid "This account is inactive."
msgstr "此账号未激活"

#: templates/account/base.html:16
msgid "Messages:"
msgstr ""

#: templates/account/base.html:26
msgid "Menu:"
msgstr ""

#: templates/account/base.html:29 templates/account/email_change.html:31
#, fuzzy
#| msgid "Email"
msgid "Change Email"
msgstr "E-mail"

#: templates/account/base.html:30 templates/account/logout.html:5
#: templates/account/logout.html:8 templates/account/logout.html:17
msgid "Sign Out"
msgstr "注销"

#: templates/account/base.html:32 templates/account/login.html:6
#: templates/account/login.html:10 templates/account/login.html:43
#: templates/mfa/authenticate.html:4 templates/mfa/authenticate.html:16
#: templates/socialaccount/login.html:4
msgid "Sign In"
msgstr "登录"

#: templates/account/base.html:33 templates/account/signup.html:8
#: templates/account/signup.html:18 templates/socialaccount/signup.html:8
#: templates/socialaccount/signup.html:19
msgid "Sign Up"
msgstr "注册"

#: templates/account/email.html:5 templates/account/email.html:8
msgid "Email Addresses"
msgstr "E-mail地址"

#: templates/account/email.html:10
msgid "The following email addresses are associated with your account:"
msgstr "以下e-mail地址已关联到您的帐号："

#: templates/account/email.html:24
msgid "Verified"
msgstr "已验证"

#: templates/account/email.html:26
msgid "Unverified"
msgstr "未验证"

#: templates/account/email.html:28
msgid "Primary"
msgstr "首选e-mail"

#: templates/account/email.html:34
msgid "Make Primary"
msgstr "设置首选e-mail"

#: templates/account/email.html:35 templates/account/email_change.html:21
msgid "Re-send Verification"
msgstr "重发验证e-mail"

#: templates/account/email.html:36 templates/socialaccount/connections.html:35
msgid "Remove"
msgstr "移除"

#: templates/account/email.html:47
msgid "Add Email Address"
msgstr "添加E-mail地址"

#: templates/account/email.html:52
msgid "Add Email"
msgstr "添加E-mail"

#: templates/account/email.html:62
msgid "Do you really want to remove the selected email address?"
msgstr "您真的想移除选定的e-mail地址吗？"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr ""

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr ""

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "User %(user_display)s at %(site_name)s has given this as an email "
#| "address.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s\n"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"网站%(site_name)s上的用户%(user_display)s将此设为其e-mail地址。\n"
"\n"
"为验证这是正确的，请访问%(activate_url)s\n"

#: templates/account/email/email_confirmation_subject.txt:3
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Please Confirm Your Email Address"
msgstr "确认E-mail地址"

#: templates/account/email/password_reset_key_message.txt:4
#, fuzzy
#| msgid ""
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account at %(site_domain)s.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"您收到此邮件表示您或者他人在网站 %(site_domain)s上为您的账号请求了密码重"
"置。\n"
"若您未请求密码重置，可以直接忽略此邮件。如要重置密码，请点击下面的链接。"

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "作为提示，您的用户名是%(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "密码重置邮件"

#: templates/account/email/unknown_account_message.txt:4
#, fuzzy, python-format
#| msgid ""
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account at %(site_domain)s.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"您收到此邮件表示您或者他人在网站 %(site_domain)s上为您的账号请求了密码重"
"置。\n"
"若您未请求密码重置，可以直接忽略此邮件。如要重置密码，请点击下面的链接。"

#: templates/account/email_change.html:4 templates/account/email_change.html:7
#, fuzzy
#| msgid "Email Addresses"
msgid "Email Address"
msgstr "E-mail地址"

#: templates/account/email_change.html:11
#, fuzzy
#| msgid "The following email addresses are associated with your account:"
msgid "The following email address is associated with your account:"
msgstr "以下e-mail地址已关联到您的帐号："

#: templates/account/email_change.html:16
#, fuzzy
#| msgid "Your email address has already been verified"
msgid "Your email address is still pending verification:"
msgstr "您的e-mail地址已经认证。"

#: templates/account/email_change.html:27
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Change Email Address"
msgstr "确认E-mail地址"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "确认E-mail地址"

#: templates/account/email_confirm.html:17
#, fuzzy, python-format
#| msgid ""
#| "Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an e-"
#| "mail address for user %(user_display)s."
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"请确认<a href=\"mailto:%(email)s\">%(email)s</a>是否是用户 %(user_display)s的"
"e-mail地址。"

#: templates/account/email_confirm.html:21
#: templates/account/reauthenticate.html:19
msgid "Confirm"
msgstr "确认"

#: templates/account/email_confirm.html:24
#: templates/account/messages/email_confirmation_failed.txt:2
#, fuzzy, python-format
#| msgid "The social account is already connected to a different account."
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "社交账号已连接到另一个账号。"

#: templates/account/email_confirm.html:31
#, fuzzy, python-format
#| msgid ""
#| "This e-mail confirmation link expired or is invalid. Please <a href="
#| "\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgid ""
"This email confirmation link expired or is invalid. Please <a href="
"\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"e-mail验证链接失效或无效。请点击 <a href=\"%(email_url)s\">发起新的e-mail验证"
"请求。"

#: templates/account/login.html:15
#, fuzzy, python-format
msgid ""
"Please sign in with one\n"
"of your existing third party accounts. Or, <a href=\"%(signup_url)s\">sign "
"up</a>\n"
"for a %(site_name)s account and sign in below:"
msgstr ""
"请用您的第三方账号登录。\n"
"或者<a href=\"%(signup_url)s\">注册</a> \n"
"一个 %(site_name)s账号并登录："

#: templates/account/login.html:25
msgid "or"
msgstr "或"

#: templates/account/login.html:32
#, python-format
msgid ""
"If you have not created an account yet, then please\n"
"<a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"如果没有账号，请先\n"
"<a href=\"%(signup_url)s\">注册</a> 。"

#: templates/account/login.html:42 templates/account/password_change.html:14
msgid "Forgot Password?"
msgstr "忘记密码了？"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "您确定要注销登录吗？"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "您不能删除您的主e-mail地址 (%(email)s) "

#: templates/account/messages/email_confirmation_sent.txt:2
#, fuzzy, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "确认e-mail已发往 %(email)s"

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "您已确认e-mail地址 %(email)s "

#: templates/account/messages/email_deleted.txt:2
#, fuzzy, python-format
msgid "Removed email address %(email)s."
msgstr "e-mail地址 %(email)s 已删除"

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "以 %(name)s..身份成功登录"

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "您已注销登录。"

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "密码修改成功。"

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "密码设置成功。"

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "主e-mail地址已设置"

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "您的主e-mail地址必须被验证。"

#: templates/account/password_change.html:5
#: templates/account/password_change.html:8
#: templates/account/password_change.html:13
#: templates/account/password_reset_from_key.html:4
#: templates/account/password_reset_from_key.html:7
#: templates/account/password_reset_from_key_done.html:4
#: templates/account/password_reset_from_key_done.html:7
msgid "Change Password"
msgstr "修改密码"

#: templates/account/password_reset.html:6
#: templates/account/password_reset.html:10
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:9
msgid "Password Reset"
msgstr "密码重置"

#: templates/account/password_reset.html:15
#, fuzzy
#| msgid ""
#| "Forgotten your password? Enter your e-mail address below, and we'll send "
#| "you an e-mail allowing you to reset it."
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"忘记密码？在下面输入您的e-mail地址，我们将给您发送一封e-mail，以便重置密码。"

#: templates/account/password_reset.html:20
msgid "Reset My Password"
msgstr "重置我的密码"

#: templates/account/password_reset.html:23
msgid "Please contact us if you have any trouble resetting your password."
msgstr "如在重置密码时遇到问题，请与我们联系。"

#: templates/account/password_reset_done.html:15
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"我们已经给您发送了一封e-mail验证邮件。\n"
"请点击e-mail中的链接。若您在几分钟后仍未收到邮件，请联系我们。"

#: templates/account/password_reset_from_key.html:7
msgid "Bad Token"
msgstr "Bad Token"

#: templates/account/password_reset_from_key.html:11
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"密码重置链接无效，可能该链接已被使用。请重新申请<a href="
"\"%(passwd_reset_url)s\">链接重置</a>。"

#: templates/account/password_reset_from_key.html:16
msgid "change password"
msgstr "修改密码"

#: templates/account/password_reset_from_key_done.html:8
msgid "Your password is now changed."
msgstr "您的密码现已被修改。"

#: templates/account/password_set.html:5 templates/account/password_set.html:8
#: templates/account/password_set.html:13
msgid "Set Password"
msgstr "设置密码"

#: templates/account/reauthenticate.html:5
#: templates/account/reauthenticate.html:9
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Confirm Access"
msgstr "确认E-mail地址"

#: templates/account/reauthenticate.html:11
msgid "To safeguard the security of your account, please enter your password:"
msgstr ""

#: templates/account/signup.html:5 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "注册"

#: templates/account/signup.html:10
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr "已经有一个账号？ 请<a href=\"%(login_url)s\">登录</a>."

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:8
msgid "Sign Up Closed"
msgstr "已关闭注册"

#: templates/account/signup_closed.html:10
msgid "We are sorry, but the sign up is currently closed."
msgstr "非常抱歉，当前已关闭注册。"

#: templates/account/snippets/already_logged_in.html:5
msgid "Note"
msgstr "注意"

#: templates/account/snippets/already_logged_in.html:5
#, python-format
msgid "you are already logged in as %(user_display)s."
msgstr "您已以 %(user_display)s的身份登录"

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "警告："

#: templates/account/snippets/warn_no_email.html:3
#, fuzzy
#| msgid ""
#| "You currently do not have any e-mail address set up. You should really "
#| "add an e-mail address so you can receive notifications, reset your "
#| "password, etc."
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"您当前未设置任何邮件地址。您需要设置一个邮件地址，以便接收通知，重置密码等。"

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:8
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:8
msgid "Verify Your Email Address"
msgstr "验证您的E-mail地址。"

#: templates/account/verification_sent.html:10
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for verification. Follow the link provided "
#| "to finalize the signup process. Please contact us if you do not receive "
#| "it within a few minutes."
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"我们已向您发了一封验证e-mail。点击e-mail中的链接完成注册流程。如果您在几分钟"
"后仍未收到邮件，请与我们联系。"

#: templates/account/verified_email_required.html:12
#, fuzzy
#| msgid ""
#| "This part of the site requires us to verify that\n"
#| "you are who you claim to be. For this purpose, we require that you\n"
#| "verify ownership of your e-mail address. "
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"网站的这部分功能要求验证您的真实身份。\n"
"为此，我们需要您确认您是此账号e-mail地址的所有者。"

#: templates/account/verified_email_required.html:16
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"我们已经给您发送了一封e-mail验证邮件。\n"
"请点击e-mail中的链接。若您在几分钟后仍未收到邮件，请联系我们。"

#: templates/account/verified_email_required.html:20
#, fuzzy, python-format
#| msgid ""
#| "<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change "
#| "your e-mail address</a>."
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>注意:</strong> 您仍然能够<a href=\"%(email_url)s\">修改您的e-mail地"
"址 </a>."

#: templates/mfa/authenticate.html:7 templates/mfa/index.html:4
#: templates/mfa/index.html:7
msgid "Two-Factor Authentication"
msgstr ""

#: templates/mfa/authenticate.html:9
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""

#: templates/mfa/index.html:9 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr ""

#: templates/mfa/index.html:11
msgid "Authentication using an authenticator app is active."
msgstr ""

#: templates/mfa/index.html:14 templates/mfa/totp/deactivate_form.html:11
msgid "Deactivate"
msgstr ""

#: templates/mfa/index.html:18
msgid "An authenticator app is not active."
msgstr ""

#: templates/mfa/index.html:21 templates/mfa/totp/activate_form.html:16
msgid "Activate"
msgstr ""

#: templates/mfa/index.html:27 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:4
#: templates/mfa/recovery_codes/index.html:4
msgid "Recovery Codes"
msgstr ""

#: templates/mfa/index.html:30 templates/mfa/recovery_codes/index.html:6
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
msgstr[1] ""

#: templates/mfa/index.html:34
msgid "View codes"
msgstr ""

#: templates/mfa/index.html:37 templates/mfa/recovery_codes/index.html:16
msgid "Download codes"
msgstr ""

#: templates/mfa/index.html:40 templates/mfa/recovery_codes/index.html:20
msgid "Generate new codes"
msgstr ""

#: templates/mfa/index.html:44
msgid "No recovery codes set up."
msgstr ""

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr ""

#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr ""

#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:6
msgid ""
"You are about to generate a new set of recovery codes for your account. This "
"action will invalidate your existing codes. Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/generate.html:11
msgid "Generate"
msgstr ""

#: templates/mfa/totp/activate_form.html:4
msgid "Activate Authenticator App"
msgstr ""

#: templates/mfa/totp/activate_form.html:9
msgid "Authenticator secret"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:4
msgid "Deactivate Authenticator App"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:6
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""

#: templates/openid/login.html:9
msgid "OpenID Sign In"
msgstr "OpenID登录"

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:8
msgid "Social Network Login Failure"
msgstr "社交网络登录失败"

#: templates/socialaccount/authentication_error.html:10
msgid ""
"An error occurred while attempting to login via your social network account."
msgstr "当尝试用您的社交网络账号登录时，发生了一个错误。"

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:8
msgid "Account Connections"
msgstr "账号链接"

#: templates/socialaccount/connections.html:11
msgid ""
"You can sign in to your account using any of the following third party "
"accounts:"
msgstr "您可以用您的以下任何第三方账号登录"

#: templates/socialaccount/connections.html:43
msgid ""
"You currently have no social network accounts connected to this account."
msgstr "您当前没有任何社交网络账号与此账号关联"

#: templates/socialaccount/connections.html:46
msgid "Add a 3rd Party Account"
msgstr "添加一个第三方账号"

#: templates/socialaccount/login.html:8
#, python-format
msgid "Connect %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:10
#, python-format
msgid "You are about to connect a new third party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:12
#, python-format
msgid "Sign In Via %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:14
#, python-format
msgid "You are about to sign in using a third party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:19
msgid "Continue"
msgstr ""

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "登录已取消"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a href=\"%(login_url)s"
"\">sign in</a>."
msgstr ""
"您决定取消使用您的已有账号登录我们的网站。如果这是一个失误，请继续<a href="
"\"%(login_url)s\">登录</a>."

#: templates/socialaccount/messages/account_connected.txt:2
#, fuzzy
msgid "The social account has been connected."
msgstr "社交账号已连接"

#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The social account is already connected to a different account."
msgstr "社交账号已连接到另一个账号。"

#: templates/socialaccount/messages/account_disconnected.txt:2
#, fuzzy
msgid "The social account has been disconnected."
msgstr "社交账号已断开连接"

#: templates/socialaccount/signup.html:10
#, fuzzy, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"您将使用您的%(provider_name)s账号登录\n"
"%(site_name)s。作为最后一步，请完成以下表单："

#~ msgid "This email address is already associated with another account."
#~ msgstr "此e-mail地址已关联到其他账号。"

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr "我们已给您发了一封e-mail，如您在几分钟后仍没收到，请与我们联系。"

#~ msgid "Account"
#~ msgstr "账号"

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "您提供的账号或密码错误"

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr "用户名只能包含字母，数字和@/./+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "此用户名已被使用，请选择其他用户名。"

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "登录"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "您已确认<a href=\"mailto:%(email)s\">%(email)s</a>是用户%(user_display)s的"
#~ "e-mail地址。"

#~ msgid "Thanks for using our site!"
#~ msgstr "感谢您使用我们的网站！"

#~ msgid "Confirmation email sent to %(email)s"
#~ msgstr "确认e-mail已发往 %(email)s"

#~ msgid "Delete Password"
#~ msgstr "删除密码"

#~ msgid ""
#~ "You may delete your password since you are currently logged in using "
#~ "OpenID."
#~ msgstr "您当前使用OpenID登录，因此您可以删除你的密码。"

#~ msgid "delete my password"
#~ msgstr "删除我的密码"

#~ msgid "Password Deleted"
#~ msgstr "密码已删除"

#~ msgid "Your password has been deleted."
#~ msgstr "您的密码已被删除。"

#~ msgid ""
#~ "If you have any trouble resetting your password, contact us at <a href="
#~ "\"mailto:%(CONTACT_EMAIL)s\">%(CONTACT_EMAIL)s</a>."
#~ msgstr ""
#~ "如果您在重置密码中遇到任何问题，请联系我们<a href=\"mailto:"
#~ "%(CONTACT_EMAIL)s\">%(CONTACT_EMAIL)s</a>."

#~ msgid "Invalid confirmation key."
#~ msgstr "不合法的确认密匙"

#~ msgid "OpenID"
#~ msgstr "OpenID"

#~ msgid "Already have an account?"
#~ msgstr "已经有账户了?"

#~ msgid "Sign in"
#~ msgstr "注册"

#~ msgid "Language"
#~ msgstr "语言"

#~ msgid "Pinax can be used in your preferred language."
#~ msgstr "Pinax可以在您的首选语言中使用。"

#~ msgid "Change my language"
#~ msgstr "切换我的语言"

#~ msgid "Timezone"
#~ msgstr "时区"

#, fuzzy
#~ msgid ""
#~ "You're receiving this e-mail because you requested a password reset\n"
#~ "for your user account at Pinax.\n"
#~ "\n"
#~ "Your new password is: %(new_password)s\n"
#~ "\n"
#~ "Your username, in case you've forgotten: %(username)s\n"
#~ "\n"
#~ "You should log in as soon as possible and change your password.\n"
#~ "\n"
#~ "Thanks for using our site!\n"
#~ msgstr ""
#~ "Je ontvangt deze mail omdat er een verzoek is ingelegd om het wachtwoord\n"
#~ "behorende bij je %(site_name)s account opnieuw in te stellen.\n"
#~ "\n"
#~ "Je nieuwe wachtwoord is: %(new_password)s\n"
#~ "\n"
#~ "Je gebruikersnaam, voor het geval je die vergeten bent, is: %(username)s\n"
#~ "\n"
#~ "Je moet zo snel mogelijk inloggen en bovenstaand wachtwoord veranderen.\n"
#~ "\n"
#~ "Bedankt voor het gebruik van onze site!\n"

#~ msgid "If checked you will stay logged in for 3 weeks"
#~ msgstr "Bij 'Onthouden' blijf je ingelogd gedurende 3 weken"

#~ msgid "Timezone successfully updated."
#~ msgstr "Tijdzone gewijzigd."

#~ msgid "Language successfully updated."
#~ msgstr "Taal gewijzigd."

#~ msgid "None"
#~ msgstr "Geen"

#~ msgid "add"
#~ msgstr "Voeg toe"

#~ msgid "Log In"
#~ msgstr "Inloggen"

#~ msgid "Log in"
#~ msgstr "Inloggen"

#~ msgid "Logout"
#~ msgstr "Afmelden"

#~ msgid ""
#~ "When you receive the new password, you should <a href=\"%(login_url)s"
#~ "\">log in</a> and change it as soon as possible."
#~ msgstr ""
#~ "Zodra je het nieuwe wachtwoord ontvangen hebt moet je zo snel mogelijk <a "
#~ "href=\"%(login_url)s\">inloggen</a> en het wachtwoord wijzigen."

#~ msgid "You are already logged in."
#~ msgstr "Je bent al ingelogd."

#~ msgid ""
#~ "By clicking \"Sign Up\", you are indicating that you have read and agree "
#~ "to the <a href=\"%(terms_url)s\">Terms of Use</a> and <a href="
#~ "\"%(privacy_url)s\">Privacy Policy</a>."
#~ msgstr ""
#~ "Door te registreren geef je aan dat je de <a href=\"%(terms_url)s"
#~ "\">gebruiksvoorwaarden</a> en de <a href=\"%(privacy_url)s\">privacy "
#~ "policy</a> gelezen hebt en ermee akkoord gaat."

#~ msgid ""
#~ "If you have any trouble creating your account, contact us at <a href="
#~ "\"mailto:%(contact_email)s\">%(contact_email)s</a>."
#~ msgstr ""
#~ "Als je problemen hebt om een account aan te maken, neem dan contact op "
#~ "met <a href=\"mailto:%(contact_email)s\">%(contact_email)s</a>."

#~ msgid "Log in &raquo;"
#~ msgstr "Inloggen"
