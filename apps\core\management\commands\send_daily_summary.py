"""
Management command to send daily summary report.
"""
from django.core.management.base import BaseCommand
from apps.core.notifications import NotificationManager


class Command(BaseCommand):
    help = 'Send daily summary report to managers'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('📧 إرسال التقرير اليومي...'))

        try:
            NotificationManager.send_daily_summary()
            self.stdout.write(self.style.SUCCESS('✅ تم إرسال التقرير اليومي بنجاح!'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ فشل في إرسال التقرير: {e}'))
