"""
Views for advanced reporting and analytics in the Moroccan Travel Agency ERP system.
"""
from django.shortcuts import render
from django.views.generic import TemplateView
from django.db.models import Count, Sum, Avg, Q, F, ExpressionWrapper, DecimalField
from django.db.models.functions import <PERSON><PERSON><PERSON>Mont<PERSON>, ExtractYear
from django.utils import timezone
from datetime import datetime, timedelta
from apps.crm.models import Client
from apps.tours.models import TourPackage, Destination, TourCategory
from apps.accounts.models import User
from apps.core.models import Country, City
from apps.finance.models import Invoice, Payment
from apps.reservations.models import Reservation


class ReportsView(TemplateView):
    """Main reports dashboard with comprehensive analytics."""
    template_name = 'reports/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Date ranges for analytics
        today = timezone.now().date()
        last_30_days = today - timedelta(days=30)
        last_7_days = today - timedelta(days=7)
        current_year = today.year

        # Basic client statistics
        context.update({
            'total_clients': Client.objects.filter(is_deleted=False).count(),
            'new_clients_30_days': Client.objects.filter(created_at__gte=last_30_days, is_deleted=False).count(),
            'new_clients_7_days': Client.objects.filter(created_at__gte=last_7_days, is_deleted=False).count(),
            'vip_clients': Client.objects.filter(vip_status=True, is_deleted=False).count(),
            'individual_clients': Client.objects.filter(client_type='individual', is_deleted=False).count(),
            'corporate_clients': Client.objects.filter(client_type='corporate', is_deleted=False).count(),
        })

        # Package and destination statistics
        context.update({
            'total_packages': TourPackage.objects.filter(is_deleted=False).count(),
            'active_packages': TourPackage.objects.filter(is_active=True, is_deleted=False).count(),
            'featured_packages': TourPackage.objects.filter(is_featured=True, is_active=True, is_deleted=False).count(),
            'total_destinations': Destination.objects.filter(is_deleted=False).count(),
            'total_categories': TourCategory.objects.filter(is_deleted=False).count(),
        })

        # Client demographics and trends
        client_by_country = (Client.objects
            .filter(is_deleted=False)
            .values('nationality__name_ar')
            .annotate(count=Count('id'))
            .order_by('-count')[:5]
        )

        client_growth = (Client.objects
            .filter(is_deleted=False)
            .annotate(month=TruncMonth('created_at'))
            .values('month')
            .annotate(count=Count('id'))
            .order_by('month')
            .values('month', 'count')
        )

        # Package analytics
        package_by_category = (TourPackage.objects
            .filter(is_deleted=False)
            .values('category__name_ar')
            .annotate(count=Count('id'))
            .order_by('-count')
        )

        top_packages = (TourPackage.objects
            .filter(is_deleted=False)
            .annotate(
                total_bookings=Count('reservations'),
                total_revenue=Sum(
                    ExpressionWrapper(
                        F('base_price') * F('reservations__number_of_people'),
                        output_field=DecimalField()
                    )
                )
            )
            .order_by('-total_bookings')[:5]
        )

        # Financial analytics
        financial_summary = {
            'total_revenue': Invoice.objects.filter(
                created_at__year=current_year,
                is_deleted=False,
                status='paid'
            ).aggregate(total=Sum('total_amount'))['total'] or 0,

            'pending_revenue': Invoice.objects.filter(
                status='pending',
                is_deleted=False
            ).aggregate(total=Sum('total_amount'))['total'] or 0,

            'average_booking_value': Invoice.objects.filter(
                created_at__year=current_year,
                is_deleted=False,
                status='paid'
            ).aggregate(avg=Avg('total_amount'))['avg'] or 0
        }

        # Recent activity
        context.update({
            'recent_clients': Client.objects.filter(
                is_deleted=False
            ).order_by('-created_at')[:5],

            'recent_packages': TourPackage.objects.filter(
                is_deleted=False,
                is_active=True
            ).order_by('-created_at')[:5]
        })

        # Add analytics to context
        context.update({
            'client_by_country': client_by_country,
            'client_growth': list(client_growth),
            'package_by_category': package_by_category,
            'top_packages': top_packages,
            'financial_summary': financial_summary,
            'system_info': {
                'total_users': User.objects.filter(is_active=True).count(),
                'active_users': User.objects.filter(is_active=True, last_login__gte=last_30_days).count(),
                'total_countries': Country.objects.count(),
                'total_cities': City.objects.count(),
            }
        })

        return context


class ClientReportsView(TemplateView):
    """Detailed client analytics and reports."""
    template_name = 'reports/clients.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        today = timezone.now().date()

        # Client acquisition analysis
        acquisition_data = (Client.objects
            .filter(is_deleted=False)
            .annotate(month=TruncMonth('created_at'))
            .values('month')
            .annotate(
                new_clients=Count('id'),
                vip_clients=Count('id', filter=Q(vip_status=True))
            )
            .order_by('month')
        )

        # Client demographics
        demographics = {
            'by_nationality': (Client.objects
                .filter(is_deleted=False)
                .values('nationality__name_ar')
                .annotate(count=Count('id'))
                .order_by('-count')
            ),
            'by_city': (Client.objects
                .filter(is_deleted=False)
                .values('city__name_ar')
                .annotate(count=Count('id'))
                .order_by('-count')
            )
        }

        # Client activity and engagement
        activity = {
            'total_bookings': (Client.objects
                .filter(is_deleted=False)
                .annotate(booking_count=Count('reservations'))
                .aggregate(total=Sum('booking_count'))
            ),
            'average_booking_value': (Invoice.objects
                .filter(is_deleted=False)
                .values('client')
                .annotate(avg_value=Avg('total_amount'))
                .aggregate(overall_avg=Avg('avg_value'))
            )
        }

        context.update({
            'acquisition_data': list(acquisition_data),
            'demographics': demographics,
            'activity': activity
        })

        return context


class PackageReportsView(TemplateView):
    """Detailed package performance analytics."""
    template_name = 'reports/packages.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Package performance metrics
        performance = (TourPackage.objects
            .filter(is_deleted=False)
            .annotate(
                booking_count=Count('reservations'),
                total_revenue=Sum('reservations__total_amount'),
                avg_rating=Avg('reviews__rating')
            )
            .order_by('-booking_count')
        )

        # Category analysis
        category_metrics = (TourCategory.objects
            .filter(is_deleted=False)
            .annotate(
                package_count=Count('packages'),
                total_bookings=Count('packages__reservations'),
                avg_price=Avg('packages__base_price')
            )
        )

        # Destination popularity
        destination_metrics = (Destination.objects
            .filter(is_deleted=False)
            .annotate(
                package_count=Count('packages'),
                total_bookings=Count('packages__reservations')
            )
            .order_by('-total_bookings')
        )

        context.update({
            'performance': performance,
            'category_metrics': category_metrics,
            'destination_metrics': destination_metrics
        })

        return context


class ReportExportView(TemplateView):
    """معالجة تصدير التقارير بتنسيقات مختلفة."""

    def get(self, request, *args, **kwargs):
        report_type = kwargs.get('report_type')
        export_format = request.GET.get('format', 'xlsx')

        if report_type == 'financial':
            return self.export_financial_report(export_format)
        elif report_type == 'clients':
            return self.export_client_report(export_format)
        elif report_type == 'packages':
            return self.export_package_report(export_format)

        return HttpResponseBadRequest('نوع التقرير غير صالح')

    def export_financial_report(self, format):
        """تصدير التقرير المالي."""
        # الحصول على البيانات
        data = self.get_financial_data()

        if format == 'xlsx':
            return self.export_to_excel(data, 'تقرير_مالي')
        elif format == 'pdf':
            return self.export_to_pdf(data, 'تقرير_مالي')
        elif format == 'csv':
            return self.export_to_csv(data, 'تقرير_مالي')
class FinancialReportsView(TemplateView):
    """تقارير مالية تفصيلية."""
    template_name = 'reports/financial.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        today = timezone.now().date()
        current_year = today.year

        # تحليل الإيرادات الشهرية
        monthly_revenue = (Invoice.objects
            .filter(
                created_at__year=current_year,
                is_deleted=False,
                status='paid'
            )
            .annotate(month=TruncMonth('created_at'))
            .values('month')
            .annotate(
                total_revenue=Sum('total_amount'),
                booking_count=Count('id'),
                average_value=Avg('total_amount')
            )
            .order_by('month')
        )

        # تحليل طرق الدفع
        payment_methods = (Payment.objects
            .filter(
                created_at__year=current_year,
                is_deleted=False
            )
            .values('method')
            .annotate(
                total_amount=Sum('amount'),
                usage_count=Count('id')
            )
            .order_by('-total_amount')
        )

        # تحليل الأداء المالي حسب نوع الباقة
        package_performance = (TourPackage.objects
            .filter(is_deleted=False)
            .annotate(
                total_revenue=Sum('reservations__total_amount'),
                booking_count=Count('reservations'),
                avg_booking_value=Avg('reservations__total_amount')
            )
            .order_by('-total_revenue')
        )

        context.update({
            'monthly_revenue': list(monthly_revenue),
            'payment_methods': payment_methods,
            'package_performance': package_performance,
            'yearly_summary': {
                'total_revenue': Invoice.objects.filter(
                    created_at__year=current_year,
                    status='paid',
                    is_deleted=False
                ).aggregate(total=Sum('total_amount'))['total'] or 0,
                'total_bookings': Reservation.objects.filter(
                    created_at__year=current_year,
                    is_deleted=False
                ).count(),
                'average_booking_value': Invoice.objects.filter(
                    created_at__year=current_year,
                    status='paid',
                    is_deleted=False
                ).aggregate(avg=Avg('total_amount'))['avg'] or 0
            }
        })

        return context        class FinancialReportsView(TemplateView):
            """تقارير مالية تفصيلية."""
            template_name = 'reports/financial.html'

            def get_context_data(self, **kwargs):
                context = super().get_context_data(**kwargs)
                today = timezone.now().date()
                current_year = today.year

                # تحليل الإيرادات الشهرية
                monthly_revenue = (Invoice.objects
                    .filter(
                        created_at__year=current_year,
                        is_deleted=False,
                        status='paid'
                    )
                    .annotate(month=TruncMonth('created_at'))
                    .values('month')
                    .annotate(
                        total_revenue=Sum('total_amount'),
                        booking_count=Count('id'),
                        average_value=Avg('total_amount')
                    )
                    .order_by('month')
                )

                # تحليل طرق الدفع
                payment_methods = (Payment.objects
                    .filter(
                        created_at__year=current_year,
                        is_deleted=False
                    )
                    .values('method')
                    .annotate(
                        total_amount=Sum('amount'),
                        usage_count=Count('id')
                    )
                    .order_by('-total_amount')
                )

                # تحليل الأداء المالي حسب نوع الباقة
                package_performance = (TourPackage.objects
                    .filter(is_deleted=False)
                    .annotate(
                        total_revenue=Sum('reservations__total_amount'),
                        booking_count=Count('reservations'),
                        avg_booking_value=Avg('reservations__total_amount')
                    )
                    .order_by('-total_revenue')
                )

                context.update({
                    'monthly_revenue': list(monthly_revenue),
                    'payment_methods': payment_methods,
                    'package_performance': package_performance,
                    'yearly_summary': {
                        'total_revenue': Invoice.objects.filter(
                            created_at__year=current_year,
                            status='paid',
                            is_deleted=False
                        ).aggregate(total=Sum('total_amount'))['total'] or 0,
                        'total_bookings': Reservation.objects.filter(
                            created_at__year=current_year,
                            is_deleted=False
                        ).count(),
                        'average_booking_value': Invoice.objects.filter(
                            created_at__year=current_year,
                            status='paid',
                            is_deleted=False
                        ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                    }
                })

                return context                class FinancialReportsView(TemplateView):
                    """تقارير مالية تفصيلية."""
                    template_name = 'reports/financial.html'

                    def get_context_data(self, **kwargs):
                        context = super().get_context_data(**kwargs)
                        today = timezone.now().date()
                        current_year = today.year

                        # تحليل الإيرادات الشهرية
                        monthly_revenue = (Invoice.objects
                            .filter(
                                created_at__year=current_year,
                                is_deleted=False,
                                status='paid'
                            )
                            .annotate(month=TruncMonth('created_at'))
                            .values('month')
                            .annotate(
                                total_revenue=Sum('total_amount'),
                                booking_count=Count('id'),
                                average_value=Avg('total_amount')
                            )
                            .order_by('month')
                        )

                        # تحليل طرق الدفع
                        payment_methods = (Payment.objects
                            .filter(
                                created_at__year=current_year,
                                is_deleted=False
                            )
                            .values('method')
                            .annotate(
                                total_amount=Sum('amount'),
                                usage_count=Count('id')
                            )
                            .order_by('-total_amount')
                        )

                        # تحليل الأداء المالي حسب نوع الباقة
                        package_performance = (TourPackage.objects
                            .filter(is_deleted=False)
                            .annotate(
                                total_revenue=Sum('reservations__total_amount'),
                                booking_count=Count('reservations'),
                                avg_booking_value=Avg('reservations__total_amount')
                            )
                            .order_by('-total_revenue')
                        )

                        context.update({
                            'monthly_revenue': list(monthly_revenue),
                            'payment_methods': payment_methods,
                            'package_performance': package_performance,
                            'yearly_summary': {
                                'total_revenue': Invoice.objects.filter(
                                    created_at__year=current_year,
                                    status='paid',
                                    is_deleted=False
                                ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                'total_bookings': Reservation.objects.filter(
                                    created_at__year=current_year,
                                    is_deleted=False
                                ).count(),
                                'average_booking_value': Invoice.objects.filter(
                                    created_at__year=current_year,
                                    status='paid',
                                    is_deleted=False
                                ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                            }
                        })

                        return context                        class FinancialReportsView(TemplateView):
                            """تقارير مالية تفصيلية."""
                            template_name = 'reports/financial.html'

                            def get_context_data(self, **kwargs):
                                context = super().get_context_data(**kwargs)
                                today = timezone.now().date()
                                current_year = today.year

                                # تحليل الإيرادات الشهرية
                                monthly_revenue = (Invoice.objects
                                    .filter(
                                        created_at__year=current_year,
                                        is_deleted=False,
                                        status='paid'
                                    )
                                    .annotate(month=TruncMonth('created_at'))
                                    .values('month')
                                    .annotate(
                                        total_revenue=Sum('total_amount'),
                                        booking_count=Count('id'),
                                        average_value=Avg('total_amount')
                                    )
                                    .order_by('month')
                                )

                                # تحليل طرق الدفع
                                payment_methods = (Payment.objects
                                    .filter(
                                        created_at__year=current_year,
                                        is_deleted=False
                                    )
                                    .values('method')
                                    .annotate(
                                        total_amount=Sum('amount'),
                                        usage_count=Count('id')
                                    )
                                    .order_by('-total_amount')
                                )

                                # تحليل الأداء المالي حسب نوع الباقة
                                package_performance = (TourPackage.objects
                                    .filter(is_deleted=False)
                                    .annotate(
                                        total_revenue=Sum('reservations__total_amount'),
                                        booking_count=Count('reservations'),
                                        avg_booking_value=Avg('reservations__total_amount')
                                    )
                                    .order_by('-total_revenue')
                                )

                                context.update({
                                    'monthly_revenue': list(monthly_revenue),
                                    'payment_methods': payment_methods,
                                    'package_performance': package_performance,
                                    'yearly_summary': {
                                        'total_revenue': Invoice.objects.filter(
                                            created_at__year=current_year,
                                            status='paid',
                                            is_deleted=False
                                        ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                        'total_bookings': Reservation.objects.filter(
                                            created_at__year=current_year,
                                            is_deleted=False
                                        ).count(),
                                        'average_booking_value': Invoice.objects.filter(
                                            created_at__year=current_year,
                                            status='paid',
                                            is_deleted=False
                                        ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                    }
                                })

                                return context                                class FinancialReportsView(TemplateView):
                                    """تقارير مالية تفصيلية."""
                                    template_name = 'reports/financial.html'

                                    def get_context_data(self, **kwargs):
                                        context = super().get_context_data(**kwargs)
                                        today = timezone.now().date()
                                        current_year = today.year

                                        # تحليل الإيرادات الشهرية
                                        monthly_revenue = (Invoice.objects
                                            .filter(
                                                created_at__year=current_year,
                                                is_deleted=False,
                                                status='paid'
                                            )
                                            .annotate(month=TruncMonth('created_at'))
                                            .values('month')
                                            .annotate(
                                                total_revenue=Sum('total_amount'),
                                                booking_count=Count('id'),
                                                average_value=Avg('total_amount')
                                            )
                                            .order_by('month')
                                        )

                                        # تحليل طرق الدفع
                                        payment_methods = (Payment.objects
                                            .filter(
                                                created_at__year=current_year,
                                                is_deleted=False
                                            )
                                            .values('method')
                                            .annotate(
                                                total_amount=Sum('amount'),
                                                usage_count=Count('id')
                                            )
                                            .order_by('-total_amount')
                                        )

                                        # تحليل الأداء المالي حسب نوع الباقة
                                        package_performance = (TourPackage.objects
                                            .filter(is_deleted=False)
                                            .annotate(
                                                total_revenue=Sum('reservations__total_amount'),
                                                booking_count=Count('reservations'),
                                                avg_booking_value=Avg('reservations__total_amount')
                                            )
                                            .order_by('-total_revenue')
                                        )

                                        context.update({
                                            'monthly_revenue': list(monthly_revenue),
                                            'payment_methods': payment_methods,
                                            'package_performance': package_performance,
                                            'yearly_summary': {
                                                'total_revenue': Invoice.objects.filter(
                                                    created_at__year=current_year,
                                                    status='paid',
                                                    is_deleted=False
                                                ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                'total_bookings': Reservation.objects.filter(
                                                    created_at__year=current_year,
                                                    is_deleted=False
                                                ).count(),
                                                'average_booking_value': Invoice.objects.filter(
                                                    created_at__year=current_year,
                                                    status='paid',
                                                    is_deleted=False
                                                ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                            }
                                        })

                                        return context                                        class FinancialReportsView(TemplateView):
                                            """تقارير مالية تفصيلية."""
                                            template_name = 'reports/financial.html'

                                            def get_context_data(self, **kwargs):
                                                context = super().get_context_data(**kwargs)
                                                today = timezone.now().date()
                                                current_year = today.year

                                                # تحليل الإيرادات الشهرية
                                                monthly_revenue = (Invoice.objects
                                                    .filter(
                                                        created_at__year=current_year,
                                                        is_deleted=False,
                                                        status='paid'
                                                    )
                                                    .annotate(month=TruncMonth('created_at'))
                                                    .values('month')
                                                    .annotate(
                                                        total_revenue=Sum('total_amount'),
                                                        booking_count=Count('id'),
                                                        average_value=Avg('total_amount')
                                                    )
                                                    .order_by('month')
                                                )

                                                # تحليل طرق الدفع
                                                payment_methods = (Payment.objects
                                                    .filter(
                                                        created_at__year=current_year,
                                                        is_deleted=False
                                                    )
                                                    .values('method')
                                                    .annotate(
                                                        total_amount=Sum('amount'),
                                                        usage_count=Count('id')
                                                    )
                                                    .order_by('-total_amount')
                                                )

                                                # تحليل الأداء المالي حسب نوع الباقة
                                                package_performance = (TourPackage.objects
                                                    .filter(is_deleted=False)
                                                    .annotate(
                                                        total_revenue=Sum('reservations__total_amount'),
                                                        booking_count=Count('reservations'),
                                                        avg_booking_value=Avg('reservations__total_amount')
                                                    )
                                                    .order_by('-total_revenue')
                                                )

                                                context.update({
                                                    'monthly_revenue': list(monthly_revenue),
                                                    'payment_methods': payment_methods,
                                                    'package_performance': package_performance,
                                                    'yearly_summary': {
                                                        'total_revenue': Invoice.objects.filter(
                                                            created_at__year=current_year,
                                                            status='paid',
                                                            is_deleted=False
                                                        ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                        'total_bookings': Reservation.objects.filter(
                                                            created_at__year=current_year,
                                                            is_deleted=False
                                                        ).count(),
                                                        'average_booking_value': Invoice.objects.filter(
                                                            created_at__year=current_year,
                                                            status='paid',
                                                            is_deleted=False
                                                        ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                    }
                                                })

                                                return context                                                class FinancialReportsView(TemplateView):
                                                    """تقارير مالية تفصيلية."""
                                                    template_name = 'reports/financial.html'

                                                    def get_context_data(self, **kwargs):
                                                        context = super().get_context_data(**kwargs)
                                                        today = timezone.now().date()
                                                        current_year = today.year

                                                        # تحليل الإيرادات الشهرية
                                                        monthly_revenue = (Invoice.objects
                                                            .filter(
                                                                created_at__year=current_year,
                                                                is_deleted=False,
                                                                status='paid'
                                                            )
                                                            .annotate(month=TruncMonth('created_at'))
                                                            .values('month')
                                                            .annotate(
                                                                total_revenue=Sum('total_amount'),
                                                                booking_count=Count('id'),
                                                                average_value=Avg('total_amount')
                                                            )
                                                            .order_by('month')
                                                        )

                                                        # تحليل طرق الدفع
                                                        payment_methods = (Payment.objects
                                                            .filter(
                                                                created_at__year=current_year,
                                                                is_deleted=False
                                                            )
                                                            .values('method')
                                                            .annotate(
                                                                total_amount=Sum('amount'),
                                                                usage_count=Count('id')
                                                            )
                                                            .order_by('-total_amount')
                                                        )

                                                        # تحليل الأداء المالي حسب نوع الباقة
                                                        package_performance = (TourPackage.objects
                                                            .filter(is_deleted=False)
                                                            .annotate(
                                                                total_revenue=Sum('reservations__total_amount'),
                                                                booking_count=Count('reservations'),
                                                                avg_booking_value=Avg('reservations__total_amount')
                                                            )
                                                            .order_by('-total_revenue')
                                                        )

                                                        context.update({
                                                            'monthly_revenue': list(monthly_revenue),
                                                            'payment_methods': payment_methods,
                                                            'package_performance': package_performance,
                                                            'yearly_summary': {
                                                                'total_revenue': Invoice.objects.filter(
                                                                    created_at__year=current_year,
                                                                    status='paid',
                                                                    is_deleted=False
                                                                ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                'total_bookings': Reservation.objects.filter(
                                                                    created_at__year=current_year,
                                                                    is_deleted=False
                                                                ).count(),
                                                                'average_booking_value': Invoice.objects.filter(
                                                                    created_at__year=current_year,
                                                                    status='paid',
                                                                    is_deleted=False
                                                                ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                            }
                                                        })

                                                        return context                                                        class FinancialReportsView(TemplateView):
                                                            """تقارير مالية تفصيلية."""
                                                            template_name = 'reports/financial.html'

                                                            def get_context_data(self, **kwargs):
                                                                context = super().get_context_data(**kwargs)
                                                                today = timezone.now().date()
                                                                current_year = today.year

                                                                # تحليل الإيرادات الشهرية
                                                                monthly_revenue = (Invoice.objects
                                                                    .filter(
                                                                        created_at__year=current_year,
                                                                        is_deleted=False,
                                                                        status='paid'
                                                                    )
                                                                    .annotate(month=TruncMonth('created_at'))
                                                                    .values('month')
                                                                    .annotate(
                                                                        total_revenue=Sum('total_amount'),
                                                                        booking_count=Count('id'),
                                                                        average_value=Avg('total_amount')
                                                                    )
                                                                    .order_by('month')
                                                                )

                                                                # تحليل طرق الدفع
                                                                payment_methods = (Payment.objects
                                                                    .filter(
                                                                        created_at__year=current_year,
                                                                        is_deleted=False
                                                                    )
                                                                    .values('method')
                                                                    .annotate(
                                                                        total_amount=Sum('amount'),
                                                                        usage_count=Count('id')
                                                                    )
                                                                    .order_by('-total_amount')
                                                                )

                                                                # تحليل الأداء المالي حسب نوع الباقة
                                                                package_performance = (TourPackage.objects
                                                                    .filter(is_deleted=False)
                                                                    .annotate(
                                                                        total_revenue=Sum('reservations__total_amount'),
                                                                        booking_count=Count('reservations'),
                                                                        avg_booking_value=Avg('reservations__total_amount')
                                                                    )
                                                                    .order_by('-total_revenue')
                                                                )

                                                                context.update({
                                                                    'monthly_revenue': list(monthly_revenue),
                                                                    'payment_methods': payment_methods,
                                                                    'package_performance': package_performance,
                                                                    'yearly_summary': {
                                                                        'total_revenue': Invoice.objects.filter(
                                                                            created_at__year=current_year,
                                                                            status='paid',
                                                                            is_deleted=False
                                                                        ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                        'total_bookings': Reservation.objects.filter(
                                                                            created_at__year=current_year,
                                                                            is_deleted=False
                                                                        ).count(),
                                                                        'average_booking_value': Invoice.objects.filter(
                                                                            created_at__year=current_year,
                                                                            status='paid',
                                                                            is_deleted=False
                                                                        ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                    }
                                                                })

                                                                return context                                                                class FinancialReportsView(TemplateView):
                                                                    """تقارير مالية تفصيلية."""
                                                                    template_name = 'reports/financial.html'

                                                                    def get_context_data(self, **kwargs):
                                                                        context = super().get_context_data(**kwargs)
                                                                        today = timezone.now().date()
                                                                        current_year = today.year

                                                                        # تحليل الإيرادات الشهرية
                                                                        monthly_revenue = (Invoice.objects
                                                                            .filter(
                                                                                created_at__year=current_year,
                                                                                is_deleted=False,
                                                                                status='paid'
                                                                            )
                                                                            .annotate(month=TruncMonth('created_at'))
                                                                            .values('month')
                                                                            .annotate(
                                                                                total_revenue=Sum('total_amount'),
                                                                                booking_count=Count('id'),
                                                                                average_value=Avg('total_amount')
                                                                            )
                                                                            .order_by('month')
                                                                        )

                                                                        # تحليل طرق الدفع
                                                                        payment_methods = (Payment.objects
                                                                            .filter(
                                                                                created_at__year=current_year,
                                                                                is_deleted=False
                                                                            )
                                                                            .values('method')
                                                                            .annotate(
                                                                                total_amount=Sum('amount'),
                                                                                usage_count=Count('id')
                                                                            )
                                                                            .order_by('-total_amount')
                                                                        )

                                                                        # تحليل الأداء المالي حسب نوع الباقة
                                                                        package_performance = (TourPackage.objects
                                                                            .filter(is_deleted=False)
                                                                            .annotate(
                                                                                total_revenue=Sum('reservations__total_amount'),
                                                                                booking_count=Count('reservations'),
                                                                                avg_booking_value=Avg('reservations__total_amount')
                                                                            )
                                                                            .order_by('-total_revenue')
                                                                        )

                                                                        context.update({
                                                                            'monthly_revenue': list(monthly_revenue),
                                                                            'payment_methods': payment_methods,
                                                                            'package_performance': package_performance,
                                                                            'yearly_summary': {
                                                                                'total_revenue': Invoice.objects.filter(
                                                                                    created_at__year=current_year,
                                                                                    status='paid',
                                                                                    is_deleted=False
                                                                                ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                                'total_bookings': Reservation.objects.filter(
                                                                                    created_at__year=current_year,
                                                                                    is_deleted=False
                                                                                ).count(),
                                                                                'average_booking_value': Invoice.objects.filter(
                                                                                    created_at__year=current_year,
                                                                                    status='paid',
                                                                                    is_deleted=False
                                                                                ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                            }
                                                                        })

                                                                        return context                                                                        class FinancialReportsView(TemplateView):
                                                                            """تقارير مالية تفصيلية."""
                                                                            template_name = 'reports/financial.html'

                                                                            def get_context_data(self, **kwargs):
                                                                                context = super().get_context_data(**kwargs)
                                                                                today = timezone.now().date()
                                                                                current_year = today.year

                                                                                # تحليل الإيرادات الشهرية
                                                                                monthly_revenue = (Invoice.objects
                                                                                    .filter(
                                                                                        created_at__year=current_year,
                                                                                        is_deleted=False,
                                                                                        status='paid'
                                                                                    )
                                                                                    .annotate(month=TruncMonth('created_at'))
                                                                                    .values('month')
                                                                                    .annotate(
                                                                                        total_revenue=Sum('total_amount'),
                                                                                        booking_count=Count('id'),
                                                                                        average_value=Avg('total_amount')
                                                                                    )
                                                                                    .order_by('month')
                                                                                )

                                                                                # تحليل طرق الدفع
                                                                                payment_methods = (Payment.objects
                                                                                    .filter(
                                                                                        created_at__year=current_year,
                                                                                        is_deleted=False
                                                                                    )
                                                                                    .values('method')
                                                                                    .annotate(
                                                                                        total_amount=Sum('amount'),
                                                                                        usage_count=Count('id')
                                                                                    )
                                                                                    .order_by('-total_amount')
                                                                                )

                                                                                # تحليل الأداء المالي حسب نوع الباقة
                                                                                package_performance = (TourPackage.objects
                                                                                    .filter(is_deleted=False)
                                                                                    .annotate(
                                                                                        total_revenue=Sum('reservations__total_amount'),
                                                                                        booking_count=Count('reservations'),
                                                                                        avg_booking_value=Avg('reservations__total_amount')
                                                                                    )
                                                                                    .order_by('-total_revenue')
                                                                                )

                                                                                context.update({
                                                                                    'monthly_revenue': list(monthly_revenue),
                                                                                    'payment_methods': payment_methods,
                                                                                    'package_performance': package_performance,
                                                                                    'yearly_summary': {
                                                                                        'total_revenue': Invoice.objects.filter(
                                                                                            created_at__year=current_year,
                                                                                            status='paid',
                                                                                            is_deleted=False
                                                                                        ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                                        'total_bookings': Reservation.objects.filter(
                                                                                            created_at__year=current_year,
                                                                                            is_deleted=False
                                                                                        ).count(),
                                                                                        'average_booking_value': Invoice.objects.filter(
                                                                                            created_at__year=current_year,
                                                                                            status='paid',
                                                                                            is_deleted=False
                                                                                        ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                                    }
                                                                                })

                                                                                return context                                                                                class FinancialReportsView(TemplateView):
                                                                                    """تقارير مالية تفصيلية."""
                                                                                    template_name = 'reports/financial.html'

                                                                                    def get_context_data(self, **kwargs):
                                                                                        context = super().get_context_data(**kwargs)
                                                                                        today = timezone.now().date()
                                                                                        current_year = today.year

                                                                                        # تحليل الإيرادات الشهرية
                                                                                        monthly_revenue = (Invoice.objects
                                                                                            .filter(
                                                                                                created_at__year=current_year,
                                                                                                is_deleted=False,
                                                                                                status='paid'
                                                                                            )
                                                                                            .annotate(month=TruncMonth('created_at'))
                                                                                            .values('month')
                                                                                            .annotate(
                                                                                                total_revenue=Sum('total_amount'),
                                                                                                booking_count=Count('id'),
                                                                                                average_value=Avg('total_amount')
                                                                                            )
                                                                                            .order_by('month')
                                                                                        )

                                                                                        # تحليل طرق الدفع
                                                                                        payment_methods = (Payment.objects
                                                                                            .filter(
                                                                                                created_at__year=current_year,
                                                                                                is_deleted=False
                                                                                            )
                                                                                            .values('method')
                                                                                            .annotate(
                                                                                                total_amount=Sum('amount'),
                                                                                                usage_count=Count('id')
                                                                                            )
                                                                                            .order_by('-total_amount')
                                                                                        )

                                                                                        # تحليل الأداء المالي حسب نوع الباقة
                                                                                        package_performance = (TourPackage.objects
                                                                                            .filter(is_deleted=False)
                                                                                            .annotate(
                                                                                                total_revenue=Sum('reservations__total_amount'),
                                                                                                booking_count=Count('reservations'),
                                                                                                avg_booking_value=Avg('reservations__total_amount')
                                                                                            )
                                                                                            .order_by('-total_revenue')
                                                                                        )

                                                                                        context.update({
                                                                                            'monthly_revenue': list(monthly_revenue),
                                                                                            'payment_methods': payment_methods,
                                                                                            'package_performance': package_performance,
                                                                                            'yearly_summary': {
                                                                                                'total_revenue': Invoice.objects.filter(
                                                                                                    created_at__year=current_year,
                                                                                                    status='paid',
                                                                                                    is_deleted=False
                                                                                                ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                                                'total_bookings': Reservation.objects.filter(
                                                                                                    created_at__year=current_year,
                                                                                                    is_deleted=False
                                                                                                ).count(),
                                                                                                'average_booking_value': Invoice.objects.filter(
                                                                                                    created_at__year=current_year,
                                                                                                    status='paid',
                                                                                                    is_deleted=False
                                                                                                ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                                            }
                                                                                        })

                                                                                        return context                                                                                        class FinancialReportsView(TemplateView):
                                                                                            """تقارير مالية تفصيلية."""
                                                                                            template_name = 'reports/financial.html'

                                                                                            def get_context_data(self, **kwargs):
                                                                                                context = super().get_context_data(**kwargs)
                                                                                                today = timezone.now().date()
                                                                                                current_year = today.year

                                                                                                # تحليل الإيرادات الشهرية
                                                                                                monthly_revenue = (Invoice.objects
                                                                                                    .filter(
                                                                                                        created_at__year=current_year,
                                                                                                        is_deleted=False,
                                                                                                        status='paid'
                                                                                                    )
                                                                                                    .annotate(month=TruncMonth('created_at'))
                                                                                                    .values('month')
                                                                                                    .annotate(
                                                                                                        total_revenue=Sum('total_amount'),
                                                                                                        booking_count=Count('id'),
                                                                                                        average_value=Avg('total_amount')
                                                                                                    )
                                                                                                    .order_by('month')
                                                                                                )

                                                                                                # تحليل طرق الدفع
                                                                                                payment_methods = (Payment.objects
                                                                                                    .filter(
                                                                                                        created_at__year=current_year,
                                                                                                        is_deleted=False
                                                                                                    )
                                                                                                    .values('method')
                                                                                                    .annotate(
                                                                                                        total_amount=Sum('amount'),
                                                                                                        usage_count=Count('id')
                                                                                                    )
                                                                                                    .order_by('-total_amount')
                                                                                                )

                                                                                                # تحليل الأداء المالي حسب نوع الباقة
                                                                                                package_performance = (TourPackage.objects
                                                                                                    .filter(is_deleted=False)
                                                                                                    .annotate(
                                                                                                        total_revenue=Sum('reservations__total_amount'),
                                                                                                        booking_count=Count('reservations'),
                                                                                                        avg_booking_value=Avg('reservations__total_amount')
                                                                                                    )
                                                                                                    .order_by('-total_revenue')
                                                                                                )

                                                                                                context.update({
                                                                                                    'monthly_revenue': list(monthly_revenue),
                                                                                                    'payment_methods': payment_methods,
                                                                                                    'package_performance': package_performance,
                                                                                                    'yearly_summary': {
                                                                                                        'total_revenue': Invoice.objects.filter(
                                                                                                            created_at__year=current_year,
                                                                                                            status='paid',
                                                                                                            is_deleted=False
                                                                                                        ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                                                        'total_bookings': Reservation.objects.filter(
                                                                                                            created_at__year=current_year,
                                                                                                            is_deleted=False
                                                                                                        ).count(),
                                                                                                        'average_booking_value': Invoice.objects.filter(
                                                                                                            created_at__year=current_year,
                                                                                                            status='paid',
                                                                                                            is_deleted=False
                                                                                                        ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                                                    }
                                                                                                })

                                                                                                return context                                                                                                class FinancialReportsView(TemplateView):
                                                                                                    """تقارير مالية تفصيلية."""
                                                                                                    template_name = 'reports/financial.html'

                                                                                                    def get_context_data(self, **kwargs):
                                                                                                        context = super().get_context_data(**kwargs)
                                                                                                        today = timezone.now().date()
                                                                                                        current_year = today.year

                                                                                                        # تحليل الإيرادات الشهرية
                                                                                                        monthly_revenue = (Invoice.objects
                                                                                                            .filter(
                                                                                                                created_at__year=current_year,
                                                                                                                is_deleted=False,
                                                                                                                status='paid'
                                                                                                            )
                                                                                                            .annotate(month=TruncMonth('created_at'))
                                                                                                            .values('month')
                                                                                                            .annotate(
                                                                                                                total_revenue=Sum('total_amount'),
                                                                                                                booking_count=Count('id'),
                                                                                                                average_value=Avg('total_amount')
                                                                                                            )
                                                                                                            .order_by('month')
                                                                                                        )

                                                                                                        # تحليل طرق الدفع
                                                                                                        payment_methods = (Payment.objects
                                                                                                            .filter(
                                                                                                                created_at__year=current_year,
                                                                                                                is_deleted=False
                                                                                                            )
                                                                                                            .values('method')
                                                                                                            .annotate(
                                                                                                                total_amount=Sum('amount'),
                                                                                                                usage_count=Count('id')
                                                                                                            )
                                                                                                            .order_by('-total_amount')
                                                                                                        )

                                                                                                        # تحليل الأداء المالي حسب نوع الباقة
                                                                                                        package_performance = (TourPackage.objects
                                                                                                            .filter(is_deleted=False)
                                                                                                            .annotate(
                                                                                                                total_revenue=Sum('reservations__total_amount'),
                                                                                                                booking_count=Count('reservations'),
                                                                                                                avg_booking_value=Avg('reservations__total_amount')
                                                                                                            )
                                                                                                            .order_by('-total_revenue')
                                                                                                        )

                                                                                                        context.update({
                                                                                                            'monthly_revenue': list(monthly_revenue),
                                                                                                            'payment_methods': payment_methods,
                                                                                                            'package_performance': package_performance,
                                                                                                            'yearly_summary': {
                                                                                                                'total_revenue': Invoice.objects.filter(
                                                                                                                    created_at__year=current_year,
                                                                                                                    status='paid',
                                                                                                                    is_deleted=False
                                                                                                                ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                                                                'total_bookings': Reservation.objects.filter(
                                                                                                                    created_at__year=current_year,
                                                                                                                    is_deleted=False
                                                                                                                ).count(),
                                                                                                                'average_booking_value': Invoice.objects.filter(
                                                                                                                    created_at__year=current_year,
                                                                                                                    status='paid',
                                                                                                                    is_deleted=False
                                                                                                                ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                                                            }
                                                                                                        })

                                                                                                        return context                                                                                                        class FinancialReportsView(TemplateView):
                                                                                                            """تقارير مالية تفصيلية."""
                                                                                                            template_name = 'reports/financial.html'

                                                                                                            def get_context_data(self, **kwargs):
                                                                                                                context = super().get_context_data(**kwargs)
                                                                                                                today = timezone.now().date()
                                                                                                                current_year = today.year

                                                                                                                # تحليل الإيرادات الشهرية
                                                                                                                monthly_revenue = (Invoice.objects
                                                                                                                    .filter(
                                                                                                                        created_at__year=current_year,
                                                                                                                        is_deleted=False,
                                                                                                                        status='paid'
                                                                                                                    )
                                                                                                                    .annotate(month=TruncMonth('created_at'))
                                                                                                                    .values('month')
                                                                                                                    .annotate(
                                                                                                                        total_revenue=Sum('total_amount'),
                                                                                                                        booking_count=Count('id'),
                                                                                                                        average_value=Avg('total_amount')
                                                                                                                    )
                                                                                                                    .order_by('month')
                                                                                                                )

                                                                                                                # تحليل طرق الدفع
                                                                                                                payment_methods = (Payment.objects
                                                                                                                    .filter(
                                                                                                                        created_at__year=current_year,
                                                                                                                        is_deleted=False
                                                                                                                    )
                                                                                                                    .values('method')
                                                                                                                    .annotate(
                                                                                                                        total_amount=Sum('amount'),
                                                                                                                        usage_count=Count('id')
                                                                                                                    )
                                                                                                                    .order_by('-total_amount')
                                                                                                                )

                                                                                                                # تحليل الأداء المالي حسب نوع الباقة
                                                                                                                package_performance = (TourPackage.objects
                                                                                                                    .filter(is_deleted=False)
                                                                                                                    .annotate(
                                                                                                                        total_revenue=Sum('reservations__total_amount'),
                                                                                                                        booking_count=Count('reservations'),
                                                                                                                        avg_booking_value=Avg('reservations__total_amount')
                                                                                                                    )
                                                                                                                    .order_by('-total_revenue')
                                                                                                                )

                                                                                                                context.update({
                                                                                                                    'monthly_revenue': list(monthly_revenue),
                                                                                                                    'payment_methods': payment_methods,
                                                                                                                    'package_performance': package_performance,
                                                                                                                    'yearly_summary': {
                                                                                                                        'total_revenue': Invoice.objects.filter(
                                                                                                                            created_at__year=current_year,
                                                                                                                            status='paid',
                                                                                                                            is_deleted=False
                                                                                                                        ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                                                                        'total_bookings': Reservation.objects.filter(
                                                                                                                            created_at__year=current_year,
                                                                                                                            is_deleted=False
                                                                                                                        ).count(),
                                                                                                                        'average_booking_value': Invoice.objects.filter(
                                                                                                                            created_at__year=current_year,
                                                                                                                            status='paid',
                                                                                                                            is_deleted=False
                                                                                                                        ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                                                                    }
                                                                                                                })

                                                                                                                return context                                                                                                                class FinancialReportsView(TemplateView):
                                                                                                                    """تقارير مالية تفصيلية."""
                                                                                                                    template_name = 'reports/financial.html'

                                                                                                                    def get_context_data(self, **kwargs):
                                                                                                                        context = super().get_context_data(**kwargs)
                                                                                                                        today = timezone.now().date()
                                                                                                                        current_year = today.year

                                                                                                                        # تحليل الإيرادات الشهرية
                                                                                                                        monthly_revenue = (Invoice.objects
                                                                                                                            .filter(
                                                                                                                                created_at__year=current_year,
                                                                                                                                is_deleted=False,
                                                                                                                                status='paid'
                                                                                                                            )
                                                                                                                            .annotate(month=TruncMonth('created_at'))
                                                                                                                            .values('month')
                                                                                                                            .annotate(
                                                                                                                                total_revenue=Sum('total_amount'),
                                                                                                                                booking_count=Count('id'),
                                                                                                                                average_value=Avg('total_amount')
                                                                                                                            )
                                                                                                                            .order_by('month')
                                                                                                                        )

                                                                                                                        # تحليل طرق الدفع
                                                                                                                        payment_methods = (Payment.objects
                                                                                                                            .filter(
                                                                                                                                created_at__year=current_year,
                                                                                                                                is_deleted=False
                                                                                                                            )
                                                                                                                            .values('method')
                                                                                                                            .annotate(
                                                                                                                                total_amount=Sum('amount'),
                                                                                                                                usage_count=Count('id')
                                                                                                                            )
                                                                                                                            .order_by('-total_amount')
                                                                                                                        )

                                                                                                                        # تحليل الأداء المالي حسب نوع الباقة
                                                                                                                        package_performance = (TourPackage.objects
                                                                                                                            .filter(is_deleted=False)
                                                                                                                            .annotate(
                                                                                                                                total_revenue=Sum('reservations__total_amount'),
                                                                                                                                booking_count=Count('reservations'),
                                                                                                                                avg_booking_value=Avg('reservations__total_amount')
                                                                                                                            )
                                                                                                                            .order_by('-total_revenue')
                                                                                                                        )

                                                                                                                        context.update({
                                                                                                                            'monthly_revenue': list(monthly_revenue),
                                                                                                                            'payment_methods': payment_methods,
                                                                                                                            'package_performance': package_performance,
                                                                                                                            'yearly_summary': {
                                                                                                                                'total_revenue': Invoice.objects.filter(
                                                                                                                                    created_at__year=current_year,
                                                                                                                                    status='paid',
                                                                                                                                    is_deleted=False
                                                                                                                                ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                                                                                'total_bookings': Reservation.objects.filter(
                                                                                                                                    created_at__year=current_year,
                                                                                                                                    is_deleted=False
                                                                                                                                ).count(),
                                                                                                                                'average_booking_value': Invoice.objects.filter(
                                                                                                                                    created_at__year=current_year,
                                                                                                                                    status='paid',
                                                                                                                                    is_deleted=False
                                                                                                                                ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                                                                            }
                                                                                                                        })

                                                                                                                        return context                                                                                                                        class FinancialReportsView(TemplateView):
                                                                                                                            """تقارير مالية تفصيلية."""
                                                                                                                            template_name = 'reports/financial.html'

                                                                                                                            def get_context_data(self, **kwargs):
                                                                                                                                context = super().get_context_data(**kwargs)
                                                                                                                                today = timezone.now().date()
                                                                                                                                current_year = today.year

                                                                                                                                # تحليل الإيرادات الشهرية
                                                                                                                                monthly_revenue = (Invoice.objects
                                                                                                                                    .filter(
                                                                                                                                        created_at__year=current_year,
                                                                                                                                        is_deleted=False,
                                                                                                                                        status='paid'
                                                                                                                                    )
                                                                                                                                    .annotate(month=TruncMonth('created_at'))
                                                                                                                                    .values('month')
                                                                                                                                    .annotate(
                                                                                                                                        total_revenue=Sum('total_amount'),
                                                                                                                                        booking_count=Count('id'),
                                                                                                                                        average_value=Avg('total_amount')
                                                                                                                                    )
                                                                                                                                    .order_by('month')
                                                                                                                                )

                                                                                                                                # تحليل طرق الدفع
                                                                                                                                payment_methods = (Payment.objects
                                                                                                                                    .filter(
                                                                                                                                        created_at__year=current_year,
                                                                                                                                        is_deleted=False
                                                                                                                                    )
                                                                                                                                    .values('method')
                                                                                                                                    .annotate(
                                                                                                                                        total_amount=Sum('amount'),
                                                                                                                                        usage_count=Count('id')
                                                                                                                                    )
                                                                                                                                    .order_by('-total_amount')
                                                                                                                                )

                                                                                                                                # تحليل الأداء المالي حسب نوع الباقة
                                                                                                                                package_performance = (TourPackage.objects
                                                                                                                                    .filter(is_deleted=False)
                                                                                                                                    .annotate(
                                                                                                                                        total_revenue=Sum('reservations__total_amount'),
                                                                                                                                        booking_count=Count('reservations'),
                                                                                                                                        avg_booking_value=Avg('reservations__total_amount')
                                                                                                                                    )
                                                                                                                                    .order_by('-total_revenue')
                                                                                                                                )

                                                                                                                                context.update({
                                                                                                                                    'monthly_revenue': list(monthly_revenue),
                                                                                                                                    'payment_methods': payment_methods,
                                                                                                                                    'package_performance': package_performance,
                                                                                                                                    'yearly_summary': {
                                                                                                                                        'total_revenue': Invoice.objects.filter(
                                                                                                                                            created_at__year=current_year,
                                                                                                                                            status='paid',
                                                                                                                                            is_deleted=False
                                                                                                                                        ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                                                                                        'total_bookings': Reservation.objects.filter(
                                                                                                                                            created_at__year=current_year,
                                                                                                                                            is_deleted=False
                                                                                                                                        ).count(),
                                                                                                                                        'average_booking_value': Invoice.objects.filter(
                                                                                                                                            created_at__year=current_year,
                                                                                                                                            status='paid',
                                                                                                                                            is_deleted=False
                                                                                                                                        ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                                                                                    }
                                                                                                                                })

                                                                                                                                return context                                                                                                                                class FinancialReportsView(TemplateView):
                                                                                                                                    """تقارير مالية تفصيلية."""
                                                                                                                                    template_name = 'reports/financial.html'

                                                                                                                                    def get_context_data(self, **kwargs):
                                                                                                                                        context = super().get_context_data(**kwargs)
                                                                                                                                        today = timezone.now().date()
                                                                                                                                        current_year = today.year

                                                                                                                                        # تحليل الإيرادات الشهرية
                                                                                                                                        monthly_revenue = (Invoice.objects
                                                                                                                                            .filter(
                                                                                                                                                created_at__year=current_year,
                                                                                                                                                is_deleted=False,
                                                                                                                                                status='paid'
                                                                                                                                            )
                                                                                                                                            .annotate(month=TruncMonth('created_at'))
                                                                                                                                            .values('month')
                                                                                                                                            .annotate(
                                                                                                                                                total_revenue=Sum('total_amount'),
                                                                                                                                                booking_count=Count('id'),
                                                                                                                                                average_value=Avg('total_amount')
                                                                                                                                            )
                                                                                                                                            .order_by('month')
                                                                                                                                        )

                                                                                                                                        # تحليل طرق الدفع
                                                                                                                                        payment_methods = (Payment.objects
                                                                                                                                            .filter(
                                                                                                                                                created_at__year=current_year,
                                                                                                                                                is_deleted=False
                                                                                                                                            )
                                                                                                                                            .values('method')
                                                                                                                                            .annotate(
                                                                                                                                                total_amount=Sum('amount'),
                                                                                                                                                usage_count=Count('id')
                                                                                                                                            )
                                                                                                                                            .order_by('-total_amount')
                                                                                                                                        )

                                                                                                                                        # تحليل الأداء المالي حسب نوع الباقة
                                                                                                                                        package_performance = (TourPackage.objects
                                                                                                                                            .filter(is_deleted=False)
                                                                                                                                            .annotate(
                                                                                                                                                total_revenue=Sum('reservations__total_amount'),
                                                                                                                                                booking_count=Count('reservations'),
                                                                                                                                                avg_booking_value=Avg('reservations__total_amount')
                                                                                                                                            )
                                                                                                                                            .order_by('-total_revenue')
                                                                                                                                        )

                                                                                                                                        context.update({
                                                                                                                                            'monthly_revenue': list(monthly_revenue),
                                                                                                                                            'payment_methods': payment_methods,
                                                                                                                                            'package_performance': package_performance,
                                                                                                                                            'yearly_summary': {
                                                                                                                                                'total_revenue': Invoice.objects.filter(
                                                                                                                                                    created_at__year=current_year,
                                                                                                                                                    status='paid',
                                                                                                                                                    is_deleted=False
                                                                                                                                                ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                                                                                                'total_bookings': Reservation.objects.filter(
                                                                                                                                                    created_at__year=current_year,
                                                                                                                                                    is_deleted=False
                                                                                                                                                ).count(),
                                                                                                                                                'average_booking_value': Invoice.objects.filter(
                                                                                                                                                    created_at__year=current_year,
                                                                                                                                                    status='paid',
                                                                                                                                                    is_deleted=False
                                                                                                                                                ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                                                                                            }
                                                                                                                                        })

                                                                                                                                        return context                                                                                                                                        class FinancialReportsView(TemplateView):
                                                                                                                                            """تقارير مالية تفصيلية."""
                                                                                                                                            template_name = 'reports/financial.html'

                                                                                                                                            def get_context_data(self, **kwargs):
                                                                                                                                                context = super().get_context_data(**kwargs)
                                                                                                                                                today = timezone.now().date()
                                                                                                                                                current_year = today.year

                                                                                                                                                # تحليل الإيرادات الشهرية
                                                                                                                                                monthly_revenue = (Invoice.objects
                                                                                                                                                    .filter(
                                                                                                                                                        created_at__year=current_year,
                                                                                                                                                        is_deleted=False,
                                                                                                                                                        status='paid'
                                                                                                                                                    )
                                                                                                                                                    .annotate(month=TruncMonth('created_at'))
                                                                                                                                                    .values('month')
                                                                                                                                                    .annotate(
                                                                                                                                                        total_revenue=Sum('total_amount'),
                                                                                                                                                        booking_count=Count('id'),
                                                                                                                                                        average_value=Avg('total_amount')
                                                                                                                                                    )
                                                                                                                                                    .order_by('month')
                                                                                                                                                )

                                                                                                                                                # تحليل طرق الدفع
                                                                                                                                                payment_methods = (Payment.objects
                                                                                                                                                    .filter(
                                                                                                                                                        created_at__year=current_year,
                                                                                                                                                        is_deleted=False
                                                                                                                                                    )
                                                                                                                                                    .values('method')
                                                                                                                                                    .annotate(
                                                                                                                                                        total_amount=Sum('amount'),
                                                                                                                                                        usage_count=Count('id')
                                                                                                                                                    )
                                                                                                                                                    .order_by('-total_amount')
                                                                                                                                                )

                                                                                                                                                # تحليل الأداء المالي حسب نوع الباقة
                                                                                                                                                package_performance = (TourPackage.objects
                                                                                                                                                    .filter(is_deleted=False)
                                                                                                                                                    .annotate(
                                                                                                                                                        total_revenue=Sum('reservations__total_amount'),
                                                                                                                                                        booking_count=Count('reservations'),
                                                                                                                                                        avg_booking_value=Avg('reservations__total_amount')
                                                                                                                                                    )
                                                                                                                                                    .order_by('-total_revenue')
                                                                                                                                                )

                                                                                                                                                context.update({
                                                                                                                                                    'monthly_revenue': list(monthly_revenue),
                                                                                                                                                    'payment_methods': payment_methods,
                                                                                                                                                    'package_performance': package_performance,
                                                                                                                                                    'yearly_summary': {
                                                                                                                                                        'total_revenue': Invoice.objects.filter(
                                                                                                                                                            created_at__year=current_year,
                                                                                                                                                            status='paid',
                                                                                                                                                            is_deleted=False
                                                                                                                                                        ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                                                                                                        'total_bookings': Reservation.objects.filter(
                                                                                                                                                            created_at__year=current_year,
                                                                                                                                                            is_deleted=False
                                                                                                                                                        ).count(),
                                                                                                                                                        'average_booking_value': Invoice.objects.filter(
                                                                                                                                                            created_at__year=current_year,
                                                                                                                                                            status='paid',
                                                                                                                                                            is_deleted=False
                                                                                                                                                        ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                                                                                                    }
                                                                                                                                                })

                                                                                                                                                return context                                                                                                                                                class FinancialReportsView(TemplateView):
                                                                                                                                                    """تقارير مالية تفصيلية."""
                                                                                                                                                    template_name = 'reports/financial.html'

                                                                                                                                                    def get_context_data(self, **kwargs):
                                                                                                                                                        context = super().get_context_data(**kwargs)
                                                                                                                                                        today = timezone.now().date()
                                                                                                                                                        current_year = today.year

                                                                                                                                                        # تحليل الإيرادات الشهرية
                                                                                                                                                        monthly_revenue = (Invoice.objects
                                                                                                                                                            .filter(
                                                                                                                                                                created_at__year=current_year,
                                                                                                                                                                is_deleted=False,
                                                                                                                                                                status='paid'
                                                                                                                                                            )
                                                                                                                                                            .annotate(month=TruncMonth('created_at'))
                                                                                                                                                            .values('month')
                                                                                                                                                            .annotate(
                                                                                                                                                                total_revenue=Sum('total_amount'),
                                                                                                                                                                booking_count=Count('id'),
                                                                                                                                                                average_value=Avg('total_amount')
                                                                                                                                                            )
                                                                                                                                                            .order_by('month')
                                                                                                                                                        )

                                                                                                                                                        # تحليل طرق الدفع
                                                                                                                                                        payment_methods = (Payment.objects
                                                                                                                                                            .filter(
                                                                                                                                                                created_at__year=current_year,
                                                                                                                                                                is_deleted=False
                                                                                                                                                            )
                                                                                                                                                            .values('method')
                                                                                                                                                            .annotate(
                                                                                                                                                                total_amount=Sum('amount'),
                                                                                                                                                                usage_count=Count('id')
                                                                                                                                                            )
                                                                                                                                                            .order_by('-total_amount')
                                                                                                                                                        )

                                                                                                                                                        # تحليل الأداء المالي حسب نوع الباقة
                                                                                                                                                        package_performance = (TourPackage.objects
                                                                                                                                                            .filter(is_deleted=False)
                                                                                                                                                            .annotate(
                                                                                                                                                                total_revenue=Sum('reservations__total_amount'),
                                                                                                                                                                booking_count=Count('reservations'),
                                                                                                                                                                avg_booking_value=Avg('reservations__total_amount')
                                                                                                                                                            )
                                                                                                                                                            .order_by('-total_revenue')
                                                                                                                                                        )

                                                                                                                                                        context.update({
                                                                                                                                                            'monthly_revenue': list(monthly_revenue),
                                                                                                                                                            'payment_methods': payment_methods,
                                                                                                                                                            'package_performance': package_performance,
                                                                                                                                                            'yearly_summary': {
                                                                                                                                                                'total_revenue': Invoice.objects.filter(
                                                                                                                                                                    created_at__year=current_year,
                                                                                                                                                                    status='paid',
                                                                                                                                                                    is_deleted=False
                                                                                                                                                                ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                                                                                                                'total_bookings': Reservation.objects.filter(
                                                                                                                                                                    created_at__year=current_year,
                                                                                                                                                                    is_deleted=False
                                                                                                                                                                ).count(),
                                                                                                                                                                'average_booking_value': Invoice.objects.filter(
                                                                                                                                                                    created_at__year=current_year,
                                                                                                                                                                    status='paid',
                                                                                                                                                                    is_deleted=False
                                                                                                                                                                ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                                                                                                            }
                                                                                                                                                        })

                                                                                                                                                        return context                                                                                                                                                        class FinancialReportsView(TemplateView):
                                                                                                                                                            """تقارير مالية تفصيلية."""
                                                                                                                                                            template_name = 'reports/financial.html'

                                                                                                                                                            def get_context_data(self, **kwargs):
                                                                                                                                                                context = super().get_context_data(**kwargs)
                                                                                                                                                                today = timezone.now().date()
                                                                                                                                                                current_year = today.year

                                                                                                                                                                # تحليل الإيرادات الشهرية
                                                                                                                                                                monthly_revenue = (Invoice.objects
                                                                                                                                                                    .filter(
                                                                                                                                                                        created_at__year=current_year,
                                                                                                                                                                        is_deleted=False,
                                                                                                                                                                        status='paid'
                                                                                                                                                                    )
                                                                                                                                                                    .annotate(month=TruncMonth('created_at'))
                                                                                                                                                                    .values('month')
                                                                                                                                                                    .annotate(
                                                                                                                                                                        total_revenue=Sum('total_amount'),
                                                                                                                                                                        booking_count=Count('id'),
                                                                                                                                                                        average_value=Avg('total_amount')
                                                                                                                                                                    )
                                                                                                                                                                    .order_by('month')
                                                                                                                                                                )

                                                                                                                                                                # تحليل طرق الدفع
                                                                                                                                                                payment_methods = (Payment.objects
                                                                                                                                                                    .filter(
                                                                                                                                                                        created_at__year=current_year,
                                                                                                                                                                        is_deleted=False
                                                                                                                                                                    )
                                                                                                                                                                    .values('method')
                                                                                                                                                                    .annotate(
                                                                                                                                                                        total_amount=Sum('amount'),
                                                                                                                                                                        usage_count=Count('id')
                                                                                                                                                                    )
                                                                                                                                                                    .order_by('-total_amount')
                                                                                                                                                                )

                                                                                                                                                                # تحليل الأداء المالي حسب نوع الباقة
                                                                                                                                                                package_performance = (TourPackage.objects
                                                                                                                                                                    .filter(is_deleted=False)
                                                                                                                                                                    .annotate(
                                                                                                                                                                        total_revenue=Sum('reservations__total_amount'),
                                                                                                                                                                        booking_count=Count('reservations'),
                                                                                                                                                                        avg_booking_value=Avg('reservations__total_amount')
                                                                                                                                                                    )
                                                                                                                                                                    .order_by('-total_revenue')
                                                                                                                                                                )

                                                                                                                                                                context.update({
                                                                                                                                                                    'monthly_revenue': list(monthly_revenue),
                                                                                                                                                                    'payment_methods': payment_methods,
                                                                                                                                                                    'package_performance': package_performance,
                                                                                                                                                                    'yearly_summary': {
                                                                                                                                                                        'total_revenue': Invoice.objects.filter(
                                                                                                                                                                            created_at__year=current_year,
                                                                                                                                                                            status='paid',
                                                                                                                                                                            is_deleted=False
                                                                                                                                                                        ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                                                                                                                        'total_bookings': Reservation.objects.filter(
                                                                                                                                                                            created_at__year=current_year,
                                                                                                                                                                            is_deleted=False
                                                                                                                                                                        ).count(),
                                                                                                                                                                        'average_booking_value': Invoice.objects.filter(
                                                                                                                                                                            created_at__year=current_year,
                                                                                                                                                                            status='paid',
                                                                                                                                                                            is_deleted=False
                                                                                                                                                                        ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                                                                                                                    }
                                                                                                                                                                })

                                                                                                                                                                return context                                                                                                                                                                class FinancialReportsView(TemplateView):
                                                                                                                                                                    """تقارير مالية تفصيلية."""
                                                                                                                                                                    template_name = 'reports/financial.html'

                                                                                                                                                                    def get_context_data(self, **kwargs):
                                                                                                                                                                        context = super().get_context_data(**kwargs)
                                                                                                                                                                        today = timezone.now().date()
                                                                                                                                                                        current_year = today.year

                                                                                                                                                                        # تحليل الإيرادات الشهرية
                                                                                                                                                                        monthly_revenue = (Invoice.objects
                                                                                                                                                                            .filter(
                                                                                                                                                                                created_at__year=current_year,
                                                                                                                                                                                is_deleted=False,
                                                                                                                                                                                status='paid'
                                                                                                                                                                            )
                                                                                                                                                                            .annotate(month=TruncMonth('created_at'))
                                                                                                                                                                            .values('month')
                                                                                                                                                                            .annotate(
                                                                                                                                                                                total_revenue=Sum('total_amount'),
                                                                                                                                                                                booking_count=Count('id'),
                                                                                                                                                                                average_value=Avg('total_amount')
                                                                                                                                                                            )
                                                                                                                                                                            .order_by('month')
                                                                                                                                                                        )

                                                                                                                                                                        # تحليل طرق الدفع
                                                                                                                                                                        payment_methods = (Payment.objects
                                                                                                                                                                            .filter(
                                                                                                                                                                                created_at__year=current_year,
                                                                                                                                                                                is_deleted=False
                                                                                                                                                                            )
                                                                                                                                                                            .values('method')
                                                                                                                                                                            .annotate(
                                                                                                                                                                                total_amount=Sum('amount'),
                                                                                                                                                                                usage_count=Count('id')
                                                                                                                                                                            )
                                                                                                                                                                            .order_by('-total_amount')
                                                                                                                                                                        )

                                                                                                                                                                        # تحليل الأداء المالي حسب نوع الباقة
                                                                                                                                                                        package_performance = (TourPackage.objects
                                                                                                                                                                            .filter(is_deleted=False)
                                                                                                                                                                            .annotate(
                                                                                                                                                                                total_revenue=Sum('reservations__total_amount'),
                                                                                                                                                                                booking_count=Count('reservations'),
                                                                                                                                                                                avg_booking_value=Avg('reservations__total_amount')
                                                                                                                                                                            )
                                                                                                                                                                            .order_by('-total_revenue')
                                                                                                                                                                        )

                                                                                                                                                                        context.update({
                                                                                                                                                                            'monthly_revenue': list(monthly_revenue),
                                                                                                                                                                            'payment_methods': payment_methods,
                                                                                                                                                                            'package_performance': package_performance,
                                                                                                                                                                            'yearly_summary': {
                                                                                                                                                                                'total_revenue': Invoice.objects.filter(
                                                                                                                                                                                    created_at__year=current_year,
                                                                                                                                                                                    status='paid',
                                                                                                                                                                                    is_deleted=False
                                                                                                                                                                                ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                                                                                                                                'total_bookings': Reservation.objects.filter(
                                                                                                                                                                                    created_at__year=current_year,
                                                                                                                                                                                    is_deleted=False
                                                                                                                                                                                ).count(),
                                                                                                                                                                                'average_booking_value': Invoice.objects.filter(
                                                                                                                                                                                    created_at__year=current_year,
                                                                                                                                                                                    status='paid',
                                                                                                                                                                                    is_deleted=False
                                                                                                                                                                                ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                                                                                                                            }
                                                                                                                                                                        })

                                                                                                                                                                        return context                                                                                                                                                                        class FinancialReportsView(TemplateView):
                                                                                                                                                                            """تقارير مالية تفصيلية."""
                                                                                                                                                                            template_name = 'reports/financial.html'

                                                                                                                                                                            def get_context_data(self, **kwargs):
                                                                                                                                                                                context = super().get_context_data(**kwargs)
                                                                                                                                                                                today = timezone.now().date()
                                                                                                                                                                                current_year = today.year

                                                                                                                                                                                # تحليل الإيرادات الشهرية
                                                                                                                                                                                monthly_revenue = (Invoice.objects
                                                                                                                                                                                    .filter(
                                                                                                                                                                                        created_at__year=current_year,
                                                                                                                                                                                        is_deleted=False,
                                                                                                                                                                                        status='paid'
                                                                                                                                                                                    )
                                                                                                                                                                                    .annotate(month=TruncMonth('created_at'))
                                                                                                                                                                                    .values('month')
                                                                                                                                                                                    .annotate(
                                                                                                                                                                                        total_revenue=Sum('total_amount'),
                                                                                                                                                                                        booking_count=Count('id'),
                                                                                                                                                                                        average_value=Avg('total_amount')
                                                                                                                                                                                    )
                                                                                                                                                                                    .order_by('month')
                                                                                                                                                                                )

                                                                                                                                                                                # تحليل طرق الدفع
                                                                                                                                                                                payment_methods = (Payment.objects
                                                                                                                                                                                    .filter(
                                                                                                                                                                                        created_at__year=current_year,
                                                                                                                                                                                        is_deleted=False
                                                                                                                                                                                    )
                                                                                                                                                                                    .values('method')
                                                                                                                                                                                    .annotate(
                                                                                                                                                                                        total_amount=Sum('amount'),
                                                                                                                                                                                        usage_count=Count('id')
                                                                                                                                                                                    )
                                                                                                                                                                                    .order_by('-total_amount')
                                                                                                                                                                                )

                                                                                                                                                                                # تحليل الأداء المالي حسب نوع الباقة
                                                                                                                                                                                package_performance = (TourPackage.objects
                                                                                                                                                                                    .filter(is_deleted=False)
                                                                                                                                                                                    .annotate(
                                                                                                                                                                                        total_revenue=Sum('reservations__total_amount'),
                                                                                                                                                                                        booking_count=Count('reservations'),
                                                                                                                                                                                        avg_booking_value=Avg('reservations__total_amount')
                                                                                                                                                                                    )
                                                                                                                                                                                    .order_by('-total_revenue')
                                                                                                                                                                                )

                                                                                                                                                                                context.update({
                                                                                                                                                                                    'monthly_revenue': list(monthly_revenue),
                                                                                                                                                                                    'payment_methods': payment_methods,
                                                                                                                                                                                    'package_performance': package_performance,
                                                                                                                                                                                    'yearly_summary': {
                                                                                                                                                                                        'total_revenue': Invoice.objects.filter(
                                                                                                                                                                                            created_at__year=current_year,
                                                                                                                                                                                            status='paid',
                                                                                                                                                                                            is_deleted=False
                                                                                                                                                                                        ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                                                                                                                                        'total_bookings': Reservation.objects.filter(
                                                                                                                                                                                            created_at__year=current_year,
                                                                                                                                                                                            is_deleted=False
                                                                                                                                                                                        ).count(),
                                                                                                                                                                                        'average_booking_value': Invoice.objects.filter(
                                                                                                                                                                                            created_at__year=current_year,
                                                                                                                                                                                            status='paid',
                                                                                                                                                                                            is_deleted=False
                                                                                                                                                                                        ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                                                                                                                                    }
                                                                                                                                                                                })

                                                                                                                                                                                return context                                                                                                                                                                                class FinancialReportsView(TemplateView):
                                                                                                                                                                                    """تقارير مالية تفصيلية."""
                                                                                                                                                                                    template_name = 'reports/financial.html'

                                                                                                                                                                                    def get_context_data(self, **kwargs):
                                                                                                                                                                                        context = super().get_context_data(**kwargs)
                                                                                                                                                                                        today = timezone.now().date()
                                                                                                                                                                                        current_year = today.year

                                                                                                                                                                                        # تحليل الإيرادات الشهرية
                                                                                                                                                                                        monthly_revenue = (Invoice.objects
                                                                                                                                                                                            .filter(
                                                                                                                                                                                                created_at__year=current_year,
                                                                                                                                                                                                is_deleted=False,
                                                                                                                                                                                                status='paid'
                                                                                                                                                                                            )
                                                                                                                                                                                            .annotate(month=TruncMonth('created_at'))
                                                                                                                                                                                            .values('month')
                                                                                                                                                                                            .annotate(
                                                                                                                                                                                                total_revenue=Sum('total_amount'),
                                                                                                                                                                                                booking_count=Count('id'),
                                                                                                                                                                                                average_value=Avg('total_amount')
                                                                                                                                                                                            )
                                                                                                                                                                                            .order_by('month')
                                                                                                                                                                                        )

                                                                                                                                                                                        # تحليل طرق الدفع
                                                                                                                                                                                        payment_methods = (Payment.objects
                                                                                                                                                                                            .filter(
                                                                                                                                                                                                created_at__year=current_year,
                                                                                                                                                                                                is_deleted=False
                                                                                                                                                                                            )
                                                                                                                                                                                            .values('method')
                                                                                                                                                                                            .annotate(
                                                                                                                                                                                                total_amount=Sum('amount'),
                                                                                                                                                                                                usage_count=Count('id')
                                                                                                                                                                                            )
                                                                                                                                                                                            .order_by('-total_amount')
                                                                                                                                                                                        )

                                                                                                                                                                                        # تحليل الأداء المالي حسب نوع الباقة
                                                                                                                                                                                        package_performance = (TourPackage.objects
                                                                                                                                                                                            .filter(is_deleted=False)
                                                                                                                                                                                            .annotate(
                                                                                                                                                                                                total_revenue=Sum('reservations__total_amount'),
                                                                                                                                                                                                booking_count=Count('reservations'),
                                                                                                                                                                                                avg_booking_value=Avg('reservations__total_amount')
                                                                                                                                                                                            )
                                                                                                                                                                                            .order_by('-total_revenue')
                                                                                                                                                                                        )

                                                                                                                                                                                        context.update({
                                                                                                                                                                                            'monthly_revenue': list(monthly_revenue),
                                                                                                                                                                                            'payment_methods': payment_methods,
                                                                                                                                                                                            'package_performance': package_performance,
                                                                                                                                                                                            'yearly_summary': {
                                                                                                                                                                                                'total_revenue': Invoice.objects.filter(
                                                                                                                                                                                                    created_at__year=current_year,
                                                                                                                                                                                                    status='paid',
                                                                                                                                                                                                    is_deleted=False
                                                                                                                                                                                                ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                                                                                                                                                'total_bookings': Reservation.objects.filter(
                                                                                                                                                                                                    created_at__year=current_year,
                                                                                                                                                                                                    is_deleted=False
                                                                                                                                                                                                ).count(),
                                                                                                                                                                                                'average_booking_value': Invoice.objects.filter(
                                                                                                                                                                                                    created_at__year=current_year,
                                                                                                                                                                                                    status='paid',
                                                                                                                                                                                                    is_deleted=False
                                                                                                                                                                                                ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                                                                                                                                            }
                                                                                                                                                                                        })

                                                                                                                                                                                        return context                                                                                                                                                                                        class FinancialReportsView(TemplateView):
                                                                                                                                                                                            """تقارير مالية تفصيلية."""
                                                                                                                                                                                            template_name = 'reports/financial.html'

                                                                                                                                                                                            def get_context_data(self, **kwargs):
                                                                                                                                                                                                context = super().get_context_data(**kwargs)
                                                                                                                                                                                                today = timezone.now().date()
                                                                                                                                                                                                current_year = today.year

                                                                                                                                                                                                # تحليل الإيرادات الشهرية
                                                                                                                                                                                                monthly_revenue = (Invoice.objects
                                                                                                                                                                                                    .filter(
                                                                                                                                                                                                        created_at__year=current_year,
                                                                                                                                                                                                        is_deleted=False,
                                                                                                                                                                                                        status='paid'
                                                                                                                                                                                                    )
                                                                                                                                                                                                    .annotate(month=TruncMonth('created_at'))
                                                                                                                                                                                                    .values('month')
                                                                                                                                                                                                    .annotate(
                                                                                                                                                                                                        total_revenue=Sum('total_amount'),
                                                                                                                                                                                                        booking_count=Count('id'),
                                                                                                                                                                                                        average_value=Avg('total_amount')
                                                                                                                                                                                                    )
                                                                                                                                                                                                    .order_by('month')
                                                                                                                                                                                                )

                                                                                                                                                                                                # تحليل طرق الدفع
                                                                                                                                                                                                payment_methods = (Payment.objects
                                                                                                                                                                                                    .filter(
                                                                                                                                                                                                        created_at__year=current_year,
                                                                                                                                                                                                        is_deleted=False
                                                                                                                                                                                                    )
                                                                                                                                                                                                    .values('method')
                                                                                                                                                                                                    .annotate(
                                                                                                                                                                                                        total_amount=Sum('amount'),
                                                                                                                                                                                                        usage_count=Count('id')
                                                                                                                                                                                                    )
                                                                                                                                                                                                    .order_by('-total_amount')
                                                                                                                                                                                                )

                                                                                                                                                                                                # تحليل الأداء المالي حسب نوع الباقة
                                                                                                                                                                                                package_performance = (TourPackage.objects
                                                                                                                                                                                                    .filter(is_deleted=False)
                                                                                                                                                                                                    .annotate(
                                                                                                                                                                                                        total_revenue=Sum('reservations__total_amount'),
                                                                                                                                                                                                        booking_count=Count('reservations'),
                                                                                                                                                                                                        avg_booking_value=Avg('reservations__total_amount')
                                                                                                                                                                                                    )
                                                                                                                                                                                                    .order_by('-total_revenue')
                                                                                                                                                                                                )

                                                                                                                                                                                                context.update({
                                                                                                                                                                                                    'monthly_revenue': list(monthly_revenue),
                                                                                                                                                                                                    'payment_methods': payment_methods,
                                                                                                                                                                                                    'package_performance': package_performance,
                                                                                                                                                                                                    'yearly_summary': {
                                                                                                                                                                                                        'total_revenue': Invoice.objects.filter(
                                                                                                                                                                                                            created_at__year=current_year,
                                                                                                                                                                                                            status='paid',
                                                                                                                                                                                                            is_deleted=False
                                                                                                                                                                                                        ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                                                                                                                                                        'total_bookings': Reservation.objects.filter(
                                                                                                                                                                                                            created_at__year=current_year,
                                                                                                                                                                                                            is_deleted=False
                                                                                                                                                                                                        ).count(),
                                                                                                                                                                                                        'average_booking_value': Invoice.objects.filter(
                                                                                                                                                                                                            created_at__year=current_year,
                                                                                                                                                                                                            status='paid',
                                                                                                                                                                                                            is_deleted=False
                                                                                                                                                                                                        ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                                                                                                                                                    }
                                                                                                                                                                                                })

                                                                                                                                                                                                return context                                                                                                                                                                                                class FinancialReportsView(TemplateView):
                                                                                                                                                                                                    """تقارير مالية تفصيلية."""
                                                                                                                                                                                                    template_name = 'reports/financial.html'

                                                                                                                                                                                                    def get_context_data(self, **kwargs):
                                                                                                                                                                                                        context = super().get_context_data(**kwargs)
                                                                                                                                                                                                        today = timezone.now().date()
                                                                                                                                                                                                        current_year = today.year

                                                                                                                                                                                                        # تحليل الإيرادات الشهرية
                                                                                                                                                                                                        monthly_revenue = (Invoice.objects
                                                                                                                                                                                                            .filter(
                                                                                                                                                                                                                created_at__year=current_year,
                                                                                                                                                                                                                is_deleted=False,
                                                                                                                                                                                                                status='paid'
                                                                                                                                                                                                            )
                                                                                                                                                                                                            .annotate(month=TruncMonth('created_at'))
                                                                                                                                                                                                            .values('month')
                                                                                                                                                                                                            .annotate(
                                                                                                                                                                                                                total_revenue=Sum('total_amount'),
                                                                                                                                                                                                                booking_count=Count('id'),
                                                                                                                                                                                                                average_value=Avg('total_amount')
                                                                                                                                                                                                            )
                                                                                                                                                                                                            .order_by('month')
                                                                                                                                                                                                        )

                                                                                                                                                                                                        # تحليل طرق الدفع
                                                                                                                                                                                                        payment_methods = (Payment.objects
                                                                                                                                                                                                            .filter(
                                                                                                                                                                                                                created_at__year=current_year,
                                                                                                                                                                                                                is_deleted=False
                                                                                                                                                                                                            )
                                                                                                                                                                                                            .values('method')
                                                                                                                                                                                                            .annotate(
                                                                                                                                                                                                                total_amount=Sum('amount'),
                                                                                                                                                                                                                usage_count=Count('id')
                                                                                                                                                                                                            )
                                                                                                                                                                                                            .order_by('-total_amount')
                                                                                                                                                                                                        )

                                                                                                                                                                                                        # تحليل الأداء المالي حسب نوع الباقة
                                                                                                                                                                                                        package_performance = (TourPackage.objects
                                                                                                                                                                                                            .filter(is_deleted=False)
                                                                                                                                                                                                            .annotate(
                                                                                                                                                                                                                total_revenue=Sum('reservations__total_amount'),
                                                                                                                                                                                                                booking_count=Count('reservations'),
                                                                                                                                                                                                                avg_booking_value=Avg('reservations__total_amount')
                                                                                                                                                                                                            )
                                                                                                                                                                                                            .order_by('-total_revenue')
                                                                                                                                                                                                        )

                                                                                                                                                                                                        context.update({
                                                                                                                                                                                                            'monthly_revenue': list(monthly_revenue),
                                                                                                                                                                                                            'payment_methods': payment_methods,
                                                                                                                                                                                                            'package_performance': package_performance,
                                                                                                                                                                                                            'yearly_summary': {
                                                                                                                                                                                                                'total_revenue': Invoice.objects.filter(
                                                                                                                                                                                                                    created_at__year=current_year,
                                                                                                                                                                                                                    status='paid',
                                                                                                                                                                                                                    is_deleted=False
                                                                                                                                                                                                                ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                                                                                                                                                                'total_bookings': Reservation.objects.filter(
                                                                                                                                                                                                                    created_at__year=current_year,
                                                                                                                                                                                                                    is_deleted=False
                                                                                                                                                                                                                ).count(),
                                                                                                                                                                                                                'average_booking_value': Invoice.objects.filter(
                                                                                                                                                                                                                    created_at__year=current_year,
                                                                                                                                                                                                                    status='paid',
                                                                                                                                                                                                                    is_deleted=False
                                                                                                                                                                                                                ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                                                                                                                                                            }
                                                                                                                                                                                                        })

                                                                                                                                                                                                        return context                                                                                                                                                                                                        class FinancialReportsView(TemplateView):
                                                                                                                                                                                                            """تقارير مالية تفصيلية."""
                                                                                                                                                                                                            template_name = 'reports/financial.html'

                                                                                                                                                                                                            def get_context_data(self, **kwargs):
                                                                                                                                                                                                                context = super().get_context_data(**kwargs)
                                                                                                                                                                                                                today = timezone.now().date()
                                                                                                                                                                                                                current_year = today.year

                                                                                                                                                                                                                # تحليل الإيرادات الشهرية
                                                                                                                                                                                                                monthly_revenue = (Invoice.objects
                                                                                                                                                                                                                    .filter(
                                                                                                                                                                                                                        created_at__year=current_year,
                                                                                                                                                                                                                        is_deleted=False,
                                                                                                                                                                                                                        status='paid'
                                                                                                                                                                                                                    )
                                                                                                                                                                                                                    .annotate(month=TruncMonth('created_at'))
                                                                                                                                                                                                                    .values('month')
                                                                                                                                                                                                                    .annotate(
                                                                                                                                                                                                                        total_revenue=Sum('total_amount'),
                                                                                                                                                                                                                        booking_count=Count('id'),
                                                                                                                                                                                                                        average_value=Avg('total_amount')
                                                                                                                                                                                                                    )
                                                                                                                                                                                                                    .order_by('month')
                                                                                                                                                                                                                )

                                                                                                                                                                                                                # تحليل طرق الدفع
                                                                                                                                                                                                                payment_methods = (Payment.objects
                                                                                                                                                                                                                    .filter(
                                                                                                                                                                                                                        created_at__year=current_year,
                                                                                                                                                                                                                        is_deleted=False
                                                                                                                                                                                                                    )
                                                                                                                                                                                                                    .values('method')
                                                                                                                                                                                                                    .annotate(
                                                                                                                                                                                                                        total_amount=Sum('amount'),
                                                                                                                                                                                                                        usage_count=Count('id')
                                                                                                                                                                                                                    )
                                                                                                                                                                                                                    .order_by('-total_amount')
                                                                                                                                                                                                                )

                                                                                                                                                                                                                # تحليل الأداء المالي حسب نوع الباقة
                                                                                                                                                                                                                package_performance = (TourPackage.objects
                                                                                                                                                                                                                    .filter(is_deleted=False)
                                                                                                                                                                                                                    .annotate(
                                                                                                                                                                                                                        total_revenue=Sum('reservations__total_amount'),
                                                                                                                                                                                                                        booking_count=Count('reservations'),
                                                                                                                                                                                                                        avg_booking_value=Avg('reservations__total_amount')
                                                                                                                                                                                                                    )
                                                                                                                                                                                                                    .order_by('-total_revenue')
                                                                                                                                                                                                                )

                                                                                                                                                                                                                context.update({
                                                                                                                                                                                                                    'monthly_revenue': list(monthly_revenue),
                                                                                                                                                                                                                    'payment_methods': payment_methods,
                                                                                                                                                                                                                    'package_performance': package_performance,
                                                                                                                                                                                                                    'yearly_summary': {
                                                                                                                                                                                                                        'total_revenue': Invoice.objects.filter(
                                                                                                                                                                                                                            created_at__year=current_year,
                                                                                                                                                                                                                            status='paid',
                                                                                                                                                                                                                            is_deleted=False
                                                                                                                                                                                                                        ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                                                                                                                                                                        'total_bookings': Reservation.objects.filter(
                                                                                                                                                                                                                            created_at__year=current_year,
                                                                                                                                                                                                                            is_deleted=False
                                                                                                                                                                                                                        ).count(),
                                                                                                                                                                                                                        'average_booking_value': Invoice.objects.filter(
                                                                                                                                                                                                                            created_at__year=current_year,
                                                                                                                                                                                                                            status='paid',
                                                                                                                                                                                                                            is_deleted=False
                                                                                                                                                                                                                        ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                                                                                                                                                                    }
                                                                                                                                                                                                                })

                                                                                                                                                                                                                return context                                                                                                                                                                                                                class FinancialReportsView(TemplateView):
                                                                                                                                                                                                                    """تقارير مالية تفصيلية."""
                                                                                                                                                                                                                    template_name = 'reports/financial.html'

                                                                                                                                                                                                                    def get_context_data(self, **kwargs):
                                                                                                                                                                                                                        context = super().get_context_data(**kwargs)
                                                                                                                                                                                                                        today = timezone.now().date()
                                                                                                                                                                                                                        current_year = today.year

                                                                                                                                                                                                                        # تحليل الإيرادات الشهرية
                                                                                                                                                                                                                        monthly_revenue = (Invoice.objects
                                                                                                                                                                                                                            .filter(
                                                                                                                                                                                                                                created_at__year=current_year,
                                                                                                                                                                                                                                is_deleted=False,
                                                                                                                                                                                                                                status='paid'
                                                                                                                                                                                                                            )
                                                                                                                                                                                                                            .annotate(month=TruncMonth('created_at'))
                                                                                                                                                                                                                            .values('month')
                                                                                                                                                                                                                            .annotate(
                                                                                                                                                                                                                                total_revenue=Sum('total_amount'),
                                                                                                                                                                                                                                booking_count=Count('id'),
                                                                                                                                                                                                                                average_value=Avg('total_amount')
                                                                                                                                                                                                                            )
                                                                                                                                                                                                                            .order_by('month')
                                                                                                                                                                                                                        )

                                                                                                                                                                                                                        # تحليل طرق الدفع
                                                                                                                                                                                                                        payment_methods = (Payment.objects
                                                                                                                                                                                                                            .filter(
                                                                                                                                                                                                                                created_at__year=current_year,
                                                                                                                                                                                                                                is_deleted=False
                                                                                                                                                                                                                            )
                                                                                                                                                                                                                            .values('method')
                                                                                                                                                                                                                            .annotate(
                                                                                                                                                                                                                                total_amount=Sum('amount'),
                                                                                                                                                                                                                                usage_count=Count('id')
                                                                                                                                                                                                                            )
                                                                                                                                                                                                                            .order_by('-total_amount')
                                                                                                                                                                                                                        )

                                                                                                                                                                                                                        # تحليل الأداء المالي حسب نوع الباقة
                                                                                                                                                                                                                        package_performance = (TourPackage.objects
                                                                                                                                                                                                                            .filter(is_deleted=False)
                                                                                                                                                                                                                            .annotate(
                                                                                                                                                                                                                                total_revenue=Sum('reservations__total_amount'),
                                                                                                                                                                                                                                booking_count=Count('reservations'),
                                                                                                                                                                                                                                avg_booking_value=Avg('reservations__total_amount')
                                                                                                                                                                                                                            )
                                                                                                                                                                                                                            .order_by('-total_revenue')
                                                                                                                                                                                                                        )

                                                                                                                                                                                                                        context.update({
                                                                                                                                                                                                                            'monthly_revenue': list(monthly_revenue),
                                                                                                                                                                                                                            'payment_methods': payment_methods,
                                                                                                                                                                                                                            'package_performance': package_performance,
                                                                                                                                                                                                                            'yearly_summary': {
                                                                                                                                                                                                                                'total_revenue': Invoice.objects.filter(
                                                                                                                                                                                                                                    created_at__year=current_year,
                                                                                                                                                                                                                                    status='paid',
                                                                                                                                                                                                                                    is_deleted=False
                                                                                                                                                                                                                                ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                                                                                                                                                                                'total_bookings': Reservation.objects.filter(
                                                                                                                                                                                                                                    created_at__year=current_year,
                                                                                                                                                                                                                                    is_deleted=False
                                                                                                                                                                                                                                ).count(),
                                                                                                                                                                                                                                'average_booking_value': Invoice.objects.filter(
                                                                                                                                                                                                                                    created_at__year=current_year,
                                                                                                                                                                                                                                    status='paid',
                                                                                                                                                                                                                                    is_deleted=False
                                                                                                                                                                                                                                ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                                                                                                                                                                            }
                                                                                                                                                                                                                        })

                                                                                                                                                                                                                        return context                                                                                                                                                                                                                        class FinancialReportsView(TemplateView):
                                                                                                                                                                                                                            """تقارير مالية تفصيلية."""
                                                                                                                                                                                                                            template_name = 'reports/financial.html'

                                                                                                                                                                                                                            def get_context_data(self, **kwargs):
                                                                                                                                                                                                                                context = super().get_context_data(**kwargs)
                                                                                                                                                                                                                                today = timezone.now().date()
                                                                                                                                                                                                                                current_year = today.year

                                                                                                                                                                                                                                # تحليل الإيرادات الشهرية
                                                                                                                                                                                                                                monthly_revenue = (Invoice.objects
                                                                                                                                                                                                                                    .filter(
                                                                                                                                                                                                                                        created_at__year=current_year,
                                                                                                                                                                                                                                        is_deleted=False,
                                                                                                                                                                                                                                        status='paid'
                                                                                                                                                                                                                                    )
                                                                                                                                                                                                                                    .annotate(month=TruncMonth('created_at'))
                                                                                                                                                                                                                                    .values('month')
                                                                                                                                                                                                                                    .annotate(
                                                                                                                                                                                                                                        total_revenue=Sum('total_amount'),
                                                                                                                                                                                                                                        booking_count=Count('id'),
                                                                                                                                                                                                                                        average_value=Avg('total_amount')
                                                                                                                                                                                                                                    )
                                                                                                                                                                                                                                    .order_by('month')
                                                                                                                                                                                                                                )

                                                                                                                                                                                                                                # تحليل طرق الدفع
                                                                                                                                                                                                                                payment_methods = (Payment.objects
                                                                                                                                                                                                                                    .filter(
                                                                                                                                                                                                                                        created_at__year=current_year,
                                                                                                                                                                                                                                        is_deleted=False
                                                                                                                                                                                                                                    )
                                                                                                                                                                                                                                    .values('method')
                                                                                                                                                                                                                                    .annotate(
                                                                                                                                                                                                                                        total_amount=Sum('amount'),
                                                                                                                                                                                                                                        usage_count=Count('id')
                                                                                                                                                                                                                                    )
                                                                                                                                                                                                                                    .order_by('-total_amount')
                                                                                                                                                                                                                                )

                                                                                                                                                                                                                                # تحليل الأداء المالي حسب نوع الباقة
                                                                                                                                                                                                                                package_performance = (TourPackage.objects
                                                                                                                                                                                                                                    .filter(is_deleted=False)
                                                                                                                                                                                                                                    .annotate(
                                                                                                                                                                                                                                        total_revenue=Sum('reservations__total_amount'),
                                                                                                                                                                                                                                        booking_count=Count('reservations'),
                                                                                                                                                                                                                                        avg_booking_value=Avg('reservations__total_amount')
                                                                                                                                                                                                                                    )
                                                                                                                                                                                                                                    .order_by('-total_revenue')
                                                                                                                                                                                                                                )

                                                                                                                                                                                                                                context.update({
                                                                                                                                                                                                                                    'monthly_revenue': list(monthly_revenue),
                                                                                                                                                                                                                                    'payment_methods': payment_methods,
                                                                                                                                                                                                                                    'package_performance': package_performance,
                                                                                                                                                                                                                                    'yearly_summary': {
                                                                                                                                                                                                                                        'total_revenue': Invoice.objects.filter(
                                                                                                                                                                                                                                            created_at__year=current_year,
                                                                                                                                                                                                                                            status='paid',
                                                                                                                                                                                                                                            is_deleted=False
                                                                                                                                                                                                                                        ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                                                                                                                                                                                        'total_bookings': Reservation.objects.filter(
                                                                                                                                                                                                                                            created_at__year=current_year,
                                                                                                                                                                                                                                            is_deleted=False
                                                                                                                                                                                                                                        ).count(),
                                                                                                                                                                                                                                        'average_booking_value': Invoice.objects.filter(
                                                                                                                                                                                                                                            created_at__year=current_year,
                                                                                                                                                                                                                                            status='paid',
                                                                                                                                                                                                                                            is_deleted=False
                                                                                                                                                                                                                                        ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                })

                                                                                                                                                                                                                                return context                                                                                                                                                                                                                                class FinancialReportsView(TemplateView):
                                                                                                                                                                                                                                    """تقارير مالية تفصيلية."""
                                                                                                                                                                                                                                    template_name = 'reports/financial.html'

                                                                                                                                                                                                                                    def get_context_data(self, **kwargs):
                                                                                                                                                                                                                                        context = super().get_context_data(**kwargs)
                                                                                                                                                                                                                                        today = timezone.now().date()
                                                                                                                                                                                                                                        current_year = today.year

                                                                                                                                                                                                                                        # تحليل الإيرادات الشهرية
                                                                                                                                                                                                                                        monthly_revenue = (Invoice.objects
                                                                                                                                                                                                                                            .filter(
                                                                                                                                                                                                                                                created_at__year=current_year,
                                                                                                                                                                                                                                                is_deleted=False,
                                                                                                                                                                                                                                                status='paid'
                                                                                                                                                                                                                                            )
                                                                                                                                                                                                                                            .annotate(month=TruncMonth('created_at'))
                                                                                                                                                                                                                                            .values('month')
                                                                                                                                                                                                                                            .annotate(
                                                                                                                                                                                                                                                total_revenue=Sum('total_amount'),
                                                                                                                                                                                                                                                booking_count=Count('id'),
                                                                                                                                                                                                                                                average_value=Avg('total_amount')
                                                                                                                                                                                                                                            )
                                                                                                                                                                                                                                            .order_by('month')
                                                                                                                                                                                                                                        )

                                                                                                                                                                                                                                        # تحليل طرق الدفع
                                                                                                                                                                                                                                        payment_methods = (Payment.objects
                                                                                                                                                                                                                                            .filter(
                                                                                                                                                                                                                                                created_at__year=current_year,
                                                                                                                                                                                                                                                is_deleted=False
                                                                                                                                                                                                                                            )
                                                                                                                                                                                                                                            .values('method')
                                                                                                                                                                                                                                            .annotate(
                                                                                                                                                                                                                                                total_amount=Sum('amount'),
                                                                                                                                                                                                                                                usage_count=Count('id')
                                                                                                                                                                                                                                            )
                                                                                                                                                                                                                                            .order_by('-total_amount')
                                                                                                                                                                                                                                        )

                                                                                                                                                                                                                                        # تحليل الأداء المالي حسب نوع الباقة
                                                                                                                                                                                                                                        package_performance = (TourPackage.objects
                                                                                                                                                                                                                                            .filter(is_deleted=False)
                                                                                                                                                                                                                                            .annotate(
                                                                                                                                                                                                                                                total_revenue=Sum('reservations__total_amount'),
                                                                                                                                                                                                                                                booking_count=Count('reservations'),
                                                                                                                                                                                                                                                avg_booking_value=Avg('reservations__total_amount')
                                                                                                                                                                                                                                            )
                                                                                                                                                                                                                                            .order_by('-total_revenue')
                                                                                                                                                                                                                                        )

                                                                                                                                                                                                                                        context.update({
                                                                                                                                                                                                                                            'monthly_revenue': list(monthly_revenue),
                                                                                                                                                                                                                                            'payment_methods': payment_methods,
                                                                                                                                                                                                                                            'package_performance': package_performance,
                                                                                                                                                                                                                                            'yearly_summary': {
                                                                                                                                                                                                                                                'total_revenue': Invoice.objects.filter(
                                                                                                                                                                                                                                                    created_at__year=current_year,
                                                                                                                                                                                                                                                    status='paid',
                                                                                                                                                                                                                                                    is_deleted=False
                                                                                                                                                                                                                                                ).aggregate(total=Sum('total_amount'))['total'] or 0,
                                                                                                                                                                                                                                                'total_bookings': Reservation.objects.filter(
                                                                                                                                                                                                                                                    created_at__year=current_year,
                                                                                                                                                                                                                                                    is_deleted=False
                                                                                                                                                                                                                                                ).count(),
                                                                                                                                                                                                                                                'average_booking_value': Invoice.objects.filter(
                                                                                                                                                                                                                                                    created_at__year=current_year,
                                                                                                                                                                                                                                                    status='paid',
                                                                                                                                                                                                                                                    is_deleted=False
                                                                                                                                                                                                                                                ).aggregate(avg=Avg('total_amount'))['avg'] or 0
                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                        })

                                                                                                                                                                                                                                        return context

class FinancialReportsView(TemplateView):
    """تقارير مالية تفصيلية."""
    template_name = 'reports/financial.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        today = timezone.now().date()
        current_year = today.year

        # تحليل الإيرادات الشهرية
        monthly_revenue = (Invoice.objects
            .filter(
                created_at__year=current_year,
                is_deleted=False,
                status='paid'
            )
            .annotate(month=TruncMonth('created_at'))
            .values('month')
            .annotate(
                total_revenue=Sum('total_amount'),
                booking_count=Count('id'),
                average_value=Avg('total_amount')
            )
            .order_by('month')
        )

        # تحليل طرق الدفع
        payment_methods = (Payment.objects
            .filter(
                created_at__year=current_year,
                is_deleted=False
            )
            .values('method')
            .annotate(
                total_amount=Sum('amount'),
                usage_count=Count('id')
            )
            .order_by('-total_amount')
        )

        # تحليل الأداء المالي حسب نوع الباقة
        package_performance = (TourPackage.objects
            .filter(is_deleted=False)
            .annotate(
                total_revenue=Sum('reservations__total_amount'),
                booking_count=Count('reservations'),
                avg_booking_value=Avg('reservations__total_amount')
            )
            .order_by('-total_revenue')
        )

        context.update({
            'monthly_revenue': list(monthly_revenue),
            'payment_methods': payment_methods,
            'package_performance': package_performance,
            'yearly_summary': {
                'total_revenue': Invoice.objects.filter(
                    created_at__year=current_year,
                    status='paid',
                    is_deleted=False
                ).aggregate(total=Sum('total_amount'))['total'] or 0,
                'total_bookings': Reservation.objects.filter(
                    created_at__year=current_year,
                    is_deleted=False
                ).count(),
                'average_booking_value': Invoice.objects.filter(
                    created_at__year=current_year,
                    status='paid',
                    is_deleted=False
                ).aggregate(avg=Avg('total_amount'))['avg'] or 0
            }
        })

        return context
