{% load i18n %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% trans "تقارير شاملة" %} - نظام إدارة وكالة السفر المغربية</title>

    <!-- Bootstrap CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background-color: #f8f9fa;
        }
        .report-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            text-align: center;
        }
        .report-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        .report-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-bar me-2"></i>
                {% trans "تقارير النظام" %}
            </a>

            <div class="navbar-nav me-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-home me-1"></i>
                    {% trans "الرئيسية" %}
                </a>
                <a class="nav-link" href="/reports/clients/">
                    <i class="fas fa-users me-1"></i>
                    {% trans "تقارير العملاء" %}
                </a>
                <a class="nav-link" href="/reports/packages/">
                    <i class="fas fa-map-marked-alt me-1"></i>
                    {% trans "تقارير الباقات" %}
                </a>
                <a class="nav-link" href="/admin/">
                    <i class="fas fa-cog me-1"></i>
                    {% trans "الإدارة" %}
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-4">
                    <i class="fas fa-chart-line me-2"></i>
                    {% trans "لوحة التقارير الشاملة" %}
                </h1>
            </div>
        </div>

        <!-- Summary Statistics -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="report-card">
                    <div class="report-number">{{ total_clients }}</div>
                    <div class="report-label">{% trans "إجمالي العملاء" %}</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="report-card">
                    <div class="report-number">{{ total_packages }}</div>
                    <div class="report-label">{% trans "الباقات السياحية" %}</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="report-card">
                    <div class="report-number">{{ total_destinations }}</div>
                    <div class="report-label">{% trans "الوجهات المتاحة" %}</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="report-card">
                    <div class="report-number">{{ vip_clients }}</div>
                    <div class="report-label">{% trans "عملاء VIP" %}</div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <!-- Client Distribution -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>
                            {% trans "توزيع العملاء حسب النوع" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="clientTypeChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Package Distribution -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-map-marked-alt me-2"></i>
                            {% trans "توزيع الباقات حسب الفئة" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="packageCategoryChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Statistics -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">{% trans "إحصائيات العملاء" %}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <h4 class="text-primary">{{ individual_clients }}</h4>
                                <small>{% trans "أفراد" %}</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-success">{{ corporate_clients }}</h4>
                                <small>{% trans "شركات" %}</small>
                            </div>
                        </div>
                        <hr>
                        <div class="row text-center">
                            <div class="col-6">
                                <h5 class="text-info">{{ new_clients_7_days }}</h5>
                                <small>{% trans "جدد (7 أيام)" %}</small>
                            </div>
                            <div class="col-6">
                                <h5 class="text-warning">{{ new_clients_30_days }}</h5>
                                <small>{% trans "جدد (30 يوم)" %}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">{% trans "إحصائيات الباقات" %}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <h4 class="text-success">{{ active_packages }}</h4>
                                <small>{% trans "نشطة" %}</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-warning">{{ featured_packages }}</h4>
                                <small>{% trans "مميزة" %}</small>
                            </div>
                        </div>
                        <hr>
                        <div class="row text-center">
                            <div class="col-6">
                                <h5 class="text-info">{{ total_categories }}</h5>
                                <small>{% trans "الفئات" %}</small>
                            </div>
                            <div class="col-6">
                                <h5 class="text-primary">{{ total_destinations }}</h5>
                                <small>{% trans "الوجهات" %}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">{% trans "إحصائيات النظام" %}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <h4 class="text-primary">{{ total_users }}</h4>
                                <small>{% trans "المستخدمين" %}</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-success">{{ active_users }}</h4>
                                <small>{% trans "النشطين" %}</small>
                            </div>
                        </div>
                        <hr>
                        <div class="row text-center">
                            <div class="col-6">
                                <h5 class="text-info">{{ total_countries }}</h5>
                                <small>{% trans "الدول" %}</small>
                            </div>
                            <div class="col-6">
                                <h5 class="text-warning">{{ total_cities }}</h5>
                                <small>{% trans "المدن" %}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-clock me-2"></i>
                            {% trans "العملاء الجدد" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{% trans "الاسم" %}</th>
                                        <th>{% trans "النوع" %}</th>
                                        <th>{% trans "الجنسية" %}</th>
                                        <th>{% trans "تاريخ التسجيل" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for client in recent_clients %}
                                    <tr>
                                        <td>{{ client.full_name_ar }}</td>
                                        <td>
                                            {% if client.client_type == 'individual' %}
                                                <span class="badge bg-primary">{% trans "فردي" %}</span>
                                            {% else %}
                                                <span class="badge bg-success">{% trans "شركة" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ client.nationality.name_ar|default:"غير محدد" }}</td>
                                        <td>{{ client.created_at|date:"d/m/Y" }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-map me-2"></i>
                            {% trans "الباقات الحديثة" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        {% for package in recent_packages %}
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <strong>{{ package.title_ar }}</strong>
                                <br>
                                <small class="text-muted">{{ package.category.name_ar }}</small>
                            </div>
                            <span class="badge bg-primary">{{ package.base_price }} MAD</span>
                        </div>
                        {% if not forloop.last %}<hr>{% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Charts -->
    <script>
        // Client Type Chart
        const clientTypeCtx = document.getElementById('clientTypeChart').getContext('2d');
        new Chart(clientTypeCtx, {
            type: 'doughnut',
            data: {
                labels: ['أفراد', 'شركات'],
                datasets: [{
                    data: [{{ individual_clients }}, {{ corporate_clients }}],
                    backgroundColor: ['#36A2EB', '#4BC0C0']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Package Category Chart
        const packageCategoryCtx = document.getElementById('packageCategoryChart').getContext('2d');
        const packageCategoryData = [
            {% for item in package_by_category %}
                {% if item.category__name_ar %}'{{ item.category__name_ar }}'{% else %}'غير محدد'{% endif %}{% if not forloop.last %},{% endif %}
            {% empty %}
                'لا توجد بيانات'
            {% endfor %}
        ];
        const packageCategoryValues = [
            {% for item in package_by_category %}
                {{ item.count }}{% if not forloop.last %},{% endif %}
            {% empty %}
                0
            {% endfor %}
        ];

        new Chart(packageCategoryCtx, {
            type: 'bar',
            data: {
                labels: packageCategoryData,
                datasets: [{
                    label: 'عدد الباقات',
                    data: packageCategoryValues,
                    backgroundColor: '#FF6384'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>
