# Generated by Django 4.2.7 on 2025-06-12 20:35

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('suppliers', '0001_initial'),
        ('finance', '0002_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='expense',
            name='vendor',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='suppliers.supplier', verbose_name='المورد'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['payment_number'], name='finance_pay_payment_bb9d71_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['invoice', 'status'], name='finance_pay_invoice_80fc36_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['payment_date'], name='finance_pay_payment_72f761_idx'),
        ),
        migrations.AddIndex(
            model_name='invoice',
            index=models.Index(fields=['invoice_number'], name='finance_inv_invoice_89da8a_idx'),
        ),
        migrations.AddIndex(
            model_name='invoice',
            index=models.Index(fields=['client', 'status'], name='finance_inv_client__cc0479_idx'),
        ),
        migrations.AddIndex(
            model_name='invoice',
            index=models.Index(fields=['due_date'], name='finance_inv_due_dat_b93853_idx'),
        ),
        migrations.AddIndex(
            model_name='expense',
            index=models.Index(fields=['expense_number'], name='finance_exp_expense_d6be0d_idx'),
        ),
        migrations.AddIndex(
            model_name='expense',
            index=models.Index(fields=['category', 'status'], name='finance_exp_categor_cbc2fa_idx'),
        ),
        migrations.AddIndex(
            model_name='expense',
            index=models.Index(fields=['expense_date'], name='finance_exp_expense_a60cc7_idx'),
        ),
    ]
