"""
URL configuration for Moroccan Travel Agency ERP project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.conf.urls.i18n import i18n_patterns
# from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView, SpectacularRedocView

# API URLs (without i18n)
api_urlpatterns = [
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),
    path('api/auth/', include('apps.accounts.api_urls')),
    path('api/crm/', include('apps.crm.api_urls')),
    path('api/tours/', include('apps.tours.api_urls')),
    path('api/reservations/', include('apps.reservations.api_urls')),
    path('api/finance/', include('apps.finance.api_urls')),
    path('api/suppliers/', include('apps.suppliers.api_urls')),
    path('api/hr/', include('apps.hr.api_urls')),
    path('api/reports/', include('apps.reports.api_urls')),
    path('o/', include('oauth2_provider.urls', namespace='oauth2_provider')),
]

# Main URLs with i18n support
urlpatterns = [
    path('admin/', admin.site.urls),
    path('accounts/', include('allauth.urls')),
] + api_urlpatterns

# Add i18n patterns for web interface
urlpatterns += i18n_patterns(
    path('', include('apps.core.urls')),
    path('crm/', include('apps.crm.urls')),
    path('tours/', include('apps.tours.urls')),
    path('reservations/', include('apps.reservations.urls')),
    path('finance/', include('apps.finance.urls')),
    path('suppliers/', include('apps.suppliers.urls')),
    path('hr/', include('apps.hr.urls')),
    path('reports/', include('apps.reports.urls')),
    prefix_default_language=False,
)

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

# Admin site customization
admin.site.site_header = "نظام إدارة وكالة السفر المغربية"
admin.site.site_title = "ERP Admin"
admin.site.index_title = "لوحة التحكم الرئيسية"
