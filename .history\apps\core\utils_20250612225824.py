"""
Utility functions for the Moroccan Travel Agency ERP system.
"""
import os
import uuid
from django.utils.text import slugify
from django.core.files.storage import default_storage
from django.conf import settings
from PIL import Image
import io


def generate_unique_filename(instance, filename):
    """Generate a unique filename for uploaded files."""
    ext = filename.split('.')[-1]
    filename = f"{uuid.uuid4().hex}.{ext}"
    return filename


def upload_client_document(instance, filename):
    """Upload path for client documents."""
    filename = generate_unique_filename(instance, filename)
    return f"clients/{instance.client.client_code}/documents/{filename}"


def upload_client_avatar(instance, filename):
    """Upload path for client avatars."""
    filename = generate_unique_filename(instance, filename)
    return f"clients/{instance.client_code}/avatar/{filename}"


def upload_package_image(instance, filename):
    """Upload path for package images."""
    filename = generate_unique_filename(instance, filename)
    return f"packages/{instance.package_code}/images/{filename}"


def upload_destination_image(instance, filename):
    """Upload path for destination images."""
    filename = generate_unique_filename(instance, filename)
    slug = slugify(instance.name_ar)
    return f"destinations/{slug}/images/{filename}"


def compress_image(image_file, max_size=(800, 600), quality=85):
    """Compress and resize image."""
    try:
        img = Image.open(image_file)

        # Convert to RGB if necessary
        if img.mode in ('RGBA', 'LA', 'P'):
            img = img.convert('RGB')

        # Resize if larger than max_size
        img.thumbnail(max_size, Image.Resampling.LANCZOS)

        # Save to BytesIO
        output = io.BytesIO()
        img.save(output, format='JPEG', quality=quality, optimize=True)
        output.seek(0)

        return output
    except Exception as e:
        # Return original file if compression fails
        return image_file


def format_currency(amount, currency='MAD'):
    """Format currency amount."""
    if amount is None:
        return f"0.00 {currency}"
    return f"{amount:,.2f} {currency}"


def format_phone_number(phone, country_code='+212'):
    """Format phone number with country code."""
    if not phone:
        return ""

    # Remove any existing country code
    if phone.startswith(country_code):
        return phone
    elif phone.startswith('0'):
        return f"{country_code}{phone[1:]}"
    else:
        return f"{country_code}{phone}"


def calculate_age(birth_date):
    """Calculate age from birth date."""
    if not birth_date:
        return None

    from datetime import date
    today = date.today()
    return today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))


def generate_client_code():
    """Generate unique client code."""
    from datetime import date
    import random

    year = date.today().year
    random_num = random.randint(1000, 9999)
    return f"CL{year}{random_num}"


def generate_package_code():
    """Generate unique package code."""
    import random
    import string

    letters = ''.join(random.choices(string.ascii_uppercase, k=3))
    numbers = ''.join(random.choices(string.digits, k=3))
    return f"{letters}{numbers}"


def send_notification_email(to_email, subject, message, template=None):
    """Send notification email."""
    from django.core.mail import send_mail
    from django.template.loader import render_to_string

    try:
        if template:
            html_message = render_to_string(template, {'message': message})
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[to_email],
                html_message=html_message,
                fail_silently=False,
            )
        else:
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[to_email],
                fail_silently=False,
            )
        return True
    except Exception as e:
        print(f"Failed to send email: {e}")
        return False


def export_to_excel(data, filename, sheet_name='Sheet1'):
    """Export data to Excel file."""
    try:
        import pandas as pd
        from django.http import HttpResponse

        df = pd.DataFrame(data)

        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = f'attachment; filename="{filename}.xlsx"'

        with pd.ExcelWriter(response, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=sheet_name, index=False)

        return response
    except ImportError:
        # Fallback to CSV if pandas/openpyxl not available
        return export_to_csv(data, filename)


def export_to_csv(data, filename):
    """Export data to CSV file."""
    import csv
    from django.http import HttpResponse

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="{filename}.csv"'

    if data:
        writer = csv.DictWriter(response, fieldnames=data[0].keys())
        writer.writeheader()
        writer.writerows(data)

    return response


def validate_moroccan_phone(phone):
    """Validate Moroccan phone number."""
    import re

    # Remove spaces and special characters
    phone = re.sub(r'[^\d+]', '', phone)

    # Moroccan phone patterns
    patterns = [
        r'^\+212[5-7]\d{8}$',  # +212XXXXXXXXX
        r'^0[5-7]\d{8}$',      # 0XXXXXXXXX
        r'^[5-7]\d{8}$',       # XXXXXXXXX
    ]

    return any(re.match(pattern, phone) for pattern in patterns)


def get_client_statistics():
    """Get client statistics for dashboard."""
    from apps.crm.models import Client
    from django.db.models import Count, Avg

    stats = {
        'total_clients': Client.objects.count(),
        'vip_clients': Client.objects.filter(vip_status=True).count(),
        'individual_clients': Client.objects.filter(client_type='individual').count(),
        'corporate_clients': Client.objects.filter(client_type='corporate').count(),
        'avg_loyalty_points': Client.objects.aggregate(avg=Avg('loyalty_points'))['avg'] or 0,
    }

    # Client distribution by nationality (safe query)
    try:
        nationality_stats = Client.objects.select_related('nationality').exclude(
            nationality__isnull=True
        ).values('nationality__name_ar').annotate(
            count=Count('id')
        ).order_by('-count')[:5]
    except Exception:
        # Fallback: get nationality distribution without join
        nationality_stats = []
        for client in Client.objects.select_related('nationality').exclude(nationality__isnull=True)[:5]:
            if client.nationality:
                nationality_stats.append({
                    'nationality__name_ar': client.nationality.name_ar,
                    'count': 1
                })

    stats['top_nationalities'] = nationality_stats

    return stats


def get_package_statistics():
    """Get package statistics for dashboard."""
    from apps.tours.models import TourPackage, Destination
    from django.db.models import Count, Avg, Min, Max

    packages = TourPackage.objects.filter(is_active=True)

    stats = {
        'total_packages': packages.count(),
        'featured_packages': packages.filter(is_featured=True).count(),
        'total_destinations': Destination.objects.filter(is_active=True).count(),
        'avg_price': packages.aggregate(avg=Avg('base_price'))['avg'] or 0,
        'min_price': packages.aggregate(min=Min('base_price'))['min'] or 0,
        'max_price': packages.aggregate(max=Max('base_price'))['max'] or 0,
        'avg_duration': packages.aggregate(avg=Avg('duration_days'))['avg'] or 0,
    }

    # Package distribution by category
    category_stats = packages.values('category__name_ar').annotate(
        count=Count('id')
    ).order_by('-count')

    stats['category_distribution'] = category_stats

    return stats
