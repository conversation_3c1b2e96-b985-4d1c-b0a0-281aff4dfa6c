"""
Django admin configuration for Finance app.
"""
from django.contrib import admin
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from .models import Invoice, InvoiceItem, Payment, Expense, BankAccount


class InvoiceItemInline(admin.TabularInline):
    """Inline admin for Invoice items."""
    model = InvoiceItem
    extra = 1
    fields = ['description', 'quantity', 'unit_price', 'total_price']
    readonly_fields = ['total_price']


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    """Admin interface for Invoice model."""
    list_display = ['invoice_number', 'client', 'total_amount', 'status', 'issue_date', 'due_date', 'is_overdue_display']
    list_filter = ['status', 'issue_date', 'due_date', 'created_at']
    search_fields = ['invoice_number', 'client__first_name_ar', 'client__last_name_ar', 'client__email']
    list_editable = ['status']
    readonly_fields = ['invoice_number', 'paid_amount', 'remaining_amount', 'created_at', 'updated_at']
    inlines = [InvoiceItemInline]
    date_hierarchy = 'issue_date'

    def is_overdue_display(self, obj):
        """Display overdue status with color."""
        if obj.is_overdue:
            return format_html('<span style="color: red;">متأخرة</span>')
        return format_html('<span style="color: green;">في الوقت</span>')
    is_overdue_display.short_description = _('حالة الاستحقاق')

    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('invoice_number', 'reservation', 'client')
        }),
        (_('التواريخ'), {
            'fields': ('issue_date', 'due_date')
        }),
        (_('المبالغ'), {
            'fields': ('subtotal', 'tax_rate', 'tax_amount', 'discount_amount', 'total_amount')
        }),
        (_('الدفع'), {
            'fields': ('paid_amount', 'remaining_amount'),
            'classes': ('collapse',)
        }),
        (_('الحالة'), {
            'fields': ('status',)
        }),
        (_('ملاحظات'), {
            'fields': ('notes', 'terms_and_conditions'),
            'classes': ('collapse',)
        }),
        (_('معلومات النظام'), {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        """Set created_by field when creating new invoice."""
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    """Admin interface for Payment model."""
    list_display = ['payment_number', 'invoice', 'amount', 'payment_method', 'status', 'payment_date']
    list_filter = ['status', 'payment_method', 'payment_date', 'created_at']
    search_fields = ['payment_number', 'invoice__invoice_number', 'transaction_id', 'reference_number']
    list_editable = ['status']
    readonly_fields = ['payment_number', 'created_at', 'updated_at']
    date_hierarchy = 'payment_date'

    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('payment_number', 'invoice', 'reservation')
        }),
        (_('تفاصيل الدفع'), {
            'fields': ('amount', 'payment_method', 'payment_date')
        }),
        (_('الحالة'), {
            'fields': ('status',)
        }),
        (_('تفاصيل المعاملة'), {
            'fields': ('transaction_id', 'reference_number'),
            'classes': ('collapse',)
        }),
        (_('تفاصيل البنك/البطاقة'), {
            'fields': ('bank_name', 'card_last_four'),
            'classes': ('collapse',)
        }),
        (_('ملاحظات'), {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
        (_('معلومات النظام'), {
            'fields': ('processed_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        """Set processed_by field when creating new payment."""
        if not change:
            obj.processed_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Expense)
class ExpenseAdmin(admin.ModelAdmin):
    """Admin interface for Expense model."""
    list_display = ['expense_number', 'title', 'category', 'amount', 'status', 'expense_date', 'submitted_by']
    list_filter = ['status', 'category', 'expense_date', 'created_at']
    search_fields = ['expense_number', 'title', 'description', 'vendor_name']
    list_editable = ['status']
    readonly_fields = ['expense_number', 'total_amount', 'created_at', 'updated_at']
    date_hierarchy = 'expense_date'

    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('expense_number', 'title', 'description', 'category')
        }),
        (_('المبلغ'), {
            'fields': ('amount', 'tax_amount', 'total_amount')
        }),
        (_('التاريخ'), {
            'fields': ('expense_date',)
        }),
        (_('الحالة'), {
            'fields': ('status',)
        }),
        (_('المورد'), {
            'fields': ('vendor', 'vendor_name'),
            'classes': ('collapse',)
        }),
        (_('الإيصال'), {
            'fields': ('receipt',),
            'classes': ('collapse',)
        }),
        (_('الموافقة'), {
            'fields': ('approved_by', 'approval_date'),
            'classes': ('collapse',)
        }),
        (_('معلومات النظام'), {
            'fields': ('submitted_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        """Set submitted_by field when creating new expense."""
        if not change:
            obj.submitted_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(BankAccount)
class BankAccountAdmin(admin.ModelAdmin):
    """Admin interface for BankAccount model."""
    list_display = ['account_name', 'bank_name', 'account_number', 'current_balance', 'is_active', 'is_default']
    list_filter = ['is_active', 'is_default', 'bank_name']
    search_fields = ['account_name', 'bank_name', 'account_number', 'iban']
    list_editable = ['is_active', 'is_default']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('account_name', 'bank_name')
        }),
        (_('تفاصيل الحساب'), {
            'fields': ('account_number', 'iban', 'swift_code')
        }),
        (_('الرصيد'), {
            'fields': ('current_balance',)
        }),
        (_('الحالة'), {
            'fields': ('is_active', 'is_default')
        }),
        (_('معلومات الاتصال'), {
            'fields': ('branch_name', 'contact_person', 'phone'),
            'classes': ('collapse',)
        }),
        (_('معلومات النظام'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
