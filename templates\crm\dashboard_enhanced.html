{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
<style>
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
        transition: transform 0.3s ease;
    }
    .stats-card:hover {
        transform: translateY(-5px);
    }
    .stats-icon {
        font-size: 2.5rem;
        opacity: 0.8;
    }
    .chart-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 20px;
    }
    .recent-clients-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .client-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-users me-2 text-primary"></i>
                {{ page_title }}
            </h1>
            <p class="text-muted mb-0">{% trans "نظرة عامة على إدارة العملاء" %}</p>
        </div>
        <div>
            <a href="{% url 'crm:client_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                {% trans "إضافة عميل جديد" %}
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">
                                {% trans "إجمالي العملاء" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold">{{ total_clients }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users stats-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card h-100" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">
                                {% trans "العملاء المميزون" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold">{{ vip_clients }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-crown stats-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card h-100" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">
                                {% trans "عملاء جدد هذا الشهر" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold">{{ new_clients_this_month }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-plus stats-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card h-100" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">
                                {% trans "معدل النمو" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold">+12%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line stats-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Recent Data -->
    <div class="row">
        <!-- Client Types Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="chart-container mb-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2 text-primary"></i>
                        {% trans "توزيع أنواع العملاء" %}
                    </h5>
                </div>
                <canvas id="clientTypesChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Recent Clients -->
        <div class="col-xl-4 col-lg-5">
            <div class="recent-clients-card">
                <div class="card-header bg-transparent border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2 text-primary"></i>
                        {% trans "العملاء الجدد" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% for client in recent_clients %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="client-avatar me-3">
                            {{ client.first_name_ar|first }}{{ client.last_name_ar|first }}
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-0">{{ client.full_name_ar }}</h6>
                            <small class="text-muted">{{ client.email }}</small>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">{{ client.created_at|date:"M d" }}</small>
                            {% if client.vip_status %}
                            <br><span class="badge bg-warning text-dark">VIP</span>
                            {% endif %}
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted text-center">{% trans "لا توجد عملاء جدد" %}</p>
                    {% endfor %}
                </div>
                <div class="card-footer bg-transparent border-0">
                    <a href="{% url 'crm:client_list' %}" class="btn btn-outline-primary btn-sm w-100">
                        {% trans "عرض جميع العملاء" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2 text-primary"></i>
                        {% trans "إجراءات سريعة" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'crm:client_create' %}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-user-plus mb-2 d-block"></i>
                                {% trans "إضافة عميل" %}
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'crm:client_list' %}" class="btn btn-outline-info w-100">
                                <i class="fas fa-search mb-2 d-block"></i>
                                {% trans "البحث في العملاء" %}
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="#" class="btn btn-outline-success w-100">
                                <i class="fas fa-file-export mb-2 d-block"></i>
                                {% trans "تصدير البيانات" %}
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="#" class="btn btn-outline-warning w-100">
                                <i class="fas fa-chart-bar mb-2 d-block"></i>
                                {% trans "التقارير" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script>
// Client Types Chart
const ctx = document.getElementById('clientTypesChart').getContext('2d');
const clientTypesChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: [
            {% for type in client_types %}
            '{{ type.client_type }}',
            {% endfor %}
        ],
        datasets: [{
            data: [
                {% for type in client_types %}
                {{ type.count }},
                {% endfor %}
            ],
            backgroundColor: [
                '#667eea',
                '#764ba2',
                '#f093fb',
                '#f5576c',
                '#4facfe',
                '#00f2fe'
            ],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 20,
                    usePointStyle: true
                }
            }
        }
    }
});
</script>
{% endblock %}
