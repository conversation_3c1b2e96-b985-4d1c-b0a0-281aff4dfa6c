# Generated by Django 4.2.7 on 2025-06-13 09:05

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import phonenumber_field.modelfields


class Migration(migrations.Migration):

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='account_locked_until',
            field=models.DateTimeField(blank=True, null=True, verbose_name='الحساب مقفل حتى'),
        ),
        migrations.AddField(
            model_name='user',
            name='avatar',
            field=models.ImageField(blank=True, upload_to='avatars/', verbose_name='الصورة الشخصية'),
        ),
        migrations.AddField(
            model_name='user',
            name='department',
            field=models.CharField(blank=True, max_length=50, verbose_name='القسم'),
        ),
        migrations.AddField(
            model_name='user',
            name='emergency_contact_name',
            field=models.CharField(blank=True, max_length=100, verbose_name='اسم جهة الاتصال الطارئ'),
        ),
        migrations.AddField(
            model_name='user',
            name='emergency_contact_phone',
            field=phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=128, region=None, verbose_name='هاتف جهة الاتصال الطارئ'),
        ),
        migrations.AddField(
            model_name='user',
            name='failed_login_attempts',
            field=models.PositiveIntegerField(default=0, verbose_name='محاولات تسجيل الدخول الفاشلة'),
        ),
        migrations.AddField(
            model_name='user',
            name='hire_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ التوظيف'),
        ),
        migrations.AddField(
            model_name='user',
            name='is_active_employee',
            field=models.BooleanField(default=True, verbose_name='موظف نشط'),
        ),
        migrations.AddField(
            model_name='user',
            name='last_activity',
            field=models.DateTimeField(blank=True, null=True, verbose_name='آخر نشاط'),
        ),
        migrations.AddField(
            model_name='user',
            name='last_password_change',
            field=models.DateTimeField(blank=True, null=True, verbose_name='آخر تغيير كلمة مرور'),
        ),
        migrations.AddField(
            model_name='user',
            name='login_count',
            field=models.PositiveIntegerField(default=0, verbose_name='عدد مرات تسجيل الدخول'),
        ),
        migrations.AddField(
            model_name='user',
            name='notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات'),
        ),
        migrations.AddField(
            model_name='user',
            name='salary',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='الراتب'),
        ),
        migrations.AddField(
            model_name='user',
            name='termination_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ إنهاء الخدمة'),
        ),
        migrations.AddField(
            model_name='user',
            name='termination_reason',
            field=models.TextField(blank=True, verbose_name='سبب إنهاء الخدمة'),
        ),
        migrations.AddField(
            model_name='user',
            name='two_factor_enabled',
            field=models.BooleanField(default=False, verbose_name='المصادقة الثنائية مفعلة'),
        ),
        migrations.AlterField(
            model_name='user',
            name='phone',
            field=phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=128, region=None, verbose_name='رقم الهاتف'),
        ),
        migrations.CreateModel(
            name='UserSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('session_key', models.CharField(max_length=40, unique=True, verbose_name='مفتاح الجلسة')),
                ('ip_address', models.GenericIPAddressField(verbose_name='عنوان IP')),
                ('user_agent', models.TextField(verbose_name='معلومات المتصفح')),
                ('login_time', models.DateTimeField(auto_now_add=True, verbose_name='وقت تسجيل الدخول')),
                ('last_activity', models.DateTimeField(auto_now=True, verbose_name='آخر نشاط')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('logout_time', models.DateTimeField(blank=True, null=True, verbose_name='وقت تسجيل الخروج')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'جلسة مستخدم',
                'verbose_name_plural': 'جلسات المستخدمين',
                'ordering': ['-login_time'],
            },
        ),
        migrations.CreateModel(
            name='UserPermissionGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم المجموعة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('permissions', models.ManyToManyField(blank=True, to='auth.permission', verbose_name='الصلاحيات')),
            ],
            options={
                'verbose_name': 'مجموعة صلاحيات',
                'verbose_name_plural': 'مجموعات الصلاحيات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ActivityLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('action', models.CharField(choices=[('login', 'تسجيل دخول'), ('logout', 'تسجيل خروج'), ('create', 'إنشاء'), ('update', 'تحديث'), ('delete', 'حذف'), ('view', 'عرض'), ('export', 'تصدير'), ('import', 'استيراد'), ('backup', 'نسخ احتياطي'), ('restore', 'استعادة')], max_length=20, verbose_name='الإجراء')),
                ('content_type', models.CharField(blank=True, max_length=100, verbose_name='نوع المحتوى')),
                ('object_id', models.CharField(blank=True, max_length=100, verbose_name='معرف الكائن')),
                ('object_repr', models.CharField(blank=True, max_length=200, verbose_name='تمثيل الكائن')),
                ('changes', models.JSONField(blank=True, default=dict, verbose_name='التغييرات')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('user_agent', models.TextField(blank=True, verbose_name='معلومات المتصفح')),
                ('session_key', models.CharField(blank=True, max_length=40, verbose_name='مفتاح الجلسة')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'سجل النشاط',
                'verbose_name_plural': 'سجلات النشاط',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'action'], name='accounts_ac_user_id_bfb4b4_idx'), models.Index(fields=['created_at'], name='accounts_ac_created_936747_idx'), models.Index(fields=['content_type', 'object_id'], name='accounts_ac_content_6b971f_idx')],
            },
        ),
    ]
