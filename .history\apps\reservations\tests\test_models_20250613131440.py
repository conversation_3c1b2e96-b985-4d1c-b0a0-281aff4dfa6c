"""
Test suite for Reservation model and related models.
"""
from django.test import TestCase
from django.utils import timezone
from django.core.exceptions import ValidationError
from apps.reservations.models import (
    Reservation,
    ReservationParticipant,
    ReservationService,
    ReservationDocument
)
from apps.accounts.models import User
from apps.crm.models import Client
from apps.tours.models import TourPackage, TourCategory
from djmoney.money import Money


class TestReservationModel(TestCase):
    """Test cases for the Reservation model and related functionality."""

    @classmethod
    def setUpTestData(cls):
        """Set up data for all test methods."""
        # Create test user
        cls.user = User.objects.create(username="testuser")

        # Create test client
        cls.client = Client.objects.create(
            client_code="CL123",
            first_name_ar="Test",
            last_name_ar="Client",
            phone="*********",
            email="<EMAIL>"
        )
          # Create test package
        cls.category = TourCategory.objects.create(
            name_ar="فئة اختبار",
            name_fr="Catégorie test",
            name_en="Test category"
        )

        cls.package = TourPackage.objects.create(
            package_code="PKG001",
            title_ar="باقة اختبار",
            title_fr="Package Test",
            title_en="Test Package",
            short_description_ar="وصف الباقة",
            short_description_fr="Description du package",
            short_description_en="Package Description",
            detailed_description_ar="وصف مفصل للباقة",
            detailed_description_fr="Description détaillée du package",
            detailed_description_en="Detailed package description",
            duration_days=5,
            min_participants=2,
            max_participants=10,
            category=cls.category
        )

    def test_create_reservation(self):
        """Test basic reservation creation."""
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD')
        )

        self.assertEqual(reservation.adults, 2)
        self.assertTrue(reservation.reservation_number.startswith('RES'))
        self.assertEqual(reservation.client.id, self.client.id)
        self.assertEqual(reservation.package.id, self.package.id)

    def test_reservation_validation(self):
        """Test reservation validation rules."""
        # Test dates validation
        with self.assertRaises(ValidationError):
            invalid_dates = Reservation(
                client=self.client,
                package=self.package,
                departure_date=timezone.now().date(),
                return_date=timezone.now().date() - timezone.timedelta(days=1),
                adults=2,
                adult_price=Money(1000, 'MAD')
            )
            invalid_dates.full_clean()

        # Test participant numbers validation
        with self.assertRaises(ValidationError):
            invalid_participants = Reservation(
                client=self.client,
                package=self.package,
                departure_date=timezone.now().date(),
                return_date=timezone.now().date() + timezone.timedelta(days=5),
                adults=0,
                adult_price=Money(1000, 'MAD')
            )
            invalid_participants.full_clean()

    def test_price_calculations(self):
        """Test price calculations for reservations."""
        prices = {
            'adult': 999.99,
            'child': 499.99,
            'infant': 0,
            'discount': 99.99,
            'tax': 199.99
        }

        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            children=1,
            infants=1,
            adult_price=Money(prices['adult'], 'MAD'),
            child_price=Money(prices['child'], 'MAD'),
            infant_price=Money(prices['infant'], 'MAD'),
            discount_amount=Money(prices['discount'], 'MAD'),
            tax_amount=Money(prices['tax'], 'MAD')
        )

        expected = {
            'subtotal': (2 * prices['adult']) + prices['child'],
            'total': 0
        }
        expected['total'] = (
            expected['subtotal'] - prices['discount'] + prices['tax']
        )

        self.assertAlmostEqual(
            float(reservation.subtotal.amount),
            expected['subtotal'],
            places=2
        )
        self.assertAlmostEqual(
            float(reservation.total_amount.amount),
            expected['total'],
            places=2
        )

    def test_participant_management(self):
        """Test participant management functionality."""
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=1,
            adult_price=Money(1000, 'MAD')
        )

        # Test adding participants of different types
        birthdates = {
            'adult': timezone.timedelta(days=365 * 25),
            'child': timezone.timedelta(days=365 * 8),
            'infant': timezone.timedelta(days=180)
        }

        participants = [
            ('John', 'Doe', 'adult'),
            ('Jane', 'Doe', 'child'),
            ('Baby', 'Doe', 'infant')
        ]

        for first, last, ptype in participants:
            dob = timezone.now().date() - birthdates[ptype]
            participant = ReservationParticipant.objects.create(
                reservation=reservation,
                first_name=first,
                last_name=last,
                date_of_birth=dob,
                participant_type=ptype
            )
            self.assertEqual(participant.participant_type, ptype)

    def test_service_management(self):
        """Test service management functionality."""
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD')
        )

        # Test adding and calculating services
        services = [
            ('transfer', 'Airport Transfer', 200, 2),
            ('activity', 'Desert Tour', 500, 2),
            ('accommodation', 'Extra Night', 800, 1)
        ]

        for stype, name, price, qty in services:
            service = ReservationService.objects.create(
                reservation=reservation,
                service_type=stype,
                name=name,
                unit_price=Money(price, 'MAD'),
                quantity=qty
            )
            self.assertEqual(
                float(service.total_price.amount),
                price * qty
            )

        total = sum(
            float(s.total_price.amount)
            for s in reservation.services.all()
        )
        expected = sum(price * qty for _, _, price, qty in services)
        self.assertEqual(total, expected)

    def test_document_management(self):
        """Test document management functionality."""
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD')
        )

        # Test adding documents
        docs = [
            ('passport', 'Passport Copy', False),
            ('visa', 'Visa Copy', False),
            ('contract', 'Contract', True)
        ]

        for doc_type, title, is_public in docs:
            doc = ReservationDocument.objects.create(
                reservation=reservation,
                document_type=doc_type,
                title=title,
                is_public=is_public
            )
            self.assertEqual(doc.document_type, doc_type)
            self.assertEqual(doc.is_public, is_public)

        self.assertEqual(reservation.documents.count(), len(docs))

    def test_status_transitions(self):
        """Test reservation status transitions."""
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD'),
            status='pending'
        )

        # Test valid transitions
        valid_transitions = ['confirmed', 'completed', 'cancelled']
        for status in valid_transitions:
            reservation.status = status
            reservation.save()
            self.assertEqual(
                Reservation.objects.get(id=reservation.id).status,
                status
            )

        # Test invalid transition
        with self.assertRaises(ValidationError):
            reservation.status = 'pending'  # Can't go back to pending
            reservation.full_clean()

    def test_multilingual_fields(self):
        """Test multilingual field handling."""
        notes = {
            'ar': "ملاحظات",
            'fr': "Notes en français",
            'en': "Notes in English"
        }

        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD'),
            notes_ar=notes['ar'],
            notes_fr=notes['fr'],
            notes_en=notes['en']
        )

        for lang, text in notes.items():
            self.assertEqual(
                getattr(reservation, f'notes_{lang}'),
                text
            )

    def test_payment_calculations(self):
        """Test payment amount calculations."""
        # Import at function level to avoid circular imports
        from apps.finance.models import Payment

        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD')
        )

        # Create some payments
        payments = [
            ('completed', 500),
            ('completed', 700),
            ('pending', 300),  # This shouldn't count in paid amount
            ('failed', 200),   # This shouldn't count in paid amount
        ]

        for status, amount in payments:
            Payment.objects.create(
                reservation=reservation,
                amount=Money(amount, 'MAD'),
                status=status
            )

        # Test paid amount (only completed payments)
        self.assertEqual(float(reservation.paid_amount), 1200.0)

        # Test remaining amount
        expected_remaining = float(reservation.total_amount.amount) - 1200.0
        self.assertEqual(float(reservation.remaining_amount), expected_remaining)

        # Test payment status updates
        reservation.refresh_from_db()
        if reservation.remaining_amount > 0 and reservation.paid_amount > 0:
            self.assertEqual(reservation.payment_status, 'partial')
        elif reservation.remaining_amount <= 0:
            self.assertEqual(reservation.payment_status, 'paid')
        else:
            self.assertEqual(reservation.payment_status, 'unpaid')

    def test_package_availability(self):
        """Test package availability relationship and validation."""
        from apps.tours.models import PackageAvailability

        # Create package availability
        availability = PackageAvailability.objects.create(
            package=self.package,
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timezone.timedelta(days=7),
            available_slots=5
        )

        # Test reservation with availability
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            availability=availability,
            departure_date=availability.start_date,
            return_date=availability.end_date,
            adults=2,
            adult_price=Money(1000, 'MAD')
        )

        self.assertEqual(reservation.availability, availability)
        self.assertEqual(
            reservation.departure_date,
            availability.start_date
        )

        # Test validation of dates matching availability
        with self.assertRaises(ValidationError):
            invalid_dates = Reservation(
                client=self.client,
                package=self.package,
                availability=availability,
                departure_date=timezone.now().date() + timezone.timedelta(days=10),
                return_date=timezone.now().date() + timezone.timedelta(days=15),
                adults=2,
                adult_price=Money(1000, 'MAD')
            )
            invalid_dates.full_clean()

    def test_sales_agent_relationship(self):
        """Test sales agent relationship and operations."""
        from apps.accounts.models import User

        # Create sales agent
        agent = User.objects.create_user(
            username='salesagent',
            password='testpass123',
            is_staff=True
        )

        # Test reservation with sales agent
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD'),
            sales_agent=agent
        )

        self.assertEqual(reservation.sales_agent, agent)

        # Test null sales agent
        reservation.sales_agent = None
        reservation.save()
        self.assertIsNone(reservation.sales_agent)

    def test_document_file_operations(self):
        """Test document file operations and validations."""
        from django.core.files.uploadedfile import SimpleUploadedFile
        from django.conf import settings
        import os

        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD')
        )

        # Create a test file
        file_content = b'Test file content'
        test_file = SimpleUploadedFile(
            'test_document.pdf',
            file_content,
            content_type='application/pdf'
        )

        # Create document with file
        document = ReservationDocument.objects.create(
            reservation=reservation,
            document_type='contract',
            title='Test Contract',
            file=test_file,
            is_public=True
        )

        # Test file was saved
        self.assertTrue(os.path.exists(document.file.path))
        self.assertEqual(document.file_size, len(file_content))

        # Test public access
        self.assertTrue(document.is_public)

        # Clean up test file
        document.file.delete()


class TestReservationParticipantModel(TestCase):
    """Test cases for the ReservationParticipant model."""

    @classmethod
    def setUpTestData(cls):
        """Set up data for all test methods."""
        cls.user = User.objects.create(username="testuser")
        cls.client = Client.objects.create(
            client_code="CL123",
            first_name_ar="Test",
            last_name_ar="Client",
            phone="*********",
            email="<EMAIL>"
        )
        # Create test category
        cls.category = TourCategory.objects.create(
            name_ar="فئة اختبار",
            name_fr="Catégorie test",
            name_en="Test category"
        )

        cls.package = TourPackage.objects.create(
            package_code="PKG001",
            title_ar="باقة اختبار",
            title_fr="Package Test",
            title_en="Test Package",
            short_description_ar="وصف الباقة",
            short_description_fr="Description du package",
            short_description_en="Package Description",
            detailed_description_ar="وصف مفصل للباقة",
            detailed_description_fr="Description détaillée du package",
            detailed_description_en="Detailed package description",
            duration_days=5,
            min_participants=2,
            max_participants=10,
            category=cls.category
        )
        cls.reservation = Reservation.objects.create(
            client=cls.client,
            package=cls.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD')
        )

    def test_participant_age_classification(self):
        """Test participant age classification and validation."""
        today = timezone.now().date()

        # Test adult age classification
        adult = ReservationParticipant.objects.create(
            reservation=self.reservation,
            first_name="John",
            last_name="Doe",
            date_of_birth=today - timezone.timedelta(days=365 * 20),
            participant_type='adult'
        )
        self.assertEqual(adult.age, 20)

        # Test child age classification
        child = ReservationParticipant.objects.create(
            reservation=self.reservation,
            first_name="Jane",
            last_name="Doe",
            date_of_birth=today - timezone.timedelta(days=365 * 8),
            participant_type='child'
        )
        self.assertEqual(child.age, 8)

        # Test infant age classification
        infant = ReservationParticipant.objects.create(
            reservation=self.reservation,
            first_name="Baby",
            last_name="Doe",
            date_of_birth=today - timezone.timedelta(days=180),
            participant_type='infant'
        )
        self.assertTrue(infant.age < 2)

    def test_passport_validation(self):
        """Test passport expiry validation."""
        today = timezone.now().date()
        with self.assertRaises(ValidationError):
            participant = ReservationParticipant(
                reservation=self.reservation,
                first_name="John",
                last_name="Doe",
                date_of_birth=today - timezone.timedelta(days=365 * 30),
                participant_type='adult',
                passport_number='A123456',
                passport_expiry=today - timezone.timedelta(days=1)
            )
            participant.full_clean()

    def test_participant_full_name(self):
        """Test full name property."""
        participant = ReservationParticipant.objects.create(
            reservation=self.reservation,
            first_name="John",
            last_name="Doe",
            participant_type='adult'
        )
        self.assertEqual(participant.full_name, "John Doe")


class TestReservationServiceModel(TestCase):
    """Test cases for the ReservationService model."""

    @classmethod
    def setUpTestData(cls):
        """Set up data for all test methods."""
        cls.user = User.objects.create(username="testuser")
        cls.client = Client.objects.create(
            client_code="CL123",
            first_name_ar="Test",
            last_name_ar="Client",
            phone="*********",
            email="<EMAIL>"
        )
        cls.package = TourPackage.objects.create(
            title_ar="باقة اختبار",
            title_fr="Package Test",
            title_en="Test Package",
            description_ar="وصف الباقة",
            description_fr="Description du package",
            description_en="Package Description",
            duration_days=5,
            min_participants=2,
            max_participants=10
        )
        cls.reservation = Reservation.objects.create(
            client=cls.client,
            package=cls.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD')
        )

    def test_service_date_validation(self):
        """Test service date validation."""
        # Test date within reservation period
        service = ReservationService.objects.create(
            reservation=self.reservation,
            service_type='activity',
            name='Desert Tour',
            unit_price=Money(500, 'MAD'),
            quantity=2,
            service_date=self.reservation.departure_date +
                        timezone.timedelta(days=1)
        )
        self.assertTrue(
            self.reservation.departure_date <= service.service_date <=
            self.reservation.return_date
        )

        # Test date outside reservation period
        with self.assertRaises(ValidationError):
            invalid_service = ReservationService(
                reservation=self.reservation,
                service_type='activity',
                name='Desert Tour',
                unit_price=Money(500, 'MAD'),
                quantity=2,
                service_date=self.reservation.departure_date -
                            timezone.timedelta(days=1)
            )
            invalid_service.full_clean()

    def test_service_price_calculations(self):
        """Test service price calculations with different quantities."""
        services = [
            {
                'type': 'transfer',
                'name': 'Airport Transfer',
                'price': 200,
                'qty': 4,
                'optional': True
            },
            {
                'type': 'activity',
                'name': 'Desert Tour',
                'price': 800,
                'qty': 2,
                'optional': False
            }
        ]

        total_mandatory = 0
        total_optional = 0

        for service_data in services:
            service = ReservationService.objects.create(
                reservation=self.reservation,
                service_type=service_data['type'],
                name=service_data['name'],
                unit_price=Money(service_data['price'], 'MAD'),
                quantity=service_data['qty'],
                is_optional=service_data['optional']
            )

            expected_total = service_data['price'] * service_data['qty']
            self.assertEqual(
                float(service.total_price.amount),
                expected_total
            )

            if service_data['optional']:
                total_optional += expected_total
            else:
                total_mandatory += expected_total

        all_services = self.reservation.services.all()
        total_services = sum(
            float(s.total_price.amount) for s in all_services
        )

        self.assertEqual(
            total_services,
            total_mandatory + total_optional
        )


class TestReservationErrorCases(TestCase):
    """Test error cases for reservations."""

    @classmethod
    def setUpTestData(cls):
        """Set up data for all test methods."""
        cls.user = User.objects.create(username="testuser")
        cls.client = Client.objects.create(
            client_code="CL123",
            first_name_ar="Test",
            last_name_ar="Client",
            phone="*********",
            email="<EMAIL>"
        )
        cls.package = TourPackage.objects.create(
            title_ar="باقة اختبار",
            title_fr="Package Test",
            title_en="Test Package",
            description_ar="وصف الباقة",
            description_fr="Description du package",
            description_en="Package Description",
            duration_days=5,
            min_participants=2,
            max_participants=10
        )

    def test_missing_required_fields(self):
        """Test validation of required fields."""
        # Test missing client
        with self.assertRaises(ValidationError):
            reservation = Reservation(
                package=self.package,
                departure_date=timezone.now().date(),
                return_date=timezone.now().date() + timezone.timedelta(days=5),
                adults=2,
                adult_price=Money(1000, 'MAD')
            )
            reservation.full_clean()

        # Test missing package
        with self.assertRaises(ValidationError):
            reservation = Reservation(
                client=self.client,
                departure_date=timezone.now().date(),
                return_date=timezone.now().date() + timezone.timedelta(days=5),
                adults=2,
                adult_price=Money(1000, 'MAD')
            )
            reservation.full_clean()

    def test_invalid_state_transitions(self):
        """Test invalid reservation status transitions."""
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD'),
            status='completed'
        )

        invalid_transitions = [
            'draft',    # Can't go back to draft
            'pending',  # Can't go back to pending
        ]

        for new_status in invalid_transitions:
            with self.assertRaises(ValidationError):
                reservation.status = new_status
                reservation.full_clean()

    def test_invalid_date_ranges(self):
        """Test invalid date range scenarios."""
        today = timezone.now().date()

        invalid_dates = [
            (today, today - timezone.timedelta(days=1)),  # End before start
            (today - timezone.timedelta(days=1), today),  # Start in past
            (today + timezone.timedelta(days=365),
             today + timezone.timedelta(days=366))  # Too far in future
        ]

        for start_date, end_date in invalid_dates:
            with self.assertRaises(ValidationError):
                reservation = Reservation(
                    client=self.client,
                    package=self.package,
                    departure_date=start_date,
                    return_date=end_date,
                    adults=2,
                    adult_price=Money(1000, 'MAD')
                )
                reservation.full_clean()

    def test_invalid_prices(self):
        """Test invalid price scenarios."""
        invalid_prices = [
            -1000,  # Negative price
            0,      # Zero price
        ]

        for price in invalid_prices:
            with self.assertRaises(ValidationError):
                reservation = Reservation(
                    client=self.client,
                    package=self.package,
                    departure_date=timezone.now().date(),
                    return_date=timezone.now().date() +
                              timezone.timedelta(days=5),
                    adults=2,
                    adult_price=Money(price, 'MAD')
                )
                reservation.full_clean()

    def test_over_capacity_booking(self):
        """Test booking over package capacity."""
        with self.assertRaises(ValidationError):
            reservation = Reservation(
                client=self.client,
                package=self.package,
                departure_date=timezone.now().date(),
                return_date=timezone.now().date() + timezone.timedelta(days=5),
                adults=9,
                children=2,  # Total 11 > max_participants 10
                adult_price=Money(1000, 'MAD')
            )
            reservation.full_clean()
