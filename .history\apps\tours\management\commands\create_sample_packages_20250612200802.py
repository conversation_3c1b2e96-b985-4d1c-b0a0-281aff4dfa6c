"""
Management command to create sample tour packages.
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from apps.tours.models import TourPackage, TourCategory, Destination, Itinerary
from decimal import Decimal
import random


class Command(BaseCommand):
    help = 'Create sample tour packages'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('بدء إنشاء باقات سياحية تجريبية...'))

        with transaction.atomic():
            self.create_sample_packages()

        self.stdout.write(self.style.SUCCESS('تم إنشاء الباقات السياحية التجريبية بنجاح!'))

    def create_sample_packages(self):
        """Create sample tour packages."""

        # Get categories and destinations
        cultural_category = TourCategory.objects.get(name_ar='سياحة ثقافية')
        desert_category = TourCategory.objects.get(name_ar='سياحة الصحراء')
        adventure_category = TourCategory.objects.get(name_ar='سياحة المغامرات')
        leisure_category = TourCategory.objects.get(name_ar='سياحة الاستجمام')

        marrakech = Destination.objects.get(name_ar='مراكش الحمراء')
        merzouga = Destination.objects.get(name_ar='صحراء مرزوقة')
        fez = Destination.objects.get(name_ar='فاس العتيقة')
        chefchaouen = Destination.objects.get(name_ar='شفشاون الزرقاء')

        packages_data = [
            {
                'package_code': 'MAR001',
                'title_ar': 'جولة مراكش الكلاسيكية',
                'title_fr': 'Tour Classique de Marrakech',
                'title_en': 'Classic Marrakech Tour',
                'short_description_ar': 'استكشف جمال مراكش الحمراء في رحلة لا تُنسى تشمل زيارة جامع الفنا وقصر البديع والحدائق الأندلسية',
                'short_description_fr': 'Découvrez la beauté de Marrakech la Rouge dans un voyage inoubliable incluant la place Jemaa el-Fna, le Palais El Badi et les jardins andalous',
                'short_description_en': 'Explore the beauty of Red Marrakech in an unforgettable journey including Jemaa el-Fna square, El Badi Palace and Andalusian gardens',
                'detailed_description_ar': 'رحلة شاملة لاستكشاف مراكش الحمراء مع زيارة أهم المعالم التاريخية والثقافية',
                'detailed_description_fr': 'Voyage complet pour explorer Marrakech la Rouge avec visite des principaux sites historiques et culturels',
                'detailed_description_en': 'Complete journey to explore Red Marrakech with visits to major historical and cultural sites',
                'category': cultural_category,
                'duration_days': 3,
                'duration_nights': 2,
                'max_participants': 20,
                'min_participants': 2,
                'difficulty_level': 'easy',
                'base_price': Decimal('1200.00'),
                'child_price': Decimal('800.00'),
                'infant_price': Decimal('200.00'),
                'inclusions': 'الإقامة في فندق 4 نجوم، وجبة الإفطار، النقل، دليل سياحي',
                'exclusions': 'تذاكر الطيران، الوجبات الرئيسية، المشتريات الشخصية',
                'is_active': True,
                'is_featured': True
            },
            {
                'package_code': 'DES001',
                'title_ar': 'مغامرة الصحراء الكبرى',
                'title_fr': 'Aventure du Grand Désert',
                'title_en': 'Great Desert Adventure',
                'short_description_ar': 'تجربة فريدة في صحراء مرزوقة مع ركوب الجمال والمبيت في المخيم الصحراوي تحت النجوم',
                'short_description_fr': 'Une expérience unique dans le désert de Merzouga avec balade à dos de chameau et nuit au camp dans le désert sous les étoiles',
                'short_description_en': 'A unique experience in Merzouga desert with camel riding and overnight stay in desert camp under the stars',
                'detailed_description_ar': 'مغامرة صحراوية شاملة تتضمن رحلة عبر الأطلس الكبير وتجربة الحياة البدوية',
                'detailed_description_fr': 'Aventure complète dans le désert incluant un voyage à travers le Haut Atlas et l\'expérience de la vie bédouine',
                'detailed_description_en': 'Complete desert adventure including journey through High Atlas and Bedouin life experience',
                'category': desert_category,
                'duration_days': 4,
                'duration_nights': 3,
                'max_participants': 15,
                'min_participants': 2,
                'difficulty_level': 'moderate',
                'base_price': Decimal('1800.00'),
                'child_price': Decimal('1200.00'),
                'infant_price': Decimal('300.00'),
                'inclusions': 'النقل بسيارة 4x4، المبيت في المخيم، جميع الوجبات، ركوب الجمال',
                'exclusions': 'تذاكر الطيران، المشروبات الكحولية، الإكراميات',
                'is_active': True,
                'is_featured': True
            },
            {
                'package_code': 'FEZ001',
                'title_ar': 'كنوز فاس التاريخية',
                'title_fr': 'Trésors Historiques de Fès',
                'title_en': 'Historical Treasures of Fez',
                'description_ar': 'اكتشف التراث الإسلامي العريق في فاس مع زيارة جامعة القرويين والمدينة العتيقة والحرف التقليدية',
                'description_fr': 'Découvrez le riche patrimoine islamique de Fès avec la visite de l\'université Al Quaraouiyine, la médina et l\'artisanat traditionnel',
                'description_en': 'Discover the rich Islamic heritage of Fez with visits to Al Quaraouiyine University, the medina and traditional crafts',
                'category': cultural_category,
                'duration_days': 2,
                'duration_nights': 1,
                'max_participants': 25,
                'min_participants': 1,
                'difficulty_level': 'easy',
                'price_adult': Decimal('800.00'),
                'price_child': Decimal('500.00'),
                'price_infant': Decimal('100.00'),
                'includes_ar': 'الإقامة في رياض تقليدي، وجبة الإفطار، دليل محلي، النقل',
                'includes_fr': 'Hébergement en riad traditionnel, petit-déjeuner, guide local, transport',
                'includes_en': 'Traditional riad accommodation, breakfast, local guide, transport',
                'excludes_ar': 'الغداء والعشاء، المشتريات، رسوم دخول المتاحف',
                'excludes_fr': 'Déjeuner et dîner, achats, frais d\'entrée aux musées',
                'excludes_en': 'Lunch and dinner, purchases, museum entrance fees',
                'is_active': True,
                'is_featured': False
            },
            {
                'package_code': 'CHE001',
                'title_ar': 'سحر شفشاون الأزرق',
                'title_fr': 'Charme de Chefchaouen la Bleue',
                'title_en': 'Blue Chefchaouen Charm',
                'description_ar': 'استمتع بجمال المدينة الزرقاء في أحضان جبال الريف مع المشي في الأزقة الضيقة والاستمتاع بالطبيعة',
                'description_fr': 'Profitez de la beauté de la ville bleue dans les montagnes du Rif en vous promenant dans les ruelles étroites et en appréciant la nature',
                'description_en': 'Enjoy the beauty of the blue city in the Rif Mountains while walking through narrow alleys and appreciating nature',
                'category': leisure_category,
                'duration_days': 2,
                'duration_nights': 1,
                'max_participants': 12,
                'min_participants': 2,
                'difficulty_level': 'easy',
                'price_adult': Decimal('600.00'),
                'price_child': Decimal('400.00'),
                'price_infant': Decimal('50.00'),
                'includes_ar': 'الإقامة في فندق محلي، وجبة الإفطار، النقل، جولة مشي',
                'includes_fr': 'Hébergement en hôtel local, petit-déjeuner, transport, visite à pied',
                'includes_en': 'Local hotel accommodation, breakfast, transport, walking tour',
                'excludes_ar': 'الوجبات الأخرى، الأنشطة الاختيارية، التسوق',
                'excludes_fr': 'Autres repas, activités optionnelles, shopping',
                'excludes_en': 'Other meals, optional activities, shopping',
                'is_active': True,
                'is_featured': False
            },
            {
                'package_code': 'ADV001',
                'title_ar': 'مغامرة الأطلس الكبير',
                'title_fr': 'Aventure du Haut Atlas',
                'title_en': 'High Atlas Adventure',
                'description_ar': 'رحلة مغامرة في جبال الأطلس الكبير مع التسلق والمشي لمسافات طويلة وزيارة القرى البربرية',
                'description_fr': 'Voyage d\'aventure dans les montagnes du Haut Atlas avec escalade, randonnée et visite des villages berbères',
                'description_en': 'Adventure trip in the High Atlas mountains with climbing, hiking and visiting Berber villages',
                'category': adventure_category,
                'duration_days': 5,
                'duration_nights': 4,
                'max_participants': 10,
                'min_participants': 4,
                'difficulty_level': 'hard',
                'price_adult': Decimal('2200.00'),
                'price_child': Decimal('1500.00'),
                'price_infant': Decimal('0.00'),
                'includes_ar': 'الإقامة في النزل الجبلية، جميع الوجبات، المعدات، دليل جبلي',
                'includes_fr': 'Hébergement en gîtes de montagne, tous les repas, équipements, guide de montagne',
                'includes_en': 'Mountain lodge accommodation, all meals, equipment, mountain guide',
                'excludes_ar': 'المعدات الشخصية، التأمين، النقل من المطار',
                'excludes_fr': 'Équipement personnel, assurance, transport depuis l\'aéroport',
                'excludes_en': 'Personal equipment, insurance, airport transport',
                'is_active': True,
                'is_featured': True
            }
        ]

        for package_data in packages_data:
            package, created = TourPackage.objects.get_or_create(
                package_code=package_data['package_code'],
                defaults=package_data
            )
            if created:
                self.stdout.write(f'تم إنشاء باقة: {package.title_ar} ({package.package_code})')

                # Add destinations to package
                if package.package_code == 'MAR001':
                    package.destinations.add(marrakech)
                elif package.package_code == 'DES001':
                    package.destinations.add(merzouga)
                elif package.package_code == 'FEZ001':
                    package.destinations.add(fez)
                elif package.package_code == 'CHE001':
                    package.destinations.add(chefchaouen)
                elif package.package_code == 'ADV001':
                    package.destinations.add(marrakech, fez)

                # Create sample itinerary
                self.create_sample_itinerary(package)

    def create_sample_itinerary(self, package):
        """Create sample itinerary for a package."""
        itineraries_data = {
            'MAR001': [
                {
                    'day_number': 1,
                    'title_ar': 'الوصول إلى مراكش',
                    'title_fr': 'Arrivée à Marrakech',
                    'title_en': 'Arrival in Marrakech',
                    'description_ar': 'الوصول إلى المطار والانتقال إلى الفندق، جولة استطلاعية في ساحة جامع الفنا',
                    'description_fr': 'Arrivée à l\'aéroport et transfert à l\'hôtel, visite exploratoire de la place Jemaa el-Fna',
                    'description_en': 'Airport arrival and hotel transfer, exploratory tour of Jemaa el-Fna square',
                    'accommodation': 'فندق 4 نجوم في المدينة',
                    'meals': 'العشاء'
                },
                {
                    'day_number': 2,
                    'title_ar': 'استكشاف المدينة القديمة',
                    'title_fr': 'Exploration de la médina',
                    'title_en': 'Exploring the old city',
                    'description_ar': 'زيارة قصر البديع وحدائق المنارة والأسواق التقليدية',
                    'description_fr': 'Visite du Palais El Badi, des jardins de la Ménara et des souks traditionnels',
                    'description_en': 'Visit El Badi Palace, Menara gardens and traditional souks',
                    'accommodation': 'فندق 4 نجوم في المدينة',
                    'meals': 'الإفطار والغداء'
                },
                {
                    'day_number': 3,
                    'title_ar': 'المغادرة',
                    'title_fr': 'Départ',
                    'title_en': 'Departure',
                    'description_ar': 'وقت حر للتسوق والانتقال إلى المطار',
                    'description_fr': 'Temps libre pour le shopping et transfert à l\'aéroport',
                    'description_en': 'Free time for shopping and airport transfer',
                    'accommodation': '',
                    'meals': 'الإفطار'
                }
            ],
            'DES001': [
                {
                    'day_number': 1,
                    'title_ar': 'الانطلاق نحو الصحراء',
                    'title_fr': 'Départ vers le désert',
                    'title_en': 'Departure to the desert',
                    'description_ar': 'الانطلاق من مراكش عبر الأطلس الكبير إلى ورزازات',
                    'description_fr': 'Départ de Marrakech via le Haut Atlas vers Ouarzazate',
                    'description_en': 'Departure from Marrakech via High Atlas to Ouarzazate',
                    'accommodation': 'فندق في ورزازات',
                    'meals': 'الإفطار والعشاء'
                },
                {
                    'day_number': 2,
                    'title_ar': 'وصول إلى مرزوقة',
                    'title_fr': 'Arrivée à Merzouga',
                    'title_en': 'Arrival in Merzouga',
                    'description_ar': 'الوصول إلى مرزوقة وركوب الجمال إلى المخيم الصحراوي',
                    'description_fr': 'Arrivée à Merzouga et balade à dos de chameau vers le camp dans le désert',
                    'description_en': 'Arrival in Merzouga and camel ride to desert camp',
                    'accommodation': 'مخيم صحراوي',
                    'meals': 'جميع الوجبات'
                }
            ]
        }

        if package.package_code in itineraries_data:
            for itinerary_data in itineraries_data[package.package_code]:
                itinerary_data['package'] = package
                itinerary, created = Itinerary.objects.get_or_create(
                    package=package,
                    day_number=itinerary_data['day_number'],
                    defaults=itinerary_data
                )
                if created:
                    self.stdout.write(f'  تم إنشاء برنامج اليوم {itinerary.day_number}: {itinerary.title_ar}')
