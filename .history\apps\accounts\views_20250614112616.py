"""
Views for accounts app.
Contains all the view classes for managing user accounts, roles, permissions and sessions.
"""
from typing import Any, Dict, Optional
from django.db.models import QuerySet
from django.http import HttpRequest, HttpResponse
from django.shortcuts import redirect
from django.contrib.auth import logout
from django.contrib.auth.views import LoginView as DjangoLoginView
from django.contrib.auth.mixins import (
    LoginRequiredMixin,
    PermissionRequiredMixin,
)
from django.views.generic import (
    TemplateView,
    ListView,
    DetailView,
    CreateView,
    UpdateView,
)
from django.contrib import messages
from django.urls import reverse_lazy
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

User = get_user_model()


class LoginView(DjangoLoginView):
    """Handle user authentication and login."""
    template_name = 'accounts/login.html'
    redirect_authenticated_user = True

    def get_success_url(self) -> str:
        return reverse_lazy('core:dashboard')


class LogoutView(LoginRequiredMixin, TemplateView):
    """Handle user logout."""
    def get(
        self,
        request: HttpRequest,
        *args: Any,
        **kwargs: Any
    ) -> HttpResponse:
        logout(request)
        messages.success(request, _('تم تسجيل الخروج بنجاح'))
        return redirect('accounts:login')


class PasswordChangeView(LoginRequiredMixin, TemplateView):
    """Handle password change for authenticated users."""
    template_name = 'accounts/password_change.html'


class PasswordResetView(TemplateView):
    """Handle password reset request."""
    template_name = 'accounts/password_reset.html'


class ProfileView(LoginRequiredMixin, TemplateView):
    """Display user profile information."""
    template_name = 'accounts/profile.html'

    def get_context_data(self, **kwargs: Any) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)
        context['user'] = self.request.user
        return context


class ProfileEditView(LoginRequiredMixin, UpdateView):
    """Handle user profile editing."""
    model = User
    template_name = 'accounts/profile_edit.html'
    fields = ['email', 'first_name', 'last_name']
    success_url = reverse_lazy('accounts:profile')

    def get_object(self) -> User:
        """Get the user object to edit."""
        return self.request.user


class UserListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    """Display list of all users."""
    model = User
    template_name = 'accounts/user_list.html'
    context_object_name = 'users'
    permission_required = 'auth.view_user'


class UserDetailView(LoginRequiredMixin, PermissionRequiredMixin, DetailView):
    """Display detailed information about a specific user."""
    model = User
    template_name = 'accounts/user_detail.html'
    context_object_name = 'user'
    permission_required = 'auth.view_user'


class UserCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    """Handle user creation."""
    model = User
    template_name = 'accounts/user_form.html'
    fields = [
        'username',
        'email',
        'first_name',
        'last_name',
        'role',
        'is_active'
    ]
    success_url = reverse_lazy('accounts:user_list')
    permission_required = 'auth.add_user'

    def form_valid(self, form):
        messages.success(self.request, _('تم إنشاء المستخدم بنجاح'))
        return super().form_valid(form)


class UserUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    """Handle user information updates."""
    model = User
    template_name = 'accounts/user_form.html'
    fields = ['username', 'email', 'first_name', 'last_name', 'role', 'is_active']
    success_url = reverse_lazy('accounts:user_list')
    permission_required = 'auth.change_user'

    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث المستخدم بنجاح'))
        return super().form_valid(form)


class UserDeactivateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    """Handle user deactivation."""
    model = User
    template_name = 'accounts/user_deactivate.html'
    fields = ['is_active']
    success_url = reverse_lazy('accounts:user_list')
    permission_required = 'auth.change_user'

    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث حالة المستخدم بنجاح'))
        return super().form_valid(form)


class RoleListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    """Display list of all roles."""
    template_name = 'accounts/role_list.html'
    permission_required = 'auth.view_group'
    model = User.groups.field.model
    context_object_name = 'roles'


class RoleCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    """Handle role creation."""
    template_name = 'accounts/role_form.html'
    permission_required = 'auth.add_group'
    model = User.groups.field.model
    fields = ['name', 'permissions']
    success_url = reverse_lazy('accounts:role_list')


class RoleUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    """Handle role updates."""
    template_name = 'accounts/role_form.html'
    permission_required = 'auth.change_group'
    model = User.groups.field.model
    fields = ['name', 'permissions']
    success_url = reverse_lazy('accounts:role_list')


class PermissionListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    """Display list of all permissions."""
    template_name = 'accounts/permission_list.html'
    permission_required = 'auth.view_permission'
    model = User.user_permissions.field.model
    context_object_name = 'permissions'


class PermissionCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    """Handle permission creation."""
    template_name = 'accounts/permission_form.html'
    permission_required = 'auth.add_permission'
    model = User.user_permissions.field.model
    fields = ['name', 'codename']
    success_url = reverse_lazy('accounts:permission_list')


class SessionListView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """Display list of active sessions."""
    template_name = 'accounts/session_list.html'
    permission_required = 'auth.view_session'


class SessionTerminateView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """Handle session termination."""
    template_name = 'accounts/session_terminate.html'
    permission_required = 'auth.delete_session'

    def post(self, request: HttpRequest, *args: Any, **kwargs: Any) -> HttpResponse:
        # Add session termination logic here
        messages.success(request, _('تم إنهاء الجلسة بنجاح'))
        return redirect('accounts:session_list')



