{% extends 'base.html' %}
{% load static %}

{% block title %}المدفوعات{% endblock %}

{% block extra_css %}
<style>
    .payment-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: transform 0.3s ease;
    }
    
    .payment-card:hover {
        transform: translateY(-2px);
    }
    
    .payment-status {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: bold;
    }
    
    .status-completed {
        background: #d4edda;
        color: #155724;
    }
    
    .status-pending {
        background: #fff3cd;
        color: #856404;
    }
    
    .status-failed {
        background: #f8d7da;
        color: #721c24;
    }
    
    .status-refunded {
        background: #d1ecf1;
        color: #0c5460;
    }
    
    .payment-amount {
        font-size: 1.5rem;
        font-weight: bold;
        color: #28a745;
    }
    
    .payment-method-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 15px;
    }
    
    .method-cash { background: #28a745; color: white; }
    .method-card { background: #007bff; color: white; }
    .method-bank { background: #6f42c1; color: white; }
    .method-online { background: #fd7e14; color: white; }
    
    .stats-row {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
    }
    
    .search-form {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-credit-card text-primary me-2"></i>
                        المدفوعات
                    </h1>
                    <p class="text-muted mb-0">إدارة وعرض جميع المدفوعات</p>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="alert('ميزة إضافة الدفعات قيد التطوير')">
                        <i class="fas fa-plus me-2"></i>
                        إضافة دفعة جديدة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-row">
        <div class="row text-center">
            <div class="col-md-3">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="me-3">
                        <i class="fas fa-money-bill-wave fa-2x text-primary"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">{{ payments.count }}</h4>
                        <small class="text-muted">إجمالي المدفوعات</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="me-3">
                        <i class="fas fa-check-circle fa-2x text-success"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">0</h4>
                        <small class="text-muted">مكتملة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="me-3">
                        <i class="fas fa-clock fa-2x text-warning"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">0</h4>
                        <small class="text-muted">معلقة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="me-3">
                        <i class="fas fa-chart-line fa-2x text-info"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">0 درهم</h4>
                        <small class="text-muted">إجمالي المبلغ</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Form -->
    <div class="search-form">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       placeholder="ابحث في المدفوعات..." value="{{ request.GET.search }}">
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="completed">مكتملة</option>
                    <option value="pending">معلقة</option>
                    <option value="failed">فاشلة</option>
                    <option value="refunded">مستردة</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="method" class="form-label">طريقة الدفع</label>
                <select class="form-select" id="method" name="method">
                    <option value="">جميع الطرق</option>
                    <option value="cash">نقدي</option>
                    <option value="card">بطاقة</option>
                    <option value="bank_transfer">تحويل بنكي</option>
                    <option value="online">دفع إلكتروني</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="date_from" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="date_from" name="date_from" 
                       value="{{ request.GET.date_from }}">
            </div>
            <div class="col-md-2">
                <label for="date_to" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="date_to" name="date_to" 
                       value="{{ request.GET.date_to }}">
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Payments List -->
    <div class="row">
        {% for payment in payments %}
        <div class="col-lg-6 mb-4">
            <div class="payment-card">
                <div class="d-flex align-items-center mb-3">
                    <div class="payment-method-icon method-{{ payment.payment_method }}">
                        {% if payment.payment_method == 'cash' %}
                            <i class="fas fa-money-bill"></i>
                        {% elif payment.payment_method == 'card' %}
                            <i class="fas fa-credit-card"></i>
                        {% elif payment.payment_method == 'bank_transfer' %}
                            <i class="fas fa-university"></i>
                        {% else %}
                            <i class="fas fa-globe"></i>
                        {% endif %}
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1">دفعة رقم #{{ payment.payment_number }}</h6>
                        <small class="text-muted">{{ payment.payment_date|date:"d/m/Y H:i" }}</small>
                    </div>
                    <div>
                        <span class="payment-status status-{{ payment.status }}">
                            {% if payment.status == 'completed' %}مكتملة
                            {% elif payment.status == 'pending' %}معلقة
                            {% elif payment.status == 'failed' %}فاشلة
                            {% elif payment.status == 'refunded' %}مستردة
                            {% else %}{{ payment.get_status_display }}
                            {% endif %}
                        </span>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">المبلغ</small>
                        <div class="payment-amount">{{ payment.amount }} درهم</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">الفاتورة</small>
                        <div>
                            {% if payment.invoice %}
                                <a href="{% url 'finance:invoice_detail' payment.invoice.pk %}" 
                                   class="text-decoration-none">
                                    #{{ payment.invoice.invoice_number }}
                                </a>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                {% if payment.description %}
                <div class="mb-3">
                    <small class="text-muted">الوصف</small>
                    <p class="mb-0">{{ payment.description|truncatewords:15 }}</p>
                </div>
                {% endif %}
                
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small class="text-muted">طريقة الدفع: {{ payment.get_payment_method_display }}</small>
                    </div>
                    <div class="btn-group" role="group">
                        <a href="{% url 'finance:payment_detail' payment.pk %}" 
                           class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{% url 'finance:payment_edit' payment.pk %}" 
                           class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button class="btn btn-outline-success btn-sm" onclick="printReceipt({{ payment.pk }})">
                            <i class="fas fa-print"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-credit-card fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد مدفوعات</h4>
                <p class="text-muted">ابدأ بإضافة دفعة جديدة</p>
                <a href="{% url 'finance:payment_add' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة دفعة جديدة
                </a>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="row">
        <div class="col-12">
            <nav aria-label="صفحات المدفوعات">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1">الأولى</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                        </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                    </li>
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function printReceipt(paymentId) {
    window.open('/finance/payments/' + paymentId + '/receipt/', '_blank');
}

$(document).ready(function() {
    console.log('Payments list loaded');
});
</script>
{% endblock %}
