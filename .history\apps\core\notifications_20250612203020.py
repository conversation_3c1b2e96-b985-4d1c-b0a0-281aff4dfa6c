"""
Notification system for the Moroccan Travel Agency ERP.
"""
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import timezone
from datetime import datetime, timedelta

User = get_user_model()


class NotificationManager:
    """Manage system notifications."""

    @staticmethod
    def send_welcome_email(user):
        """Send welcome email to new user."""
        subject = "مرحباً بك في نظام إدارة وكالة السفر المغربية"
        message = f"""
        مرحباً {user.full_name_ar or user.username}،

        تم إنشاء حسابك بنجاح في نظام إدارة وكالة السفر المغربية.

        بيانات الدخول:
        اسم المستخدم: {user.username}
        الدور: {user.get_role_display()}

        يمكنك الآن تسجيل الدخول والبدء في استخدام النظام.

        رابط النظام: http://127.0.0.1:8000/admin/

        مع تحيات فريق العمل
        """

        try:
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
                fail_silently=False,
            )
            return True
        except Exception as e:
            print(f"Failed to send welcome email: {e}")
            return False

    @staticmethod
    def send_client_registration_notification(client):
        """Send notification when new client registers."""
        # Notify sales team
        sales_users = User.objects.filter(role='sales', is_active=True)

        subject = f"عميل جديد: {client.full_name_ar}"
        message = f"""
        تم تسجيل عميل جديد في النظام:

        الاسم: {client.full_name_ar}
        النوع: {client.get_client_type_display()}
        البريد الإلكتروني: {client.email}
        الهاتف: {client.phone}
        الجنسية: {client.nationality.name_ar if client.nationality else 'غير محدد'}

        يرجى المتابعة مع العميل في أقرب وقت ممكن.

        رابط ملف العميل: http://127.0.0.1:8000/admin/crm/client/{client.id}/change/
        """

        for user in sales_users:
            try:
                send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[user.email],
                    fail_silently=True,
                )
            except Exception as e:
                print(f"Failed to send client notification to {user.email}: {e}")

    @staticmethod
    def send_package_inquiry_notification(client, package):
        """Send notification when client inquires about package."""
        # Notify assigned agent or sales team
        if hasattr(client, 'assigned_agent') and client.assigned_agent:
            recipients = [client.assigned_agent]
        else:
            recipients = User.objects.filter(role='sales', is_active=True)

        subject = f"استفسار عن باقة: {package.title_ar}"
        message = f"""
        استفسار جديد عن باقة سياحية:

        العميل: {client.full_name_ar}
        البريد الإلكتروني: {client.email}
        الهاتف: {client.phone}

        الباقة: {package.title_ar}
        المدة: {package.duration_days} أيام / {package.duration_nights} ليالي
        السعر: {package.base_price} درهم

        يرجى التواصل مع العميل لتقديم المزيد من التفاصيل.

        رابط الباقة: http://127.0.0.1:8000/admin/tours/tourpackage/{package.id}/change/
        رابط العميل: http://127.0.0.1:8000/admin/crm/client/{client.id}/change/
        """

        for user in recipients:
            try:
                send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[user.email],
                    fail_silently=True,
                )
            except Exception as e:
                print(f"Failed to send package inquiry notification to {user.email}: {e}")

    @staticmethod
    def send_daily_summary():
        """Send daily summary to managers."""
        from apps.crm.models import Client
        from apps.tours.models import TourPackage

        today = timezone.now().date()
        yesterday = today - timedelta(days=1)

        # Get statistics
        new_clients_today = Client.objects.filter(created_at__date=today).count()
        new_clients_yesterday = Client.objects.filter(created_at__date=yesterday).count()

        total_clients = Client.objects.count()
        vip_clients = Client.objects.filter(vip_status=True).count()

        active_packages = TourPackage.objects.filter(is_active=True).count()

        # Notify managers
        managers = User.objects.filter(role__in=['admin', 'manager'], is_active=True)

        subject = f"ملخص يومي - {today.strftime('%d/%m/%Y')}"
        message = f"""
        ملخص يومي لنظام إدارة وكالة السفر المغربية
        التاريخ: {today.strftime('%d/%m/%Y')}

        📊 إحصائيات العملاء:
        - عملاء جدد اليوم: {new_clients_today}
        - عملاء جدد أمس: {new_clients_yesterday}
        - إجمالي العملاء: {total_clients}
        - عملاء VIP: {vip_clients}

        🏖️ إحصائيات الباقات:
        - باقات نشطة: {active_packages}

        📈 مقارنة مع الأمس:
        العملاء الجدد: {"↗️ زيادة" if new_clients_today > new_clients_yesterday else "↘️ نقصان" if new_clients_today < new_clients_yesterday else "→ نفس العدد"}

        رابط النظام: http://127.0.0.1:8000/
        رابط التقارير: http://127.0.0.1:8000/reports/

        تم إنشاء هذا التقرير تلقائياً
        """

        for manager in managers:
            try:
                send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[manager.email],
                    fail_silently=True,
                )
            except Exception as e:
                print(f"Failed to send daily summary to {manager.email}: {e}")

    @staticmethod
    def send_system_alert(message, alert_type='info', recipients=None):
        """Send system alert to specified users or all admins."""
        if recipients is None:
            recipients = User.objects.filter(role='admin', is_active=True)

        subject = f"تنبيه النظام - {alert_type.upper()}"

        for user in recipients:
            try:
                send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[user.email],
                    fail_silently=True,
                )
            except Exception as e:
                print(f"Failed to send system alert to {user.email}: {e}")


# Convenience functions
def notify_new_client(client):
    """Shortcut to notify about new client."""
    NotificationManager.send_client_registration_notification(client)


def notify_package_inquiry(client, package):
    """Shortcut to notify about package inquiry."""
    NotificationManager.send_package_inquiry_notification(client, package)


def send_welcome_email(user):
    """Shortcut to send welcome email."""
    return NotificationManager.send_welcome_email(user)
