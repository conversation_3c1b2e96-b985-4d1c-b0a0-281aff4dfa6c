// CRM Module Functions

// Initialize Notifications
const notify = {
    success: (message) => {
        toastr.success(message, '', {
            closeButton: true,
            progressBar: true,
            rtl: true
        });
    },
    error: (message) => {
        toastr.error(message, '', {
            closeButton: true,
            progressBar: true,
            rtl: true,
            timeOut: 0,
            extendedTimeOut: 0
        });
    },
    info: (message) => {
        toastr.info(message, '', {
            closeButton: true,
            progressBar: true,
            rtl: true
        });
    }
};

// Client Input Validation
const validateClient = (client) => {
    const errors = [];

    if (!client.full_name_ar?.trim()) {
        errors.push('الاسم بالعربية مطلوب');
    }

    if (!client.phone?.trim()) {
        errors.push('رقم الهاتف مطلوب');
    } else if (!/^(\+212|0)[567]\d{8}$/.test(client.phone)) {
        errors.push('رقم الهاتف غير صالح');
    }

    if (client.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(client.email)) {
        errors.push('البريد الإلكتروني غير صالح');
    }

    if (client.passport_number && !/^[A-Z0-9]{6,12}$/.test(client.passport_number)) {
        errors.push('رقم جواز السفر غير صالح');
    }

    return errors;
};

// Contact Validation
const validateContact = (contact) => {
    const errors = [];

    if (!contact.first_name_ar?.trim()) {
        errors.push('الاسم الأول بالعربية مطلوب');
    }

    if (!contact.last_name_ar?.trim()) {
        errors.push('اسم العائلة بالعربية مطلوب');
    }

    if (!contact.phone?.trim()) {
        errors.push('رقم الهاتف مطلوب');
    } else if (!/^(\+212|0)[567]\d{8}$/.test(contact.phone)) {
        errors.push('رقم الهاتف غير صالح');
    }

    if (!contact.email?.trim()) {
        errors.push('البريد الإلكتروني مطلوب');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contact.email)) {
        errors.push('البريد الإلكتروني غير صالح');
    }

    if (!contact.position?.trim()) {
        errors.push('المنصب مطلوب');
    }

    return errors;
};

// Interaction Validation
const validateInteraction = (interaction) => {
    const errors = [];

    if (!interaction.type) {
        errors.push('نوع التفاعل مطلوب');
    }

    if (!interaction.subject?.trim()) {
        errors.push('موضوع التفاعل مطلوب');
    }

    if (!interaction.description?.trim()) {
        errors.push('وصف التفاعل مطلوب');
    }

    if (!interaction.date) {
        errors.push('تاريخ التفاعل مطلوب');
    }

    return errors;
};

// Format Phone Numbers
const formatPhoneNumber = (phone) => {
    if (!phone) return '';

    // Format Moroccan phone numbers
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
        return cleaned.replace(/(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4 $5');
    }
    return phone;
};

// Format Dates for Interactions
const formatInteractionDate = (date) => {
    if (!date) return '';
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    };
    return new Date(date).toLocaleString('ar-MA', options);
};

// Cache Management
const cacheKey = 'crm_clients_cache';
const cacheExpiry = 5 * 60 * 1000; // 5 minutes

const getCachedClients = () => {
    const cached = localStorage.getItem(cacheKey);
    if (cached) {
        const { timestamp, data } = JSON.parse(cached);
        if (Date.now() - timestamp < cacheExpiry) {
            return data;
        }
    }
    return null;
};

const setCachedClients = (clients) => {
    localStorage.setItem(cacheKey, JSON.stringify({
        timestamp: Date.now(),
        data: clients
    }));
};

// Offline Support
let offlineQueue = [];

const processOfflineQueue = async () => {
    if (navigator.onLine && offlineQueue.length > 0) {
        notify.info(`جاري معالجة ${offlineQueue.length} عملية معلقة...`);

        for (const task of offlineQueue) {
            try {
                await task();
            } catch (error) {
                console.error('Error processing offline task:', error);
            }
        }

        offlineQueue = [];
        notify.success('تم معالجة جميع العمليات المعلقة');
    }
};

window.addEventListener('online', processOfflineQueue);

// Get Interaction Status Badge Class
const getStatusBadgeClass = (status) => {
    const classes = {
        'open': 'badge-info',
        'in_progress': 'badge-primary',
        'pending': 'badge-warning',
        'resolved': 'badge-success',
        'closed': 'badge-secondary'
    };
    return classes[status] || 'badge-light';
};

// Get Priority Badge Class
const getPriorityBadgeClass = (priority) => {
    const classes = {
        'low': 'badge-info',
        'medium': 'badge-warning',
        'high': 'badge-danger',
        'urgent': 'badge-danger blink'
    };
    return classes[priority] || 'badge-light';
};

// Get Interaction Type Icon
const getInteractionTypeIcon = (type) => {
    const icons = {
        'call': 'fas fa-phone',
        'email': 'fas fa-envelope',
        'meeting': 'fas fa-users',
        'whatsapp': 'fab fa-whatsapp',
        'visit': 'fas fa-building',
        'other': 'fas fa-comment'
    };
    return icons[type] || 'fas fa-comment';
};

// Calculate Follow-up Status
const getFollowupStatus = (interaction) => {
    if (!interaction.requires_followup) {
        return { status: 'none', className: 'text-muted' };
    }

    if (!interaction.followup_date) {
        return { status: 'pending', className: 'text-warning' };
    }

    const followupDate = new Date(interaction.followup_date);
    const now = new Date();

    if (interaction.completed_at) {
        return { status: 'completed', className: 'text-success' };
    }

    if (followupDate < now) {
        return { status: 'overdue', className: 'text-danger' };
    }

    // Check if follow-up is due within 24 hours
    const hours24 = 24 * 60 * 60 * 1000;
    if ((followupDate - now) <= hours24) {
        return { status: 'due-soon', className: 'text-warning' };
    }

    return { status: 'scheduled', className: 'text-info' };
};

// Handle offline interactions
const queueInteraction = (interaction) => {
    if (!navigator.onLine) {
        offlineQueue.push(async () => {
            await axios.post('/api/crm/interactions/', interaction);
        });

        // Store in IndexedDB for persistence
        const dbRequest = indexedDB.open('crmOfflineDB', 1);

        dbRequest.onerror = (event) => {
            console.error('IndexedDB error:', event.target.error);
        };

        dbRequest.onupgradeneeded = (event) => {
            const db = event.target.result;
            if (!db.objectStoreNames.contains('interactions')) {
                db.createObjectStore('interactions', { keyPath: 'id', autoIncrement: true });
            }
        };

        dbRequest.onsuccess = (event) => {
            const db = event.target.result;
            const transaction = db.transaction(['interactions'], 'readwrite');
            const store = transaction.objectStore('interactions');
            store.add({
                ...interaction,
                status: 'pending',
                created_at: new Date().toISOString()
            });
        };

        notify.info('تم حفظ التفاعل للمزامنة لاحقاً');
        return true;
    }
    return false;
};

// Export Client Data
const exportClientData = async (format) => {
    try {
        const response = await axios.get(`/api/clients/export/?format=${format}`, {
            responseType: 'blob'
        });

        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `clients_${new Date().toISOString().split('T')[0]}.${format}`);
        document.body.appendChild(link);
        link.click();
        link.remove();
    } catch (error) {
        notify.error('حدث خطأ أثناء تصدير البيانات');
        console.error('Export error:', error);
    }
};

// Analytics and Logging Functions
const logInteractionActivity = (interaction, activityType) => {
    const activity = {
        type: activityType,
        interaction_id: interaction.id,
        client_id: interaction.client_id,
        timestamp: new Date().toISOString(),
        details: interaction
    };

    // Store in IndexedDB for analytics
    const dbRequest = indexedDB.open('crmAnalyticsDB', 1);

    dbRequest.onerror = (event) => {
        console.error('Analytics DB error:', event.target.error);
    };

    dbRequest.onupgradeneeded = (event) => {
        const db = event.target.result;
        if (!db.objectStoreNames.contains('activities')) {
            const store = db.createObjectStore('activities', { keyPath: 'id', autoIncrement: true });
            store.createIndex('timestamp', 'timestamp');
            store.createIndex('type', 'type');
            store.createIndex('client_id', 'client_id');
        }
    };

    dbRequest.onsuccess = (event) => {
        const db = event.target.result;
        const transaction = db.transaction(['activities'], 'readwrite');
        const store = transaction.objectStore('activities');
        store.add(activity);
    };
};

const analyzeInteractionPatterns = async (clientId = null) => {
    return new Promise((resolve, reject) => {
        const dbRequest = indexedDB.open('crmAnalyticsDB', 1);

        dbRequest.onerror = () => reject(new Error('Could not access analytics database'));

        dbRequest.onsuccess = (event) => {
            const db = event.target.result;
            const transaction = db.transaction(['activities'], 'readonly');
            const store = transaction.objectStore('activities');

            let query = clientId ?
                store.index('client_id').getAll(clientId) :
                store.index('timestamp').getAll();

            query.onsuccess = (event) => {
                const activities = event.target.result;

                // Analyze patterns
                const analysis = {
                    totalInteractions: activities.length,
                    byType: {},
                    byHour: Array(24).fill(0),
                    byDay: Array(7).fill(0),
                    averageResponseTime: 0,
                    completionRate: 0
                };

                let totalResponseTime = 0;
                let completedInteractions = 0;

                activities.forEach(activity => {
                    // Count by type
                    analysis.byType[activity.type] = (analysis.byType[activity.type] || 0) + 1;

                    // Count by hour and day
                    const date = new Date(activity.timestamp);
                    analysis.byHour[date.getHours()]++;
                    analysis.byDay[date.getDay()]++;

                    // Calculate response and completion metrics
                    if (activity.details.completed_at) {
                        completedInteractions++;
                        totalResponseTime += new Date(activity.details.completed_at) - new Date(activity.details.created_at);
                    }
                });

                // Calculate averages
                analysis.completionRate = (completedInteractions / activities.length) * 100;
                analysis.averageResponseTime = totalResponseTime / completedInteractions;

                resolve(analysis);
            };

            query.onerror = () => reject(new Error('Error querying activities'));
        };
    });
};

const generateAutomaticReport = async (startDate, endDate) => {
    try {
        // Get interaction patterns
        const patterns = await analyzeInteractionPatterns();

        // Calculate key metrics
        const report = {
            period: {
                start: startDate,
                end: endDate
            },
            metrics: {
                totalInteractions: patterns.totalInteractions,
                completionRate: patterns.completionRate.toFixed(2) + '%',
                averageResponseTime: (patterns.averageResponseTime / (1000 * 60 * 60)).toFixed(2) + ' hours',
                mostActiveHour: patterns.byHour.indexOf(Math.max(...patterns.byHour)),
                mostActiveDay: patterns.byDay.indexOf(Math.max(...patterns.byDay)),
                interactionsByType: patterns.byType
            },
            recommendations: []
        };

        // Generate recommendations
        if (patterns.completionRate < 80) {
            report.recommendations.push('نسبة إكمال التفاعلات منخفضة. يُقترح مراجعة عملية المتابعة.');
        }

        if (patterns.averageResponseTime > 24 * 60 * 60 * 1000) { // More than 24 hours
            report.recommendations.push('متوسط وقت الاستجابة مرتفع. يُقترح تحسين سرعة الاستجابة للعملاء.');
        }

        return report;
    } catch (error) {
        console.error('Error generating report:', error);
        throw error;
    }
};

const analyzeClientBehavior = async (clientId) => {
    const activities = await getClientActivities(clientId);

    const analysis = {
        interactionFrequency: calculateInteractionFrequency(activities),
        peakActivityTimes: findPeakActivityTimes(activities),
        preferredChannels: analyzePreferredChannels(activities),
        responsePatterns: analyzeResponsePatterns(activities),
        customerValue: calculateCustomerValue(activities),
        recommendations: []
    };

    // Generate insights
    if (analysis.interactionFrequency.trend === 'decreasing') {
        analysis.recommendations.push('تناقص في معدل التفاعل. اقتراح: زيادة التواصل مع العميل');
    }

    if (analysis.preferredChannels.primary) {
        analysis.recommendations.push(
            `القناة المفضلة: ${analysis.preferredChannels.primary}. ` +
            'اقتراح: تركيز التواصل على هذه القناة'
        );
    }

    if (analysis.responsePatterns.avgResponseTime > 24) {
        analysis.recommendations.push('متوسط وقت الاستجابة مرتفع. اقتراح: تحسين سرعة الاستجابة');
    }

    return analysis;
};

const calculateInteractionFrequency = (activities) => {
    const now = new Date();
    const sixMonthsAgo = new Date(now.setMonth(now.getMonth() - 6));

    const recentActivities = activities.filter(a =>
        new Date(a.timestamp) >= sixMonthsAgo
    );

    const monthlyCount = {};
    recentActivities.forEach(activity => {
        const month = new Date(activity.timestamp).getMonth();
        monthlyCount[month] = (monthlyCount[month] || 0) + 1;
    });

    const trend = calculateTrend(Object.values(monthlyCount));

    return {
        monthly: monthlyCount,
        average: recentActivities.length / 6,
        trend
    };
};

const findPeakActivityTimes = (activities) => {
    const hourlyCount = Array(24).fill(0);
    const dailyCount = Array(7).fill(0);
    const monthlyCount = Array(12).fill(0);

    activities.forEach(activity => {
        const date = new Date(activity.timestamp);
        hourlyCount[date.getHours()]++;
        dailyCount[date.getDay()]++;
        monthlyCount[date.getMonth()]++;
    });

    return {
        peakHour: hourlyCount.indexOf(Math.max(...hourlyCount)),
        peakDay: dailyCount.indexOf(Math.max(...dailyCount)),
        peakMonth: monthlyCount.indexOf(Math.max(...monthlyCount)),
        distribution: {
            hourly: hourlyCount,
            daily: dailyCount,
            monthly: monthlyCount
        }
    };
};

const analyzePreferredChannels = (activities) => {
    const channels = activities.reduce((acc, curr) => {
        const channel = curr.details.type;
        acc[channel] = (acc[channel] || 0) + 1;
        return acc;
    }, {});

    const sorted = Object.entries(channels)
        .sort(([,a], [,b]) => b - a);

    return {
        primary: sorted[0]?.[0],
        secondary: sorted[1]?.[0],
        distribution: channels
    };
};

const analyzeResponsePatterns = (activities) => {
    const responseTimes = activities
        .filter(a => a.details.completed_at)
        .map(a => {
            const start = new Date(a.details.created_at);
            const end = new Date(a.details.completed_at);
            return (end - start) / (1000 * 60 * 60); // Convert to hours
        });

    return {
        avgResponseTime: responseTimes.length ?
            responseTimes.reduce((a, b) => a + b) / responseTimes.length : 0,
        minResponseTime: Math.min(...responseTimes) || 0,
        maxResponseTime: Math.max(...responseTimes) || 0,
        responseRate: activities.length ?
            (responseTimes.length / activities.length) * 100 : 0
    };
};

const calculateCustomerValue = (activities) => {
    const now = new Date();
    const recencyScore = calculateRecencyScore(activities, now);
    const frequencyScore = calculateFrequencyScore(activities);
    const engagementScore = calculateEngagementScore(activities);

    return {
        recencyScore,
        frequencyScore,
        engagementScore,
        totalScore: (recencyScore + frequencyScore + engagementScore) / 3,
        category: determineCustomerCategory(recencyScore, frequencyScore, engagementScore)
    };
};

const calculateRecencyScore = (activities, now) => {
    if (!activities.length) return 0;

    const lastActivity = new Date(
        Math.max(...activities.map(a => new Date(a.timestamp)))
    );
    const daysSinceLastActivity = (now - lastActivity) / (1000 * 60 * 60 * 24);

    if (daysSinceLastActivity <= 7) return 100;
    if (daysSinceLastActivity <= 30) return 75;
    if (daysSinceLastActivity <= 90) return 50;
    if (daysSinceLastActivity <= 180) return 25;
    return 0;
};

const calculateFrequencyScore = (activities) => {
    const sixMonthsActivities = activities.filter(a => {
        const date = new Date(a.timestamp);
        const sixMonthsAgo = new Date();
        sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
        return date >= sixMonthsAgo;
    });

    const monthlyAverage = sixMonthsActivities.length / 6;

    if (monthlyAverage >= 10) return 100;
    if (monthlyAverage >= 5) return 75;
    if (monthlyAverage >= 2) return 50;
    if (monthlyAverage >= 1) return 25;
    return 0;
};

const calculateEngagementScore = (activities) => {
    if (!activities.length) return 0;

    const completedActivities = activities.filter(a =>
        a.details.completed_at
    );

    const completionRate = (completedActivities.length / activities.length) * 100;
    const hasHighPriority = activities.some(a =>
        a.details.priority === 'high' || a.details.priority === 'urgent'
    );
    const hasFollowups = activities.some(a =>
        a.details.requires_followup
    );

    let score = 0;
    if (completionRate >= 80) score += 50;
    else if (completionRate >= 50) score += 25;

    if (hasHighPriority) score += 25;
    if (hasFollowups) score += 25;

    return score;
};

const determineCustomerCategory = (recency, frequency, engagement) => {
    const totalScore = (recency + frequency + engagement) / 3;

    if (totalScore >= 80) return 'VIP';
    if (totalScore >= 60) return 'Active';
    if (totalScore >= 40) return 'Regular';
    if (totalScore >= 20) return 'Inactive';
    return 'At Risk';
};

const calculateTrend = (values) => {
    if (values.length < 2) return 'stable';

    const changes = [];
    for (let i = 1; i < values.length; i++) {
        changes.push(values[i] - values[i-1]);
    }

    const averageChange = changes.reduce((a, b) => a + b) / changes.length;

    if (averageChange > 0.5) return 'increasing';
    if (averageChange < -0.5) return 'decreasing';
    return 'stable';
};

// Export functions
window.crmUtils = {
    notify,
    validateClient,
    exportClientData,
    formatPhoneNumber,
    getCachedClients,
    setCachedClients,
    validateContact,
    validateInteraction,
    formatInteractionDate,
    getStatusBadgeClass,
    getPriorityBadgeClass,
    getInteractionTypeIcon,
    getFollowupStatus,
    queueInteraction,
    logInteractionActivity,
    analyzeInteractionPatterns,
    generateAutomaticReport,
    analyzeClientBehavior,
    analyzePreferredChannels,
    calculateCustomerValue,
    calculateTrend
};
