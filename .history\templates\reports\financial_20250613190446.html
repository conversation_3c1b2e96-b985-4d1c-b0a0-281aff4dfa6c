{% extends 'base.html' %}
{% load static %}

{% block content %}
<!-- إضافة قسم الفلترة -->
<div class="container-fluid mb-4">
    <div class="card">
        <div class="card-body">
            <form id="filterForm" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">الفترة الزمنية</label>
                    <select class="form-select" name="time_period">
                        <option value="this_month">الشهر الحالي</option>
                        <option value="last_month">الشهر الماضي</option>
                        <option value="this_year">السنة الحالية</option>
                        <option value="custom">فترة مخصصة</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">نوع الباقة</label>
                    <select class="form-select" name="package_type">
                        <option value="all">الكل</option>
                        {% for category in tour_categories %}
                            <option value="{{ category.id }}">{{ category.name_ar }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">طريقة الدفع</label>
                    <select class="form-select" name="payment_method">
                        <option value="all">الكل</option>
                        <option value="cash">نقداً</option>
                        <option value="card">بطاقة بنكية</option>
                        <option value="transfer">تحويل بنكي</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">تطبيق الفلتر</button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <!-- ملخص مالي -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">الملخص المالي للعام {{ current_year }}</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="metric">
                                <h6>إجمالي الإيرادات</h6>
                                <h3 data-summary="revenue">{{ yearly_summary.total_revenue|floatformat:2 }} درهم</h3>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metric">
                                <h6>عدد الحجوزات</h6>
                                <h3 data-summary="bookings">{{ yearly_summary.total_bookings }}</h3>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metric">
                                <h6>متوسط قيمة الحجز</h6>
                                <h3 data-summary="average">{{ yearly_summary.average_booking_value|floatformat:2 }} درهم</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الرسم البياني للإيرادات الشهرية -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">الإيرادات الشهرية</h5>
                    <canvas id="monthlyRevenueChart"></canvas>
                </div>
            </div>
        </div>

        <!-- تحليل طرق الدفع -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">طرق الدفع</h5>
                    <canvas id="paymentMethodsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار التصدير -->
    <div class="card mt-3">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">خيارات التصدير</h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary" onclick="exportReport('pdf')">
                        <i class="fas fa-file-pdf"></i> PDF
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="exportReport('excel')">
                        <i class="fas fa-file-excel"></i> Excel
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="exportReport('csv')">
                        <i class="fas fa-file-csv"></i> CSV
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- مؤشرات أداء إضافية -->
    <div class="row mt-3">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h6>معدل النمو الشهري</h6>
                    <h3 data-summary="growth">{{ monthly_growth|floatformat:1 }}%</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h6>نسبة الإشغال</h6>
                    <h3 data-summary="occupancy">{{ occupancy_rate|floatformat:1 }}%</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h6>معدل التحصيل</h6>
                    <h3 data-summary="collection">{{ collection_rate|floatformat:1 }}%</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h6>متوسط مدة الرحلة</h6>
                    <h3 data-summary="duration">{{ avg_trip_duration }} يوم</h3>
                </div>
            </div>
        </div>
    </div>

    <div class="card mt-4">
    <div class="card-body">
        <h5 class="card-title mb-3">تفاصيل المعاملات</h5>
        <div class="table-responsive">
            <table class="table table-hover" id="transactionsTable">
                <thead>
                    <tr>
                        <th>رقم الحجز</th>
                        <th>العميل</th>
                        <th>الباقة</th>
                        <th>تاريخ الحجز</th>
                        <th>المبلغ</th>
                        <th>طريقة الدفع</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for transaction in transactions %}
                    <tr>
                        <td>{{ transaction.reservation_number }}</td>
                        <td>{{ transaction.client_name }}</td>
                        <td>{{ transaction.package_name }}</td>
                        <td>{{ transaction.booking_date|date:"Y-m-d" }}</td>
                        <td>{{ transaction.amount|floatformat:2 }} درهم</td>
                        <td>{{ transaction.payment_method }}</td>
                        <td>
                            <span class="badge bg-{{ transaction.status_color }}">
                                {{ transaction.status }}
                            </span>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- إضافة بعد قسم الرسوم البيانية الحالية -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">التحليل التنبؤي للمبيعات</h5>
                <div class="d-flex justify-content-between mb-3">
                    <div class="btn-group">
                        <button class="btn btn-outline-primary active" data-forecast-period="3">3 أشهر</button>
                        <button class="btn btn-outline-primary" data-forecast-period="6">6 أشهر</button>
                        <button class="btn btn-outline-primary" data-forecast-period="12">سنة</button>
                    </div>
                    <select class="form-select" style="width: 200px" id="forecastModel">
                        <option value="linear">نموذج خطي</option>
                        <option value="seasonal">نموذج موسمي</option>
                        <option value="weighted">المتوسط المرجح</option>
                    </select>
                </div>
                <canvas id="forecastChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- إضافة بعد قسم التحليل التنبؤي -->
<div class="row mt-4">
    <!-- تحليل الموسمية -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">تحليل الموسمية والاتجاهات</h5>
                <div class="d-flex justify-content-between mb-3">
                    <div class="btn-group">
                        <button class="btn btn-outline-primary active" data-trend-type="seasonal">موسمي</button>
                        <button class="btn btn-outline-primary" data-trend-type="yearly">سنوي</button>
                    </div>
                    <select class="form-select" style="width: 200px" id="trendMetric">
                        <option value="revenue">الإيرادات</option>
                        <option value="bookings">الحجوزات</option>
                        <option value="occupancy">نسبة الإشغال</option>
                    </select>
                </div>
                <canvas id="seasonalityChart" height="300"></canvas>
            </div>
        </div>
    </div>

    <!-- المقارنة التنافسية -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">المقارنة مع متوسط السوق</h5>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>المؤشر</th>
                                <th>الوكالة</th>
                                <th>متوسط السوق</th>
                                <th>الفرق</th>
                            </tr>
                        </thead>
                        <tbody id="competitiveAnalysis">
                            <!-- سيتم تعبئة البيانات عبر JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تحليل سلوك العملاء -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">تحليل سلوك العملاء</h5>
                <div class="row">
                    <div class="col-md-4">
                        <canvas id="customerSegmentChart"></canvas>
                    </div>
                    <div class="col-md-8">
                        <div id="customerInsights" class="mt-3">
                            <!-- سيتم تعبئة البيانات عبر JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تحليل العوامل الخارجية -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">تحليل تأثير العوامل الخارجية</h5>
                <div class="row mb-3">
                    <div class="col-md-3">
                        <select class="form-select" id="externalFactorType">
                            <option value="seasonal">المواسم السياحية</option>
                            <option value="events">الأحداث الخاصة</option>
                            <option value="weather">الطقس</option>
                            <option value="economic">العوامل الاقتصادية</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="impactMetric">
                            <option value="bookings">تأثير على الحجوزات</option>
                            <option value="revenue">تأثير على الإيرادات</option>
                            <option value="prices">تأثير على الأسعار</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-8">
                        <canvas id="externalFactorsChart" height="300"></canvas>
                    </div>
                    <div class="col-md-4">
                        <div id="factorInsights" class="mt-3">
                            <h6>التحليل والتوصيات</h6>
                            <div class="factor-impact-list">
                                <!-- سيتم تعبئة البيانات عبر JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تكوين الرسم البياني للإيرادات الشهرية
    const monthlyRevenueChart = new Chart(
        document.getElementById('monthlyRevenueChart').getContext('2d'),
        {
            type: 'line',
            data: {
                labels: {{ monthly_revenue|safe }}.map(item => item.month),
                datasets: [{
                    label: 'الإيرادات',
                    data: {{ monthly_revenue|safe }}.map(item => item.total_revenue),
                    borderColor: '#4e73df',
                    tension: 0.1,
                    fill: true
                }, {
                    label: 'عدد الحجوزات',
                    data: {{ monthly_revenue|safe }}.map(item => item.booking_count),
                    borderColor: '#1cc88a',
                    tension: 0.1,
                    yAxisID: 'bookings'
                }]
            },
            options: {
                responsive: true,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    y: {
                        type: 'linear',
                        position: 'left',
                        title: {
                            display: true,
                            text: 'الإيرادات (درهم)'
                        }
                    },
                    bookings: {
                        type: 'linear',
                        position: 'right',
                        title: {
                            display: true,
                            text: 'عدد الحجوزات'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        rtl: true,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.datasetIndex === 0) {
                                    label += context.parsed.y.toFixed(2) + ' درهم';
                                } else {
                                    label += context.parsed.y + ' حجز';
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        }
    );

    // معالجة نموذج الفلترة
    document.getElementById('filterForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        const formData = new FormData(this);

        try {
            const response = await fetch('/api/reports/financial/?' + new URLSearchParams(formData));
            const data = await response.json();

            // تحديث البيانات في الرسوم البيانية
            monthlyRevenueChart.data.labels = data.monthly_revenue.map(item => item.month);
            monthlyRevenueChart.data.datasets[0].data = data.monthly_revenue.map(item => item.total_revenue);
            monthlyRevenueChart.data.datasets[1].data = data.monthly_revenue.map(item => item.booking_count);
            monthlyRevenueChart.update();

            // تحديث الملخص
            updateSummary(data.yearly_summary);
        } catch (error) {
            console.error('Error fetching data:', error);
            alert('حدث خطأ أثناء تحديث البيانات');
        }
    });
});

// دالة تصدير التقارير
function exportReport(format) {
    const filters = new FormData(document.getElementById('filterForm'));
    const queryString = new URLSearchParams(filters).toString();

    const exportUrls = {
        'pdf': `/api/reports/export/pdf/?${queryString}`,
        'excel': `/api/reports/export/excel/?${queryString}`,
        'csv': `/api/reports/export/csv/?${queryString}`
    };

    window.location.href = exportUrls[format];
}

// تهيئة جدول البيانات
$(document).ready(function() {
    $('#transactionsTable').DataTable({
        language: {
            url: "{% static 'js/dataTables.arabic.json' %}"
        },
        order: [[3, 'desc']],
        pageLength: 10,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'collection',
                text: 'تصدير',
                buttons: ['copy', 'excel', 'pdf']
            }
        ]
    });
});

function updateSummary(summary) {
    document.querySelector('[data-summary="revenue"]').textContent =
        `${summary.total_revenue.toFixed(2)} درهم`;
    document.querySelector('[data-summary="bookings"]').textContent =
        summary.total_bookings;
    document.querySelector('[data-summary="average"]').textContent =
        `${summary.average_booking_value.toFixed(2)} درهم`;
}

// تحديث المؤشرات الإضافية
function updateKPIs(data) {
    document.querySelector('[data-summary="growth"]').textContent =
        `${data.monthly_growth.toFixed(1)}%`;
    document.querySelector('[data-summary="occupancy"]').textContent =
        `${data.occupancy_rate.toFixed(1)}%`;
    document.querySelector('[data-summary="collection"]').textContent =
        `${data.collection_rate.toFixed(1)}%`;
    document.querySelector('[data-summary="duration"]').textContent =
        `${data.avg_trip_duration} يوم`;
}

// تهيئة الرسم البياني التنبؤي
const forecastChart = new Chart(
    document.getElementById('forecastChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'البيانات الفعلية',
                    data: [],
                    borderColor: '#4e73df',
                    fill: false
                },
                {
                    label: 'التنبؤ',
                    data: [],
                    borderColor: '#1cc88a',
                    borderDash: [5, 5],
                    fill: false
                },
                {
                    label: 'هامش الثقة (95%)',
                    data: [],
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    borderWidth: 0,
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    mode: 'index',
                    intersect: false
                },
                legend: {
                    rtl: true,
                    position: 'top'
                }
            }
        }
    }
);

// دالة تحديث التنبؤات
async function updateForecast(period = 3, model = 'linear') {
    try {
        const response = await fetch(`/api/reports/forecast/?period=${period}&model=${model}`);
        const data = await response.json();

        forecastChart.data.labels = [...data.actual_dates, ...data.forecast_dates];
        forecastChart.data.datasets[0].data = data.actual_values;
        forecastChart.data.datasets[1].data = data.forecast_values;
        forecastChart.data.datasets[2].data = data.confidence_interval;

        forecastChart.update();

        // تحديث مؤشرات التنبؤ
        updateForecastMetrics(data.metrics);
    } catch (error) {
        console.error('خطأ في تحديث التنبؤات:', error);
        notify.error('حدث خطأ أثناء تحديث التنبؤات');
    }
}

// دالة تحديث مؤشرات التنبؤ
function updateForecastMetrics(metrics) {
    const metricsContainer = document.querySelector('#forecastMetrics');
    metricsContainer.innerHTML = `
        <div class="row g-3">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>النمو المتوقع</h6>
                        <h3>${metrics.expected_growth}%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>دقة التنبؤ</h6>
                        <h3>${metrics.accuracy}%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>الإيرادات المتوقعة</h6>
                        <h3>${metrics.expected_revenue} درهم</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>معامل الثقة</h6>
                        <h3>${metrics.confidence_level}%</h3>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// إضافة مستمعي الأحداث
document.querySelectorAll('[data-forecast-period]').forEach(button => {
    button.addEventListener('click', (e) => {
        document.querySelector('[data-forecast-period].active').classList.remove('active');
        e.target.classList.add('active');
        updateForecast(parseInt(e.target.dataset.forecastPeriod),
                      document.getElementById('forecastModel').value);
    });
});

document.getElementById('forecastModel').addEventListener('change', (e) => {
    const activePeriod = document.querySelector('[data-forecast-period].active');
    updateForecast(parseInt(activePeriod.dataset.forecastPeriod), e.target.value);
});

// تحديث التنبؤات عند تحميل الصفحة
updateForecast();

// تهيئة الرسم البياني لتحليل الموسمية
const seasonalityChart = new Chart(
    document.getElementById('seasonalityChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'الاتجاه العام',
                    data: [],
                    borderColor: '#4e73df',
                    borderWidth: 2,
                    fill: false
                },
                {
                    label: 'البيانات الموسمية',
                    data: [],
                    borderColor: '#1cc88a',
                    borderWidth: 1,
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    mode: 'index',
                    intersect: false
                }
            }
        }
    }
);

// تحليل سلوك العملاء
const customerSegmentChart = new Chart(
    document.getElementById('customerSegmentChart').getContext('2d'),
    {
        type: 'doughnut',
        data: {
            labels: ['عملاء جدد', 'عملاء متكررون', 'عملاء موسميون'],
            datasets: [{
                data: [],
                backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    rtl: true,
                    position: 'bottom'
                }
            }
        }
    }
);

// دالة تحديث تحليل الموسمية
async function updateSeasonalityAnalysis(type = 'seasonal', metric = 'revenue') {
    try {
        const response = await fetch(`/api/reports/seasonality/?type=${type}&metric=${metric}`);
        const data = await response.json();

        seasonalityChart.data.labels = data.labels;
        seasonalityChart.data.datasets[0].data = data.trend;
        seasonalityChart.data.datasets[1].data = data.seasonal;
        seasonalityChart.update();

        updateCompetitiveAnalysis(data.market_comparison);
    } catch (error) {
        console.error('خطأ في تحديث تحليل الموسمية:', error);
        notify.error('حدث خطأ أثناء تحديث التحليل الموسمي');
    }
}

// دالة تحديث المقارنة التنافسية
function updateCompetitiveAnalysis(data) {
    const tbody = document.getElementById('competitiveAnalysis');
    tbody.innerHTML = data.map(item => `
        <tr>
            <td>${item.metric}</td>
            <td>${item.agency_value}</td>
            <td>${item.market_average}</td>
            <td>
                <span class="badge bg-${item.difference >= 0 ? 'success' : 'danger'}">
                    ${item.difference}%
                </span>
            </td>
        </tr>
    `).join('');
}

// دالة تحديث تحليل سلوك العملاء
async function updateCustomerAnalysis() {
    try {
        const response = await fetch('/api/reports/customer-analysis/');
        const data = await response.json();

        customerSegmentChart.data.datasets[0].data = data.segments;
        customerSegmentChart.update();

        document.getElementById('customerInsights').innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>أنماط الحجز</h6>
                    <ul class="list-unstyled">
                        ${data.booking_patterns.map(pattern =>
                            `<li class="mb-2">${pattern}</li>`
                        ).join('')}
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>توصيات التسويق</h6>
                    <ul class="list-unstyled">
                        ${data.marketing_recommendations.map(rec =>
                            `<li class="mb-2">${rec}</li>`
                        ).join('')}
                    </ul>
                </div>
            </div>
        `;
    } catch (error) {
        console.error('خطأ في تحديث تحليل العملاء:', error);
        notify.error('حدث خطأ أثناء تحديث تحليل العملاء');
    }
}

// تهيئة رسم بياني لتحليل العوامل الخارجية
const externalFactorsChart = new Chart(
    document.getElementById('externalFactorsChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'المؤشر الرئيسي',
                    data: [],
                    borderColor: '#4e73df',
                    fill: false
                },
                {
                    label: 'العامل الخارجي',
                    data: [],
                    borderColor: '#1cc88a',
                    borderDash: [5, 5],
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            return label + context.parsed.y.toFixed(2);
                        }
                    }
                }
            }
        }
    }
);

// دالة تحديث تحليل العوامل الخارجية
async function updateExternalFactorsAnalysis() {
    const factorType = document.getElementById('externalFactorType').value;
    const metric = document.getElementById('impactMetric').value;

    try {
        const response = await fetch(`/api/reports/external-factors/?type=${factorType}&metric=${metric}`);
        const data = await response.json();

        // تحديث الرسم البياني
        externalFactorsChart.data.labels = data.dates;
        externalFactorsChart.data.datasets[0].data = data.metric_values;
        externalFactorsChart.data.datasets[1].data = data.factor_values;
        externalFactorsChart.update();

        // تحديث التحليلات والتوصيات
        updateFactorInsights(data.insights);
    } catch (error) {
        console.error('خطأ في تحديث تحليل العوامل الخارجية:', error);
        notify.error('حدث خطأ أثناء تحديث التحليل');
    }
}

// دالة تحديث التحليلات والتوصيات
function updateFactorInsights(insights) {
    const container = document.querySelector('.factor-impact-list');
    container.innerHTML = insights.map(insight => `
        <div class="alert ${insight.impact > 0 ? 'alert-success' : 'alert-warning'} mb-2">
            <h6 class="alert-heading">${insight.title}</h6>
            <p class="mb-1">${insight.description}</p>
            <small>التأثير: ${insight.impact > 0 ? '+' : ''}${insight.impact}%</small>
        </div>
    `).join('');
}

// إضافة مستمعي الأحداث للتحليلات الجديدة
document.getElementById('externalFactorType').addEventListener('change', updateExternalFactorsAnalysis);
document.getElementById('impactMetric').addEventListener('change', updateExternalFactorsAnalysis);

// تحديث التحليلات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تكوين الرسم البياني للإيرادات الشهرية
    const monthlyRevenueChart = new Chart(
        document.getElementById('monthlyRevenueChart').getContext('2d'),
        {
            type: 'line',
            data: {
                labels: {{ monthly_revenue|safe }}.map(item => item.month),
                datasets: [{
                    label: 'الإيرادات',
                    data: {{ monthly_revenue|safe }}.map(item => item.total_revenue),
                    borderColor: '#4e73df',
                    tension: 0.1,
                    fill: true
                }, {
                    label: 'عدد الحجوزات',
                    data: {{ monthly_revenue|safe }}.map(item => item.booking_count),
                    borderColor: '#1cc88a',
                    tension: 0.1,
                    yAxisID: 'bookings'
                }]
            },
            options: {
                responsive: true,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    y: {
                        type: 'linear',
                        position: 'left',
                        title: {
                            display: true,
                            text: 'الإيرادات (درهم)'
                        }
                    },
                    bookings: {
                        type: 'linear',
                        position: 'right',
                        title: {
                            display: true,
                            text: 'عدد الحجوزات'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        rtl: true,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.datasetIndex === 0) {
                                    label += context.parsed.y.toFixed(2) + ' درهم';
                                } else {
                                    label += context.parsed.y + ' حجز';
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        }
    );

    // معالجة نموذج الفلترة
    document.getElementById('filterForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        const formData = new FormData(this);

        try {
            const response = await fetch('/api/reports/financial/?' + new URLSearchParams(formData));
            const data = await response.json();

            // تحديث البيانات في الرسوم البيانية
            monthlyRevenueChart.data.labels = data.monthly_revenue.map(item => item.month);
            monthlyRevenueChart.data.datasets[0].data = data.monthly_revenue.map(item => item.total_revenue);
            monthlyRevenueChart.data.datasets[1].data = data.monthly_revenue.map(item => item.booking_count);
            monthlyRevenueChart.update();

            // تحديث الملخص
            updateSummary(data.yearly_summary);
        } catch (error) {
            console.error('Error fetching data:', error);
            alert('حدث خطأ أثناء تحديث البيانات');
        }
    });
});

// دالة تصدير التقارير
function exportReport(format) {
    const filters = new FormData(document.getElementById('filterForm'));
    const queryString = new URLSearchParams(filters).toString();

    const exportUrls = {
        'pdf': `/api/reports/export/pdf/?${queryString}`,
        'excel': `/api/reports/export/excel/?${queryString}`,
        'csv': `/api/reports/export/csv/?${queryString}`
    };

    window.location.href = exportUrls[format];
}

// تهيئة جدول البيانات
$(document).ready(function() {
    $('#transactionsTable').DataTable({
        language: {
            url: "{% static 'js/dataTables.arabic.json' %}"
        },
        order: [[3, 'desc']],
        pageLength: 10,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'collection',
                text: 'تصدير',
                buttons: ['copy', 'excel', 'pdf']
            }
        ]
    });
});

function updateSummary(summary) {
    document.querySelector('[data-summary="revenue"]').textContent =
        `${summary.total_revenue.toFixed(2)} درهم`;
    document.querySelector('[data-summary="bookings"]').textContent =
        summary.total_bookings;
    document.querySelector('[data-summary="average"]').textContent =
        `${summary.average_booking_value.toFixed(2)} درهم`;
}

// تحديث المؤشرات الإضافية
function updateKPIs(data) {
    document.querySelector('[data-summary="growth"]').textContent =
        `${data.monthly_growth.toFixed(1)}%`;
    document.querySelector('[data-summary="occupancy"]').textContent =
        `${data.occupancy_rate.toFixed(1)}%`;
    document.querySelector('[data-summary="collection"]').textContent =
        `${data.collection_rate.toFixed(1)}%`;
    document.querySelector('[data-summary="duration"]').textContent =
        `${data.avg_trip_duration} يوم`;
}

// تهيئة الرسم البياني التنبؤي
const forecastChart = new Chart(
    document.getElementById('forecastChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'البيانات الفعلية',
                    data: [],
                    borderColor: '#4e73df',
                    fill: false
                },
                {
                    label: 'التنبؤ',
                    data: [],
                    borderColor: '#1cc88a',
                    borderDash: [5, 5],
                    fill: false
                },
                {
                    label: 'هامش الثقة (95%)',
                    data: [],
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    borderWidth: 0,
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    mode: 'index',
                    intersect: false
                },
                legend: {
                    rtl: true,
                    position: 'top'
                }
            }
        }
    }
);

// دالة تحديث التنبؤات
async function updateForecast(period = 3, model = 'linear') {
    try {
        const response = await fetch(`/api/reports/forecast/?period=${period}&model=${model}`);
        const data = await response.json();

        forecastChart.data.labels = [...data.actual_dates, ...data.forecast_dates];
        forecastChart.data.datasets[0].data = data.actual_values;
        forecastChart.data.datasets[1].data = data.forecast_values;
        forecastChart.data.datasets[2].data = data.confidence_interval;

        forecastChart.update();

        // تحديث مؤشرات التنبؤ
        updateForecastMetrics(data.metrics);
    } catch (error) {
        console.error('خطأ في تحديث التنبؤات:', error);
        notify.error('حدث خطأ أثناء تحديث التنبؤات');
    }
}

// دالة تحديث مؤشرات التنبؤ
function updateForecastMetrics(metrics) {
    const metricsContainer = document.querySelector('#forecastMetrics');
    metricsContainer.innerHTML = `
        <div class="row g-3">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>النمو المتوقع</h6>
                        <h3>${metrics.expected_growth}%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>دقة التنبؤ</h6>
                        <h3>${metrics.accuracy}%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>الإيرادات المتوقعة</h6>
                        <h3>${metrics.expected_revenue} درهم</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>معامل الثقة</h6>
                        <h3>${metrics.confidence_level}%</h3>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// إضافة مستمعي الأحداث
document.querySelectorAll('[data-forecast-period]').forEach(button => {
    button.addEventListener('click', (e) => {
        document.querySelector('[data-forecast-period].active').classList.remove('active');
        e.target.classList.add('active');
        updateForecast(parseInt(e.target.dataset.forecastPeriod),
                      document.getElementById('forecastModel').value);
    });
});

document.getElementById('forecastModel').addEventListener('change', (e) => {
    const activePeriod = document.querySelector('[data-forecast-period].active');
    updateForecast(parseInt(activePeriod.dataset.forecastPeriod), e.target.value);
});

// تحديث التنبؤات عند تحميل الصفحة
updateForecast();

// تهيئة الرسم البياني لتحليل الموسمية
const seasonalityChart = new Chart(
    document.getElementById('seasonalityChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'الاتجاه العام',
                    data: [],
                    borderColor: '#4e73df',
                    borderWidth: 2,
                    fill: false
                },
                {
                    label: 'البيانات الموسمية',
                    data: [],
                    borderColor: '#1cc88a',
                    borderWidth: 1,
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    mode: 'index',
                    intersect: false
                }
            }
        }
    }
);

// تحليل سلوك العملاء
const customerSegmentChart = new Chart(
    document.getElementById('customerSegmentChart').getContext('2d'),
    {
        type: 'doughnut',
        data: {
            labels: ['عملاء جدد', 'عملاء متكررون', 'عملاء موسميون'],
            datasets: [{
                data: [],
                backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    rtl: true,
                    position: 'bottom'
                }
            }
        }
    }
);

// دالة تحديث تحليل الموسمية
async function updateSeasonalityAnalysis(type = 'seasonal', metric = 'revenue') {
    try {
        const response = await fetch(`/api/reports/seasonality/?type=${type}&metric=${metric}`);
        const data = await response.json();

        seasonalityChart.data.labels = data.labels;
        seasonalityChart.data.datasets[0].data = data.trend;
        seasonalityChart.data.datasets[1].data = data.seasonal;
        seasonalityChart.update();

        updateCompetitiveAnalysis(data.market_comparison);
    } catch (error) {
        console.error('خطأ في تحديث تحليل الموسمية:', error);
        notify.error('حدث خطأ أثناء تحديث التحليل الموسمي');
    }
}

// دالة تحديث المقارنة التنافسية
function updateCompetitiveAnalysis(data) {
    const tbody = document.getElementById('competitiveAnalysis');
    tbody.innerHTML = data.map(item => `
        <tr>
            <td>${item.metric}</td>
            <td>${item.agency_value}</td>
            <td>${item.market_average}</td>
            <td>
                <span class="badge bg-${item.difference >= 0 ? 'success' : 'danger'}">
                    ${item.difference}%
                </span>
            </td>
        </tr>
    `).join('');
}

// دالة تحديث تحليل سلوك العملاء
async function updateCustomerAnalysis() {
    try {
        const response = await fetch('/api/reports/customer-analysis/');
        const data = await response.json();

        customerSegmentChart.data.datasets[0].data = data.segments;
        customerSegmentChart.update();

        document.getElementById('customerInsights').innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>أنماط الحجز</h6>
                    <ul class="list-unstyled">
                        ${data.booking_patterns.map(pattern =>
                            `<li class="mb-2">${pattern}</li>`
                        ).join('')}
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>توصيات التسويق</h6>
                    <ul class="list-unstyled">
                        ${data.marketing_recommendations.map(rec =>
                            `<li class="mb-2">${rec}</li>`
                        ).join('')}
                    </ul>
                </div>
            </div>
        `;
    } catch (error) {
        console.error('خطأ في تحديث تحليل العملاء:', error);
        notify.error('حدث خطأ أثناء تحديث تحليل العملاء');
    }
}

// تهيئة رسم بياني لتحليل العوامل الخارجية
const externalFactorsChart = new Chart(
    document.getElementById('externalFactorsChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'المؤشر الرئيسي',
                    data: [],
                    borderColor: '#4e73df',
                    fill: false
                },
                {
                    label: 'العامل الخارجي',
                    data: [],
                    borderColor: '#1cc88a',
                    borderDash: [5, 5],
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            return label + context.parsed.y.toFixed(2);
                        }
                    }
                }
            }
        }
    }
);

// دالة تحديث تحليل العوامل الخارجية
async function updateExternalFactorsAnalysis() {
    const factorType = document.getElementById('externalFactorType').value;
    const metric = document.getElementById('impactMetric').value;

    try {
        const response = await fetch(`/api/reports/external-factors/?type=${factorType}&metric=${metric}`);
        const data = await response.json();

        // تحديث الرسم البياني
        externalFactorsChart.data.labels = data.dates;
        externalFactorsChart.data.datasets[0].data = data.metric_values;
        externalFactorsChart.data.datasets[1].data = data.factor_values;
        externalFactorsChart.update();

        // تحديث التحليلات والتوصيات
        updateFactorInsights(data.insights);
    } catch (error) {
        console.error('خطأ في تحديث تحليل العوامل الخارجية:', error);
        notify.error('حدث خطأ أثناء تحديث التحليل');
    }
}

// دالة تحديث التحليلات والتوصيات
function updateFactorInsights(insights) {
    const container = document.querySelector('.factor-impact-list');
    container.innerHTML = insights.map(insight => `
        <div class="alert ${insight.impact > 0 ? 'alert-success' : 'alert-warning'} mb-2">
            <h6 class="alert-heading">${insight.title}</h6>
            <p class="mb-1">${insight.description}</p>
            <small>التأثير: ${insight.impact > 0 ? '+' : ''}${insight.impact}%</small>
        </div>
    `).join('');
}

// إضافة مستمعي الأحداث للتحليلات الجديدة
document.getElementById('externalFactorType').addEventListener('change', updateExternalFactorsAnalysis);
document.getElementById('impactMetric').addEventListener('change', updateExternalFactorsAnalysis);

// تحديث التحليلات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تكوين الرسم البياني للإيرادات الشهرية
    const monthlyRevenueChart = new Chart(
        document.getElementById('monthlyRevenueChart').getContext('2d'),
        {
            type: 'line',
            data: {
                labels: {{ monthly_revenue|safe }}.map(item => item.month),
                datasets: [{
                    label: 'الإيرادات',
                    data: {{ monthly_revenue|safe }}.map(item => item.total_revenue),
                    borderColor: '#4e73df',
                    tension: 0.1,
                    fill: true
                }, {
                    label: 'عدد الحجوزات',
                    data: {{ monthly_revenue|safe }}.map(item => item.booking_count),
                    borderColor: '#1cc88a',
                    tension: 0.1,
                    yAxisID: 'bookings'
                }]
            },
            options: {
                responsive: true,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    y: {
                        type: 'linear',
                        position: 'left',
                        title: {
                            display: true,
                            text: 'الإيرادات (درهم)'
                        }
                    },
                    bookings: {
                        type: 'linear',
                        position: 'right',
                        title: {
                            display: true,
                            text: 'عدد الحجوزات'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        rtl: true,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.datasetIndex === 0) {
                                    label += context.parsed.y.toFixed(2) + ' درهم';
                                } else {
                                    label += context.parsed.y + ' حجز';
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        }
    );

    // معالجة نموذج الفلترة
    document.getElementById('filterForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        const formData = new FormData(this);

        try {
            const response = await fetch('/api/reports/financial/?' + new URLSearchParams(formData));
            const data = await response.json();

            // تحديث البيانات في الرسوم البيانية
            monthlyRevenueChart.data.labels = data.monthly_revenue.map(item => item.month);
            monthlyRevenueChart.data.datasets[0].data = data.monthly_revenue.map(item => item.total_revenue);
            monthlyRevenueChart.data.datasets[1].data = data.monthly_revenue.map(item => item.booking_count);
            monthlyRevenueChart.update();

            // تحديث الملخص
            updateSummary(data.yearly_summary);
        } catch (error) {
            console.error('Error fetching data:', error);
            alert('حدث خطأ أثناء تحديث البيانات');
        }
    });
});

// دالة تصدير التقارير
function exportReport(format) {
    const filters = new FormData(document.getElementById('filterForm'));
    const queryString = new URLSearchParams(filters).toString();

    const exportUrls = {
        'pdf': `/api/reports/export/pdf/?${queryString}`,
        'excel': `/api/reports/export/excel/?${queryString}`,
        'csv': `/api/reports/export/csv/?${queryString}`
    };

    window.location.href = exportUrls[format];
}

// تهيئة جدول البيانات
$(document).ready(function() {
    $('#transactionsTable').DataTable({
        language: {
            url: "{% static 'js/dataTables.arabic.json' %}"
        },
        order: [[3, 'desc']],
        pageLength: 10,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'collection',
                text: 'تصدير',
                buttons: ['copy', 'excel', 'pdf']
            }
        ]
    });
});

function updateSummary(summary) {
    document.querySelector('[data-summary="revenue"]').textContent =
        `${summary.total_revenue.toFixed(2)} درهم`;
    document.querySelector('[data-summary="bookings"]').textContent =
        summary.total_bookings;
    document.querySelector('[data-summary="average"]').textContent =
        `${summary.average_booking_value.toFixed(2)} درهم`;
}

// تحديث المؤشرات الإضافية
function updateKPIs(data) {
    document.querySelector('[data-summary="growth"]').textContent =
        `${data.monthly_growth.toFixed(1)}%`;
    document.querySelector('[data-summary="occupancy"]').textContent =
        `${data.occupancy_rate.toFixed(1)}%`;
    document.querySelector('[data-summary="collection"]').textContent =
        `${data.collection_rate.toFixed(1)}%`;
    document.querySelector('[data-summary="duration"]').textContent =
        `${data.avg_trip_duration} يوم`;
}

// تهيئة الرسم البياني التنبؤي
const forecastChart = new Chart(
    document.getElementById('forecastChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'البيانات الفعلية',
                    data: [],
                    borderColor: '#4e73df',
                    fill: false
                },
                {
                    label: 'التنبؤ',
                    data: [],
                    borderColor: '#1cc88a',
                    borderDash: [5, 5],
                    fill: false
                },
                {
                    label: 'هامش الثقة (95%)',
                    data: [],
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    borderWidth: 0,
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    mode: 'index',
                    intersect: false
                },
                legend: {
                    rtl: true,
                    position: 'top'
                }
            }
        }
    }
);

// دالة تحديث التنبؤات
async function updateForecast(period = 3, model = 'linear') {
    try {
        const response = await fetch(`/api/reports/forecast/?period=${period}&model=${model}`);
        const data = await response.json();

        forecastChart.data.labels = [...data.actual_dates, ...data.forecast_dates];
        forecastChart.data.datasets[0].data = data.actual_values;
        forecastChart.data.datasets[1].data = data.forecast_values;
        forecastChart.data.datasets[2].data = data.confidence_interval;

        forecastChart.update();

        // تحديث مؤشرات التنبؤ
        updateForecastMetrics(data.metrics);
    } catch (error) {
        console.error('خطأ في تحديث التنبؤات:', error);
        notify.error('حدث خطأ أثناء تحديث التنبؤات');
    }
}

// دالة تحديث مؤشرات التنبؤ
function updateForecastMetrics(metrics) {
    const metricsContainer = document.querySelector('#forecastMetrics');
    metricsContainer.innerHTML = `
        <div class="row g-3">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>النمو المتوقع</h6>
                        <h3>${metrics.expected_growth}%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>دقة التنبؤ</h6>
                        <h3>${metrics.accuracy}%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>الإيرادات المتوقعة</h6>
                        <h3>${metrics.expected_revenue} درهم</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>معامل الثقة</h6>
                        <h3>${metrics.confidence_level}%</h3>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// إضافة مستمعي الأحداث
document.querySelectorAll('[data-forecast-period]').forEach(button => {
    button.addEventListener('click', (e) => {
        document.querySelector('[data-forecast-period].active').classList.remove('active');
        e.target.classList.add('active');
        updateForecast(parseInt(e.target.dataset.forecastPeriod),
                      document.getElementById('forecastModel').value);
    });
});

document.getElementById('forecastModel').addEventListener('change', (e) => {
    const activePeriod = document.querySelector('[data-forecast-period].active');
    updateForecast(parseInt(activePeriod.dataset.forecastPeriod), e.target.value);
});

// تحديث التنبؤات عند تحميل الصفحة
updateForecast();

// تهيئة الرسم البياني لتحليل الموسمية
const seasonalityChart = new Chart(
    document.getElementById('seasonalityChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'الاتجاه العام',
                    data: [],
                    borderColor: '#4e73df',
                    borderWidth: 2,
                    fill: false
                },
                {
                    label: 'البيانات الموسمية',
                    data: [],
                    borderColor: '#1cc88a',
                    borderWidth: 1,
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    mode: 'index',
                    intersect: false
                }
            }
        }
    }
);

// تحليل سلوك العملاء
const customerSegmentChart = new Chart(
    document.getElementById('customerSegmentChart').getContext('2d'),
    {
        type: 'doughnut',
        data: {
            labels: ['عملاء جدد', 'عملاء متكررون', 'عملاء موسميون'],
            datasets: [{
                data: [],
                backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    rtl: true,
                    position: 'bottom'
                }
            }
        }
    }
);

// دالة تحديث تحليل الموسمية
async function updateSeasonalityAnalysis(type = 'seasonal', metric = 'revenue') {
    try {
        const response = await fetch(`/api/reports/seasonality/?type=${type}&metric=${metric}`);
        const data = await response.json();

        seasonalityChart.data.labels = data.labels;
        seasonalityChart.data.datasets[0].data = data.trend;
        seasonalityChart.data.datasets[1].data = data.seasonal;
        seasonalityChart.update();

        updateCompetitiveAnalysis(data.market_comparison);
    } catch (error) {
        console.error('خطأ في تحديث تحليل الموسمية:', error);
        notify.error('حدث خطأ أثناء تحديث التحليل الموسمي');
    }
}

// دالة تحديث المقارنة التنافسية
function updateCompetitiveAnalysis(data) {
    const tbody = document.getElementById('competitiveAnalysis');
    tbody.innerHTML = data.map(item => `
        <tr>
            <td>${item.metric}</td>
            <td>${item.agency_value}</td>
            <td>${item.market_average}</td>
            <td>
                <span class="badge bg-${item.difference >= 0 ? 'success' : 'danger'}">
                    ${item.difference}%
                </span>
            </td>
        </tr>
    `).join('');
}

// دالة تحديث تحليل سلوك العملاء
async function updateCustomerAnalysis() {
    try {
        const response = await fetch('/api/reports/customer-analysis/');
        const data = await response.json();

        customerSegmentChart.data.datasets[0].data = data.segments;
        customerSegmentChart.update();

        document.getElementById('customerInsights').innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>أنماط الحجز</h6>
                    <ul class="list-unstyled">
                        ${data.booking_patterns.map(pattern =>
                            `<li class="mb-2">${pattern}</li>`
                        ).join('')}
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>توصيات التسويق</h6>
                    <ul class="list-unstyled">
                        ${data.marketing_recommendations.map(rec =>
                            `<li class="mb-2">${rec}</li>`
                        ).join('')}
                    </ul>
                </div>
            </div>
        `;
    } catch (error) {
        console.error('خطأ في تحديث تحليل العملاء:', error);
        notify.error('حدث خطأ أثناء تحديث تحليل العملاء');
    }
}

// تهيئة رسم بياني لتحليل العوامل الخارجية
const externalFactorsChart = new Chart(
    document.getElementById('externalFactorsChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'المؤشر الرئيسي',
                    data: [],
                    borderColor: '#4e73df',
                    fill: false
                },
                {
                    label: 'العامل الخارجي',
                    data: [],
                    borderColor: '#1cc88a',
                    borderDash: [5, 5],
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            return label + context.parsed.y.toFixed(2);
                        }
                    }
                }
            }
        }
    }
);

// دالة تحديث تحليل العوامل الخارجية
async function updateExternalFactorsAnalysis() {
    const factorType = document.getElementById('externalFactorType').value;
    const metric = document.getElementById('impactMetric').value;

    try {
        const response = await fetch(`/api/reports/external-factors/?type=${factorType}&metric=${metric}`);
        const data = await response.json();

        // تحديث الرسم البياني
        externalFactorsChart.data.labels = data.dates;
        externalFactorsChart.data.datasets[0].data = data.metric_values;
        externalFactorsChart.data.datasets[1].data = data.factor_values;
        externalFactorsChart.update();

        // تحديث التحليلات والتوصيات
        updateFactorInsights(data.insights);
    } catch (error) {
        console.error('خطأ في تحديث تحليل العوامل الخارجية:', error);
        notify.error('حدث خطأ أثناء تحديث التحليل');
    }
}

// دالة تحديث التحليلات والتوصيات
function updateFactorInsights(insights) {
    const container = document.querySelector('.factor-impact-list');
    container.innerHTML = insights.map(insight => `
        <div class="alert ${insight.impact > 0 ? 'alert-success' : 'alert-warning'} mb-2">
            <h6 class="alert-heading">${insight.title}</h6>
            <p class="mb-1">${insight.description}</p>
            <small>التأثير: ${insight.impact > 0 ? '+' : ''}${insight.impact}%</small>
        </div>
    `).join('');
}

// إضافة مستمعي الأحداث للتحليلات الجديدة
document.getElementById('externalFactorType').addEventListener('change', updateExternalFactorsAnalysis);
document.getElementById('impactMetric').addEventListener('change', updateExternalFactorsAnalysis);

// تحديث التحليلات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تكوين الرسم البياني للإيرادات الشهرية
    const monthlyRevenueChart = new Chart(
        document.getElementById('monthlyRevenueChart').getContext('2d'),
        {
            type: 'line',
            data: {
                labels: {{ monthly_revenue|safe }}.map(item => item.month),
                datasets: [{
                    label: 'الإيرادات',
                    data: {{ monthly_revenue|safe }}.map(item => item.total_revenue),
                    borderColor: '#4e73df',
                    tension: 0.1,
                    fill: true
                }, {
                    label: 'عدد الحجوزات',
                    data: {{ monthly_revenue|safe }}.map(item => item.booking_count),
                    borderColor: '#1cc88a',
                    tension: 0.1,
                    yAxisID: 'bookings'
                }]
            },
            options: {
                responsive: true,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    y: {
                        type: 'linear',
                        position: 'left',
                        title: {
                            display: true,
                            text: 'الإيرادات (درهم)'
                        }
                    },
                    bookings: {
                        type: 'linear',
                        position: 'right',
                        title: {
                            display: true,
                            text: 'عدد الحجوزات'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        rtl: true,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.datasetIndex === 0) {
                                    label += context.parsed.y.toFixed(2) + ' درهم';
                                } else {
                                    label += context.parsed.y + ' حجز';
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        }
    );

    // معالجة نموذج الفلترة
    document.getElementById('filterForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        const formData = new FormData(this);

        try {
            const response = await fetch('/api/reports/financial/?' + new URLSearchParams(formData));
            const data = await response.json();

            // تحديث البيانات في الرسوم البيانية
            monthlyRevenueChart.data.labels = data.monthly_revenue.map(item => item.month);
            monthlyRevenueChart.data.datasets[0].data = data.monthly_revenue.map(item => item.total_revenue);
            monthlyRevenueChart.data.datasets[1].data = data.monthly_revenue.map(item => item.booking_count);
            monthlyRevenueChart.update();

            // تحديث الملخص
            updateSummary(data.yearly_summary);
        } catch (error) {
            console.error('Error fetching data:', error);
            alert('حدث خطأ أثناء تحديث البيانات');
        }
    });
});

// دالة تصدير التقارير
function exportReport(format) {
    const filters = new FormData(document.getElementById('filterForm'));
    const queryString = new URLSearchParams(filters).toString();

    const exportUrls = {
        'pdf': `/api/reports/export/pdf/?${queryString}`,
        'excel': `/api/reports/export/excel/?${queryString}`,
        'csv': `/api/reports/export/csv/?${queryString}`
    };

    window.location.href = exportUrls[format];
}

// تهيئة جدول البيانات
$(document).ready(function() {
    $('#transactionsTable').DataTable({
        language: {
            url: "{% static 'js/dataTables.arabic.json' %}"
        },
        order: [[3, 'desc']],
        pageLength: 10,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'collection',
                text: 'تصدير',
                buttons: ['copy', 'excel', 'pdf']
            }
        ]
    });
});

function updateSummary(summary) {
    document.querySelector('[data-summary="revenue"]').textContent =
        `${summary.total_revenue.toFixed(2)} درهم`;
    document.querySelector('[data-summary="bookings"]').textContent =
        summary.total_bookings;
    document.querySelector('[data-summary="average"]').textContent =
        `${summary.average_booking_value.toFixed(2)} درهم`;
}

// تحديث المؤشرات الإضافية
function updateKPIs(data) {
    document.querySelector('[data-summary="growth"]').textContent =
        `${data.monthly_growth.toFixed(1)}%`;
    document.querySelector('[data-summary="occupancy"]').textContent =
        `${data.occupancy_rate.toFixed(1)}%`;
    document.querySelector('[data-summary="collection"]').textContent =
        `${data.collection_rate.toFixed(1)}%`;
    document.querySelector('[data-summary="duration"]').textContent =
        `${data.avg_trip_duration} يوم`;
}

// تهيئة الرسم البياني التنبؤي
const forecastChart = new Chart(
    document.getElementById('forecastChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'البيانات الفعلية',
                    data: [],
                    borderColor: '#4e73df',
                    fill: false
                },
                {
                    label: 'التنبؤ',
                    data: [],
                    borderColor: '#1cc88a',
                    borderDash: [5, 5],
                    fill: false
                },
                {
                    label: 'هامش الثقة (95%)',
                    data: [],
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    borderWidth: 0,
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    mode: 'index',
                    intersect: false
                },
                legend: {
                    rtl: true,
                    position: 'top'
                }
            }
        }
    }
);

// دالة تحديث التنبؤات
async function updateForecast(period = 3, model = 'linear') {
    try {
        const response = await fetch(`/api/reports/forecast/?period=${period}&model=${model}`);
        const data = await response.json();

        forecastChart.data.labels = [...data.actual_dates, ...data.forecast_dates];
        forecastChart.data.datasets[0].data = data.actual_values;
        forecastChart.data.datasets[1].data = data.forecast_values;
        forecastChart.data.datasets[2].data = data.confidence_interval;

        forecastChart.update();

        // تحديث مؤشرات التنبؤ
        updateForecastMetrics(data.metrics);
    } catch (error) {
        console.error('خطأ في تحديث التنبؤات:', error);
        notify.error('حدث خطأ أثناء تحديث التنبؤات');
    }
}

// دالة تحديث مؤشرات التنبؤ
function updateForecastMetrics(metrics) {
    const metricsContainer = document.querySelector('#forecastMetrics');
    metricsContainer.innerHTML = `
        <div class="row g-3">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>النمو المتوقع</h6>
                        <h3>${metrics.expected_growth}%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>دقة التنبؤ</h6>
                        <h3>${metrics.accuracy}%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>الإيرادات المتوقعة</h6>
                        <h3>${metrics.expected_revenue} درهم</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>معامل الثقة</h6>
                        <h3>${metrics.confidence_level}%</h3>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// إضافة مستمعي الأحداث
document.querySelectorAll('[data-forecast-period]').forEach(button => {
    button.addEventListener('click', (e) => {
        document.querySelector('[data-forecast-period].active').classList.remove('active');
        e.target.classList.add('active');
        updateForecast(parseInt(e.target.dataset.forecastPeriod),
                      document.getElementById('forecastModel').value);
    });
});

document.getElementById('forecastModel').addEventListener('change', (e) => {
    const activePeriod = document.querySelector('[data-forecast-period].active');
    updateForecast(parseInt(activePeriod.dataset.forecastPeriod), e.target.value);
});

// تحديث التنبؤات عند تحميل الصفحة
updateForecast();

// تهيئة الرسم البياني لتحليل الموسمية
const seasonalityChart = new Chart(
    document.getElementById('seasonalityChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'الاتجاه العام',
                    data: [],
                    borderColor: '#4e73df',
                    borderWidth: 2,
                    fill: false
                },
                {
                    label: 'البيانات الموسمية',
                    data: [],
                    borderColor: '#1cc88a',
                    borderWidth: 1,
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    mode: 'index',
                    intersect: false
                }
            }
        }
    }
);

// تحليل سلوك العملاء
const customerSegmentChart = new Chart(
    document.getElementById('customerSegmentChart').getContext('2d'),
    {
        type: 'doughnut',
        data: {
            labels: ['عملاء جدد', 'عملاء متكررون', 'عملاء موسميون'],
            datasets: [{
                data: [],
                backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    rtl: true,
                    position: 'bottom'
                }
            }
        }
    }
);

// دالة تحديث تحليل الموسمية
async function updateSeasonalityAnalysis(type = 'seasonal', metric = 'revenue') {
    try {
        const response = await fetch(`/api/reports/seasonality/?type=${type}&metric=${metric}`);
        const data = await response.json();

        seasonalityChart.data.labels = data.labels;
        seasonalityChart.data.datasets[0].data = data.trend;
        seasonalityChart.data.datasets[1].data = data.seasonal;
        seasonalityChart.update();

        updateCompetitiveAnalysis(data.market_comparison);
    } catch (error) {
        console.error('خطأ في تحديث تحليل الموسمية:', error);
        notify.error('حدث خطأ أثناء تحديث التحليل الموسمي');
    }
}

// دالة تحديث المقارنة التنافسية
function updateCompetitiveAnalysis(data) {
    const tbody = document.getElementById('competitiveAnalysis');
    tbody.innerHTML = data.map(item => `
        <tr>
            <td>${item.metric}</td>
            <td>${item.agency_value}</td>
            <td>${item.market_average}</td>
            <td>
                <span class="badge bg-${item.difference >= 0 ? 'success' : 'danger'}">
                    ${item.difference}%
                </span>
            </td>
        </tr>
    `).join('');
}

// دالة تحديث تحليل سلوك العملاء
async function updateCustomerAnalysis() {
    try {
        const response = await fetch('/api/reports/customer-analysis/');
        const data = await response.json();

        customerSegmentChart.data.datasets[0].data = data.segments;
        customerSegmentChart.update();

        document.getElementById('customerInsights').innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>أنماط الحجز</h6>
                    <ul class="list-unstyled">
                        ${data.booking_patterns.map(pattern =>
                            `<li class="mb-2">${pattern}</li>`
                        ).join('')}
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>توصيات التسويق</h6>
                    <ul class="list-unstyled">
                        ${data.marketing_recommendations.map(rec =>
                            `<li class="mb-2">${rec}</li>`
                        ).join('')}
                    </ul>
                </div>
            </div>
        `;
    } catch (error) {
        console.error('خطأ في تحديث تحليل العملاء:', error);
        notify.error('حدث خطأ أثناء تحديث تحليل العملاء');
    }
}

// تهيئة رسم بياني لتحليل العوامل الخارجية
const externalFactorsChart = new Chart(
    document.getElementById('externalFactorsChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'المؤشر الرئيسي',
                    data: [],
                    borderColor: '#4e73df',
                    fill: false
                },
                {
                    label: 'العامل الخارجي',
                    data: [],
                    borderColor: '#1cc88a',
                    borderDash: [5, 5],
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            return label + context.parsed.y.toFixed(2);
                        }
                    }
                }
            }
        }
    }
);

// دالة تحديث تحليل العوامل الخارجية
async function updateExternalFactorsAnalysis() {
    const factorType = document.getElementById('externalFactorType').value;
    const metric = document.getElementById('impactMetric').value;

    try {
        const response = await fetch(`/api/reports/external-factors/?type=${factorType}&metric=${metric}`);
        const data = await response.json();

        // تحديث الرسم البياني
        externalFactorsChart.data.labels = data.dates;
        externalFactorsChart.data.datasets[0].data = data.metric_values;
        externalFactorsChart.data.datasets[1].data = data.factor_values;
        externalFactorsChart.update();

        // تحديث التحليلات والتوصيات
        updateFactorInsights(data.insights);
    } catch (error) {
        console.error('خطأ في تحديث تحليل العوامل الخارجية:', error);
        notify.error('حدث خطأ أثناء تحديث التحليل');
    }
}

// دالة تحديث التحليلات والتوصيات
function updateFactorInsights(insights) {
    const container = document.querySelector('.factor-impact-list');
    container.innerHTML = insights.map(insight => `
        <div class="alert ${insight.impact > 0 ? 'alert-success' : 'alert-warning'} mb-2">
            <h6 class="alert-heading">${insight.title}</h6>
            <p class="mb-1">${insight.description}</p>
            <small>التأثير: ${insight.impact > 0 ? '+' : ''}${insight.impact}%</small>
        </div>
    `).join('');
}

// إضافة مستمعي الأحداث للتحليلات الجديدة
document.getElementById('externalFactorType').addEventListener('change', updateExternalFactorsAnalysis);
document.getElementById('impactMetric').addEventListener('change', updateExternalFactorsAnalysis);

// تحديث التحليلات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تكوين الرسم البياني للإيرادات الشهرية
    const monthlyRevenueChart = new Chart(
        document.getElementById('monthlyRevenueChart').getContext('2d'),
        {
            type: 'line',
            data: {
                labels: {{ monthly_revenue|safe }}.map(item => item.month),
                datasets: [{
                    label: 'الإيرادات',
                    data: {{ monthly_revenue|safe }}.map(item => item.total_revenue),
                    borderColor: '#4e73df',
                    tension: 0.1,
                    fill: true
                }, {
                    label: 'عدد الحجوزات',
                    data: {{ monthly_revenue|safe }}.map(item => item.booking_count),
                    borderColor: '#1cc88a',
                    tension: 0.1,
                    yAxisID: 'bookings'
                }]
            },
            options: {
                responsive: true,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    y: {
                        type: 'linear',
                        position: 'left',
                        title: {
                            display: true,
                            text: 'الإيرادات (درهم)'
                        }
                    },
                    bookings: {
                        type: 'linear',
                        position: 'right',
                        title: {
                            display: true,
                            text: 'عدد الحجوزات'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        rtl: true,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.datasetIndex === 0) {
                                    label += context.parsed.y.toFixed(2) + ' درهم';
                                } else {
                                    label += context.parsed.y + ' حجز';
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        }
    );

    // معالجة نموذج الفلترة
    document.getElementById('filterForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        const formData = new FormData(this);

        try {
            const response = await fetch('/api/reports/financial/?' + new URLSearchParams(formData));
            const data = await response.json();

            // تحديث البيانات في الرسوم البيانية
            monthlyRevenueChart.data.labels = data.monthly_revenue.map(item => item.month);
            monthlyRevenueChart.data.datasets[0].data = data.monthly_revenue.map(item => item.total_revenue);
            monthlyRevenueChart.data.datasets[1].data = data.monthly_revenue.map(item => item.booking_count);
            monthlyRevenueChart.update();

            // تحديث الملخص
            updateSummary(data.yearly_summary);
        } catch (error) {
            console.error('Error fetching data:', error);
            alert('حدث خطأ أثناء تحديث البيانات');
        }
    });
});

// دالة تصدير التقارير
function exportReport(format) {
    const filters = new FormData(document.getElementById('filterForm'));
    const queryString = new URLSearchParams(filters).toString();

    const exportUrls = {
        'pdf': `/api/reports/export/pdf/?${queryString}`,
        'excel': `/api/reports/export/excel/?${queryString}`,
        'csv': `/api/reports/export/csv/?${queryString}`
    };

    window.location.href = exportUrls[format];
}

// تهيئة جدول البيانات
$(document).ready(function() {
    $('#transactionsTable').DataTable({
        language: {
            url: "{% static 'js/dataTables.arabic.json' %}"
        },
        order: [[3, 'desc']],
        pageLength: 10,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'collection',
                text: 'تصدير',
                buttons: ['copy', 'excel', 'pdf']
            }
        ]
    });
});

function updateSummary(summary) {
    document.querySelector('[data-summary="revenue"]').textContent =
        `${summary.total_revenue.toFixed(2)} درهم`;
    document.querySelector('[data-summary="bookings"]').textContent =
        summary.total_bookings;
    document.querySelector('[data-summary="average"]').textContent =
        `${summary.average_booking_value.toFixed(2)} درهم`;
}

// تحديث المؤشرات الإضافية
function updateKPIs(data) {
    document.querySelector('[data-summary="growth"]').textContent =
        `${data.monthly_growth.toFixed(1)}%`;
    document.querySelector('[data-summary="occupancy"]').textContent =
        `${data.occupancy_rate.toFixed(1)}%`;
    document.querySelector('[data-summary="collection"]').textContent =
        `${data.collection_rate.toFixed(1)}%`;
    document.querySelector('[data-summary="duration"]').textContent =
        `${data.avg_trip_duration} يوم`;
}

// تهيئة الرسم البياني التنبؤي
const forecastChart = new Chart(
    document.getElementById('forecastChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'البيانات الفعلية',
                    data: [],
                    borderColor: '#4e73df',
                    fill: false
                },
                {
                    label: 'التنبؤ',
                    data: [],
                    borderColor: '#1cc88a',
                    borderDash: [5, 5],
                    fill: false
                },
                {
                    label: 'هامش الثقة (95%)',
                    data: [],
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    borderWidth: 0,
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    mode: 'index',
                    intersect: false
                },
                legend: {
                    rtl: true,
                    position: 'top'
                }
            }
        }
    }
);

// دالة تحديث التنبؤات
async function updateForecast(period = 3, model = 'linear') {
    try {
        const response = await fetch(`/api/reports/forecast/?period=${period}&model=${model}`);
        const data = await response.json();

        forecastChart.data.labels = [...data.actual_dates, ...data.forecast_dates];
        forecastChart.data.datasets[0].data = data.actual_values;
        forecastChart.data.datasets[1].data = data.forecast_values;
        forecastChart.data.datasets[2].data = data.confidence_interval;

        forecastChart.update();

        // تحديث مؤشرات التنبؤ
        updateForecastMetrics(data.metrics);
    } catch (error) {
        console.error('خطأ في تحديث التنبؤات:', error);
        notify.error('حدث خطأ أثناء تحديث التنبؤات');
    }
}

// دالة تحديث مؤشرات التنبؤ
function updateForecastMetrics(metrics) {
    const metricsContainer = document.querySelector('#forecastMetrics');
    metricsContainer.innerHTML = `
        <div class="row g-3">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>النمو المتوقع</h6>
                        <h3>${metrics.expected_growth}%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>دقة التنبؤ</h6>
                        <h3>${metrics.accuracy}%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>الإيرادات المتوقعة</h6>
                        <h3>${metrics.expected_revenue} درهم</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>معامل الثقة</h6>
                        <h3>${metrics.confidence_level}%</h3>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// إضافة مستمعي الأحداث
document.querySelectorAll('[data-forecast-period]').forEach(button => {
    button.addEventListener('click', (e) => {
        document.querySelector('[data-forecast-period].active').classList.remove('active');
        e.target.classList.add('active');
        updateForecast(parseInt(e.target.dataset.forecastPeriod),
                      document.getElementById('forecastModel').value);
    });
});

document.getElementById('forecastModel').addEventListener('change', (e) => {
    const activePeriod = document.querySelector('[data-forecast-period].active');
    updateForecast(parseInt(activePeriod.dataset.forecastPeriod), e.target.value);
});

// تحديث التنبؤات عند تحميل الصفحة
updateForecast();

// تهيئة الرسم البياني لتحليل الموسمية
const seasonalityChart = new Chart(
    document.getElementById('seasonalityChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'الاتجاه العام',
                    data: [],
                    borderColor: '#4e73df',
                    borderWidth: 2,
                    fill: false
                },
                {
                    label: 'البيانات الموسمية',
                    data: [],
                    borderColor: '#1cc88a',
                    borderWidth: 1,
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    mode: 'index',
                    intersect: false
                }
            }
        }
    }
);

// تحليل سلوك العملاء
const customerSegmentChart = new Chart(
    document.getElementById('customerSegmentChart').getContext('2d'),
    {
        type: 'doughnut',
        data: {
            labels: ['عملاء جدد', 'عملاء متكررون', 'عملاء موسميون'],
            datasets: [{
                data: [],
                backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    rtl: true,
                    position: 'bottom'
                }
            }
        }
    }
);

// دالة تحديث تحليل الموسمية
async function updateSeasonalityAnalysis(type = 'seasonal', metric = 'revenue') {
    try {
        const response = await fetch(`/api/reports/seasonality/?type=${type}&metric=${metric}`);
        const data = await response.json();

        seasonalityChart.data.labels = data.labels;
        seasonalityChart.data.datasets[0].data = data.trend;
        seasonalityChart.data.datasets[1].data = data.seasonal;
        seasonalityChart.update();

        updateCompetitiveAnalysis(data.market_comparison);
    } catch (error) {
        console.error('خطأ في تحديث تحليل الموسمية:', error);
        notify.error('حدث خطأ أثناء تحديث التحليل الموسمي');
    }
}

// دالة تحديث المقارنة التنافسية
function updateCompetitiveAnalysis(data) {
    const tbody = document.getElementById('competitiveAnalysis');
    tbody.innerHTML = data.map(item => `
        <tr>
            <td>${item.metric}</td>
            <td>${item.agency_value}</td>
            <td>${item.market_average}</td>
            <td>
                <span class="badge bg-${item.difference >= 0 ? 'success' : 'danger'}">
                    ${item.difference}%
                </span>
            </td>
        </tr>
    `).join('');
}

// دالة تحديث تحليل سلوك العملاء
async function updateCustomerAnalysis() {
    try {
        const response = await fetch('/api/reports/customer-analysis/');
        const data = await response.json();

        customerSegmentChart.data.datasets[0].data = data.segments;
        customerSegmentChart.update();

        document.getElementById('customerInsights').innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>أنماط الحجز</h6>
                    <ul class="list-unstyled">
                        ${data.booking_patterns.map(pattern =>
                            `<li class="mb-2">${pattern}</li>`
                        ).join('')}
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>توصيات التسويق</h6>
                    <ul class="list-unstyled">
                        ${data.marketing_recommendations.map(rec =>
                            `<li class="mb-2">${rec}</li>`
                        ).join('')}
                    </ul>
                </div>
            </div>
        `;
    } catch (error) {
        console.error('خطأ في تحديث تحليل العملاء:', error);
        notify.error('حدث خطأ أثناء تحديث تحليل العملاء');
    }
}

// تهيئة رسم بياني لتحليل العوامل الخارجية
const externalFactorsChart = new Chart(
    document.getElementById('externalFactorsChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'المؤشر الرئيسي',
                    data: [],
                    borderColor: '#4e73df',
                    fill: false
                },
                {
                    label: 'العامل الخارجي',
                    data: [],
                    borderColor: '#1cc88a',
                    borderDash: [5, 5],
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            return label + context.parsed.y.toFixed(2);
                        }
                    }
                }
            }
        }
    }
);

// دالة تحديث تحليل العوامل الخارجية
async function updateExternalFactorsAnalysis() {
    const factorType = document.getElementById('externalFactorType').value;
    const metric = document.getElementById('impactMetric').value;

    try {
        const response = await fetch(`/api/reports/external-factors/?type=${factorType}&metric=${metric}`);
        const data = await response.json();

        // تحديث الرسم البياني
        externalFactorsChart.data.labels = data.dates;
        externalFactorsChart.data.datasets[0].data = data.metric_values;
        externalFactorsChart.data.datasets[1].data = data.factor_values;
        externalFactorsChart.update();

        // تحديث التحليلات والتوصيات
        updateFactorInsights(data.insights);
    } catch (error) {
        console.error('خطأ في تحديث تحليل العوامل الخارجية:', error);
        notify.error('حدث خطأ أثناء تحديث التحليل');
    }
}

// دالة تحديث التحليلات والتوصيات
function updateFactorInsights(insights) {
    const container = document.querySelector('.factor-impact-list');
    container.innerHTML = insights.map(insight => `
        <div class="alert ${insight.impact > 0 ? 'alert-success' : 'alert-warning'} mb-2">
            <h6 class="alert-heading">${insight.title}</h6>
            <p class="mb-1">${insight.description}</p>
            <small>التأثير: ${insight.impact > 0 ? '+' : ''}${insight.impact}%</small>
        </div>
    `).join('');
}

// إضافة مستمعي الأحداث للتحليلات الجديدة
document.getElementById('externalFactorType').addEventListener('change', updateExternalFactorsAnalysis);
document.getElementById('impactMetric').addEventListener('change', updateExternalFactorsAnalysis);

// تحديث التحليلات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تكوين الرسم البياني للإيرادات الشهرية
    const monthlyRevenueChart = new Chart(
        document.getElementById('monthlyRevenueChart').getContext('2d'),
        {
            type: 'line',
            data: {
                labels: {{ monthly_revenue|safe }}.map(item => item.month),
                datasets: [{
                    label: 'الإيرادات',
                    data: {{ monthly_revenue|safe }}.map(item => item.total_revenue),
                    borderColor: '#4e73df',
                    tension: 0.1,
                    fill: true
                }, {
                    label: 'عدد الحجوزات',
                    data: {{ monthly_revenue|safe }}.map(item => item.booking_count),
                    borderColor: '#1cc88a',
                    tension: 0.1,
                    yAxisID: 'bookings'
                }]
            },
            options: {
                responsive: true,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    y: {
                        type: 'linear',
                        position: 'left',
                        title: {
                            display: true,
                            text: 'الإيرادات (درهم)'
                        }
                    },
                    bookings: {
                        type: 'linear',
                        position: 'right',
                        title: {
                            display: true,
                            text: 'عدد الحجوزات'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        rtl: true,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.datasetIndex === 0) {
                                    label += context.parsed.y.toFixed(2) + ' درهم';
                                } else {
                                    label += context.parsed.y + ' حجز';
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        }
    );

    // معالجة نموذج الفلترة
    document.getElementById('filterForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        const formData = new FormData(this);

        try {
            const response = await fetch('/api/reports/financial/?' + new URLSearchParams(formData));
            const data = await response.json();

            // تحديث البيانات في الرسوم البيانية
            monthlyRevenueChart.data.labels = data.monthly_revenue.map(item => item.month);
            monthlyRevenueChart.data.datasets[0].data = data.monthly_revenue.map(item => item.total_revenue);
            monthlyRevenueChart.data.datasets[1].data = data.monthly_revenue.map(item => item.booking_count);
            monthlyRevenueChart.update();

            // تحديث الملخص
            updateSummary(data.yearly_summary);
        } catch (error) {
            console.error('Error fetching data:', error);
            alert('حدث خطأ أثناء تحديث البيانات');
        }
    });
});

// دالة تصدير التقارير
function exportReport(format) {
    const filters = new FormData(document.getElementById('filterForm'));
    const queryString = new URLSearchParams(filters).toString();

    const exportUrls = {
        'pdf': `/api/reports/export/pdf/?${queryString}`,
        'excel': `/api/reports/export/excel/?${queryString}`,
        'csv': `/api/reports/export/csv/?${queryString}`
    };

    window.location.href = exportUrls[format];
}

// تهيئة جدول البيانات
$(document).ready(function() {
    $('#transactionsTable').DataTable({
        language: {
            url: "{% static 'js/dataTables.arabic.json' %}"
        },
        order: [[3, 'desc']],
        pageLength: 10,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'collection',
                text: 'تصدير',
                buttons: ['copy', 'excel', 'pdf']
            }
        ]
    });
});

function updateSummary(summary) {
    document.querySelector('[data-summary="revenue"]').textContent =
        `${summary.total_revenue.toFixed(2)} درهم`;
    document.querySelector('[data-summary="bookings"]').textContent =
        summary.total_bookings;
    document.querySelector('[data-summary="average"]').textContent =
        `${summary.average_booking_value.toFixed(2)} درهم`;
}

// تحديث المؤشرات الإضافية
function updateKPIs(data) {
    document.querySelector('[data-summary="growth"]').textContent =
        `${data.monthly_growth.toFixed(1)}%`;
    document.querySelector('[data-summary="occupancy"]').textContent =
        `${data.occupancy_rate.toFixed(1)}%`;
    document.querySelector('[data-summary="collection"]').textContent =
        `${data.collection_rate.toFixed(1)}%`;
    document.querySelector('[data-summary="duration"]').textContent =
        `${data.avg_trip_duration} يوم`;
}

// تهيئة الرسم البياني التنبؤي
const forecastChart = new Chart(
    document.getElementById('forecastChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'البيانات الفعلية',
                    data: [],
                    borderColor: '#4e73df',
                    fill: false
                },
                {
                    label: 'التنبؤ',
                    data: [],
                    borderColor: '#1cc88a',
                    borderDash: [5, 5],
                    fill: false
                },
                {
                    label: 'هامش الثقة (95%)',
                    data: [],
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    borderWidth: 0,
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    mode: 'index',
                    intersect: false
                },
                legend: {
                    rtl: true,
                    position: 'top'
                }
            }
        }
    }
);

// دالة تحديث التنبؤات
async function updateForecast(period = 3, model = 'linear') {
    try {
        const response = await fetch(`/api/reports/forecast/?period=${period}&model=${model}`);
        const data = await response.json();

        forecastChart.data.labels = [...data.actual_dates, ...data.forecast_dates];
        forecastChart.data.datasets[0].data = data.actual_values;
        forecastChart.data.datasets[1].data = data.forecast_values;
        forecastChart.data.datasets[2].data = data.confidence_interval;

        forecastChart.update();

        // تحديث مؤشرات التنبؤ
        updateForecastMetrics(data.metrics);
    } catch (error) {
        console.error('خطأ في تحديث التنبؤات:', error);
        notify.error('حدث خطأ أثناء تحديث التنبؤات');
    }
}

// دالة تحديث مؤشرات التنبؤ
function updateForecastMetrics(metrics) {
    const metricsContainer = document.querySelector('#forecastMetrics');
    metricsContainer.innerHTML = `
        <div class="row g-3">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>النمو المتوقع</h6>
                        <h3>${metrics.expected_growth}%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>دقة التنبؤ</h6>
                        <h3>${metrics.accuracy}%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>الإيرادات المتوقعة</h6>
                        <h3>${metrics.expected_revenue} درهم</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>معامل الثقة</h6>
                        <h3>${metrics.confidence_level}%</h3>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// إضافة مستمعي الأحداث
document.querySelectorAll('[data-forecast-period]').forEach(button => {
    button.addEventListener('click', (e) => {
        document.querySelector('[data-forecast-period].active').classList.remove('active');
        e.target.classList.add('active');
        updateForecast(parseInt(e.target.dataset.forecastPeriod),
                      document.getElementById('forecastModel').value);
    });
});

document.getElementById('forecastModel').addEventListener('change', (e) => {
    const activePeriod = document.querySelector('[data-forecast-period].active');
    updateForecast(parseInt(activePeriod.dataset.forecastPeriod), e.target.value);
});

// تحديث التنبؤات عند تحميل الصفحة
updateForecast();

// تهيئة الرسم البياني لتحليل الموسمية
const seasonalityChart = new Chart(
    document.getElementById('seasonalityChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'الاتجاه العام',
                    data: [],
                    borderColor: '#4e73df',
                    borderWidth: 2,
                    fill: false
                },
                {
                    label: 'البيانات الموسمية',
                    data: [],
                    borderColor: '#1cc88a',
                    borderWidth: 1,
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    mode: 'index',
                    intersect: false
                }
            }
        }
    }
);

// تحليل سلوك العملاء
const customerSegmentChart = new Chart(
    document.getElementById('customerSegmentChart').getContext('2d'),
    {
        type: 'doughnut',
        data: {
            labels: ['عملاء جدد', 'عملاء متكررون', 'عملاء موسميون'],
            datasets: [{
                data: [],
                backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    rtl: true,
                    position: 'bottom'
                }
            }
        }
    }
);

// دالة تحديث تحليل الموسمية
async function updateSeasonalityAnalysis(type = 'seasonal', metric = 'revenue') {
    try {
        const response = await fetch(`/api/reports/seasonality/?type=${type}&metric=${metric}`);
        const data = await response.json();

        seasonalityChart.data.labels = data.labels;
        seasonalityChart.data.datasets[0].data = data.trend;
        seasonalityChart.data.datasets[1].data = data.seasonal;
        seasonalityChart.update();

        updateCompetitiveAnalysis(data.market_comparison);
    } catch (error) {
        console.error('خطأ في تحديث تحليل الموسمية:', error);
        notify.error('حدث خطأ أثناء تحديث التحليل الموسمي');
    }
}

// دالة تحديث المقارنة التنافسية
function updateCompetitiveAnalysis(data) {
    const tbody = document.getElementById('competitiveAnalysis');
    tbody.innerHTML = data.map(item => `
        <tr>
            <td>${item.metric}</td>
            <td>${item.agency_value}</td>
            <td>${item.market_average}</td>
            <td>
                <span class="badge bg-${item.difference >= 0 ? 'success' : 'danger'}">
                    ${item.difference}%
                </span>
            </td>
        </tr>
    `).join('');
}

// دالة تحديث تحليل سلوك العملاء
async function updateCustomerAnalysis() {
    try {
        const response = await fetch('/api/reports/customer-analysis/');
        const data = await response.json();

        customerSegmentChart.data.datasets[0].data = data.segments;
        customerSegmentChart.update();

        document.getElementById('customerInsights').innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>أنماط الحجز</h6>
                    <ul class="list-unstyled">
                        ${data.booking_patterns.map(pattern =>
                            `<li class="mb-2">${pattern}</li>`
                        ).join('')}
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>توصيات التسويق</h6>
                    <ul class="list-unstyled">
                        ${data.marketing_recommendations.map(rec =>
                            `<li class="mb-2">${rec}</li>`
                        ).join('')}
                    </ul>
                </div>
            </div>
        `;
    } catch (error) {
        console.error('خطأ في تحديث تحليل العملاء:', error);
        notify.error('حدث خطأ أثناء تحديث تحليل العملاء');
    }
}

// تهيئة رسم بياني لتحليل العوامل الخارجية
const externalFactorsChart = new Chart(
    document.getElementById('externalFactorsChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'المؤشر الرئيسي',
                    data: [],
                    borderColor: '#4e73df',
                    fill: false
                },
                {
                    label: 'العامل الخارجي',
                    data: [],
                    borderColor: '#1cc88a',
                    borderDash: [5, 5],
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            return label + context.parsed.y.toFixed(2);
                        }
                    }
                }
            }
        }
    }
);

// دالة تحديث تحليل العوامل الخارجية
async function updateExternalFactorsAnalysis() {
    const factorType = document.getElementById('externalFactorType').value;
    const metric = document.getElementById('impactMetric').value;

    try {
        const response = await fetch(`/api/reports/external-factors/?type=${factorType}&metric=${metric}`);
        const data = await response.json();

        // تحديث الرسم البياني
        externalFactorsChart.data.labels = data.dates;
        externalFactorsChart.data.datasets[0].data = data.metric_values;
        externalFactorsChart.data.datasets[1].data = data.factor_values;
        externalFactorsChart.update();

        // تحديث التحليلات والتوصيات
        updateFactorInsights(data.insights);
    } catch (error) {
        console.error('خطأ في تحديث تحليل العوامل الخارجية:', error);
        notify.error('حدث خطأ أثناء تحديث التحليل');
    }
}

// دالة تحديث التحليلات والتوصيات
function updateFactorInsights(insights) {
    const container = document.querySelector('.factor-impact-list');
    container.innerHTML = insights.map(insight => `
        <div class="alert ${insight.impact > 0 ? 'alert-success' : 'alert-warning'} mb-2">
            <h6 class="alert-heading">${insight.title}</h6>
            <p class="mb-1">${insight.description}</p>
            <small>التأثير: ${insight.impact > 0 ? '+' : ''}${insight.impact}%</small>
        </div>
    `).join('');
}

// إضافة مستمعي الأحداث للتحليلات الجديدة
document.getElementById('externalFactorType').addEventListener('change', updateExternalFactorsAnalysis);
document.getElementById('impactMetric').addEventListener('change', updateExternalFactorsAnalysis);

// تحديث التحليلات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تكوين الرسم البياني للإيرادات الشهرية
    const monthlyRevenueChart = new Chart(
        document.getElementById('monthlyRevenueChart').getContext('2d'),
        {
            type: 'line',
            data: {
                labels: {{ monthly_revenue|safe }}.map(item => item.month),
                datasets: [{
                    label: 'الإيرادات',
                    data: {{ monthly_revenue|safe }}.map(item => item.total_revenue),
                    borderColor: '#4e73df',
                    tension: 0.1,
                    fill: true
                }, {
                    label: 'عدد الحجوزات',
                    data: {{ monthly_revenue|safe }}.map(item => item.booking_count),
                    borderColor: '#1cc88a',
                    tension: 0.1,
                    yAxisID: 'bookings'
                }]
            },
            options: {
                responsive: true,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    y: {
                        type: 'linear',
                        position: 'left',
                        title: {
                            display: true,
                            text: 'الإيرادات (درهم)'
                        }
                    },
                    bookings: {
                        type: 'linear',
                        position: 'right',
                        title: {
                            display: true,
                            text: 'عدد الحجوزات'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        rtl: true,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.datasetIndex === 0) {
                                    label += context.parsed.y.toFixed(2) + ' درهم';
                                } else {
                                    label += context.parsed.y + ' حجز';
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        }
    );

    // معالجة نموذج الفلترة
    document.getElementById('filterForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        const formData = new FormData(this);

        try {
            const response = await fetch('/api/reports/financial/?' + new URLSearchParams(formData));
            const data = await response.json();

            // تحديث البيانات في الرسوم البيانية
            monthlyRevenueChart.data.labels = data.monthly_revenue.map(item => item.month);
            monthlyRevenueChart.data.datasets[0].data = data.monthly_revenue.map(item => item.total_revenue);
            monthlyRevenueChart.data.datasets[1].data = data.monthly_revenue.map(item => item.booking_count);
            monthlyRevenueChart.update();

            // تحديث الملخص
            updateSummary(data.yearly_summary);
        } catch (error) {
            console.error('Error fetching data:', error);
            alert('حدث خطأ أثناء تحديث البيانات');
        }
    });
});

// دالة تصدير التقارير
function exportReport(format) {
    const filters = new FormData(document.getElementById('filterForm'));
    const queryString = new URLSearchParams(filters).toString();

    const exportUrls = {
        'pdf': `/api/reports/export/pdf/?${queryString}`,
        'excel': `/api/reports/export/excel/?${queryString}`,
        'csv': `/api/reports/export/csv/?${queryString}`
    };

    window.location.href = exportUrls[format];
}

// تهيئة جدول البيانات
$(document).ready(function() {
    $('#transactionsTable').DataTable({
        language: {
            url: "{% static 'js/dataTables.arabic.json' %}"
        },
        order: [[3, 'desc']],
        pageLength: 10,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'collection',
                text: 'تصدير',
                buttons: ['copy', 'excel', 'pdf']
            }
        ]
    });
});

function updateSummary(summary) {
    document.querySelector('[data-summary="revenue"]').textContent =
        `${summary.total_revenue.toFixed(2)} درهم`;
    document.querySelector('[data-summary="bookings"]').textContent =
        summary.total_bookings;
    document.querySelector('[data-summary="average"]').textContent =
        `${summary.average_booking_value.toFixed(2)} درهم`;
}

// تحديث المؤشرات الإضافية
function updateKPIs(data) {
    document.querySelector('[data-summary="growth"]').textContent =
        `${data.monthly_growth.toFixed(1)}%`;
    document.querySelector('[data-summary="occupancy"]').textContent =
        `${data.occupancy_rate.toFixed(1)}%`;
    document.querySelector('[data-summary="collection"]').textContent =
        `${data.collection_rate.toFixed(1)}%`;
    document.querySelector('[data-summary="duration"]').textContent =
        `${data.avg_trip_duration} يوم`;
}

// تهيئة الرسم البياني التنبؤي
const forecastChart = new Chart(
    document.getElementById('forecastChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'البيانات الفعلية',
                    data: [],
                    borderColor: '#4e73df',
                    fill: false
                },
                {
                    label: 'التنبؤ',
                    data: [],
                    borderColor: '#1cc88a',
                    borderDash: [5, 5],
                    fill: false
                },
                {
                    label: 'هامش الثقة (95%)',
                    data: [],
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    borderWidth: 0,
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    mode: 'index',
                    intersect: false
                },
                legend: {
                    rtl: true,
                    position: 'top'
                }
            }
        }
    }
);

// دالة تحديث التنبؤات
async function updateForecast(period = 3, model = 'linear') {
    try {
        const response = await fetch(`/api/reports/forecast/?period=${period}&model=${model}`);
        const data = await response.json();

        forecastChart.data.labels = [...data.actual_dates, ...data.forecast_dates];
        forecastChart.data.datasets[0].data = data.actual_values;
        forecastChart.data.datasets[1].data = data.forecast_values;
        forecastChart.data.datasets[2].data = data.confidence_interval;

        forecastChart.update();

        // تحديث مؤشرات التنبؤ
        updateForecastMetrics(data.metrics);
    } catch (error) {
        console.error('خطأ في تحديث التنبؤات:', error);
        notify.error('حدث خطأ أثناء تحديث التنبؤات');
    }
}

// دالة تحديث مؤشرات التنبؤ
function updateForecastMetrics(metrics) {
    const metricsContainer = document.querySelector('#forecastMetrics');
    metricsContainer.innerHTML = `
        <div class="row g-3">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>النمو المتوقع</h6>
                        <h3>${metrics.expected_growth}%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>دقة التنبؤ</h6>
                        <h3>${metrics.accuracy}%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>الإيرادات المتوقعة</h6>
                        <h3>${metrics.expected_revenue} درهم</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>معامل الثقة</h6>
                        <h3>${metrics.confidence_level}%</h3>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// إضافة مستمعي الأحداث
document.querySelectorAll('[data-forecast-period]').forEach(button => {
    button.addEventListener('click', (e) => {
        document.querySelector('[data-forecast-period].active').classList.remove('active');
        e.target.classList.add('active');
        updateForecast(parseInt(e.target.dataset.forecastPeriod),
                      document.getElementById('forecastModel').value);
    });
});

document.getElementById('forecastModel').addEventListener('change', (e) => {
    const activePeriod = document.querySelector('[data-forecast-period].active');
    updateForecast(parseInt(activePeriod.dataset.forecastPeriod), e.target.value);
});

// تحديث التنبؤات عند تحميل الصفحة
updateForecast();

// تهيئة الرسم البياني لتحليل الموسمية
const seasonalityChart = new Chart(
    document.getElementById('seasonalityChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'الاتجاه العام',
                    data: [],
                    borderColor: '#4e73df',
                    borderWidth: 2,
                    fill: false
                },
                {
                    label: 'البيانات الموسمية',
                    data: [],
                    borderColor: '#1cc88a',
                    borderWidth: 1,
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    mode: 'index',
                    intersect: false
                }
            }
        }
    }
);

// تحليل سلوك العملاء
const customerSegmentChart = new Chart(
    document.getElementById('customerSegmentChart').getContext('2d'),
    {
        type: 'doughnut',
        data: {
            labels: ['عملاء جدد', 'عملاء متكررون', 'عملاء موسميون'],
            datasets: [{
                data: [],
                backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    rtl: true,
                    position: 'bottom'
                }
            }
        }
    }
);

// دالة تحديث تحليل الموسمية
async function updateSeasonalityAnalysis(type = 'seasonal', metric = 'revenue') {
    try {
        const response = await fetch(`/api/reports/seasonality/?type=${type}&metric=${metric}`);
        const data = await response.json();

        seasonalityChart.data.labels = data.labels;
        seasonalityChart.data.datasets[0].data = data.trend;
        seasonalityChart.data.datasets[1].data = data.seasonal;
        seasonalityChart.update();

        updateCompetitiveAnalysis(data.market_comparison);
    } catch (error) {
        console.error('خطأ في تحديث تحليل الموسمية:', error);
        notify.error('حدث خطأ أثناء تحديث التحليل الموسمي');
    }
}

// دالة تحديث المقارنة التنافسية
function updateCompetitiveAnalysis(data) {
    const tbody = document.getElementById('competitiveAnalysis');
    tbody.innerHTML = data.map(item => `
        <tr>
            <td>${item.metric}</td>
            <td>${item.agency_value}</td>
            <td>${item.market_average}</td>
            <td>
                <span class="badge bg-${item.difference >= 0 ? 'success' : 'danger'}">
                    ${item.difference}%
                </span>
            </td>
        </tr>
    `).join('');
}

// دالة تحديث تحليل سلوك العملاء
async function updateCustomerAnalysis() {
    try {
        const response = await fetch('/api/reports/customer-analysis/');
        const data = await response.json();

        customerSegmentChart.data.datasets[0].data = data.segments;
        customerSegmentChart.update();

        document.getElementById('customerInsights').innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>أنماط الحجز</h6>
                    <ul class="list-unstyled">
                        ${data.booking_patterns.map(pattern =>
                            `<li class="mb-2">${pattern}</li>`
                        ).join('')}
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>توصيات التسويق</h6>
                    <ul class="list-unstyled">
                        ${data.marketing_recommendations.map(rec =>
                            `<li class="mb-2">${rec}</li>`
                        ).join('')}
                    </ul>
                </div>
            </div>
        `;
    } catch (error) {
        console.error('خطأ في تحديث تحليل العملاء:', error);
        notify.error('حدث خطأ أثناء تحديث تحليل العملاء');
    }
}

// تهيئة رسم بياني لتحليل العوامل الخارجية
const externalFactorsChart = new Chart(
    document.getElementById('externalFactorsChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'المؤشر الرئيسي',
                    data: [],
                    borderColor: '#4e73df',
                    fill: false
                },
                {
                    label: 'العامل الخارجي',
                    data: [],
                    borderColor: '#1cc88a',
                    borderDash: [5, 5],
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            return label + context.parsed.y.toFixed(2);
                        }
                    }
                }
            }
        }
    }
);

// دالة تحديث تحليل العوامل الخارجية
async function updateExternalFactorsAnalysis() {
    const factorType = document.getElementById('externalFactorType').value;
    const metric = document.getElementById('impactMetric').value;

    try {
        const response = await fetch(`/api/reports/external-factors/?type=${factorType}&metric=${metric}`);
        const data = await response.json();

        // تحديث الرسم البياني
        externalFactorsChart.data.labels = data.dates;
        externalFactorsChart.data.datasets[0].data = data.metric_values;
        externalFactorsChart.data.datasets[1].data = data.factor_values;
        externalFactorsChart.update();

        // تحديث التحليلات والتوصيات
        updateFactorInsights(data.insights);
    } catch (error) {
        console.error('خطأ في تحديث تحليل العوامل الخارجية:', error);
        notify.error('حدث خطأ أثناء تحديث التحليل');
    }
}

// دالة تحديث التحليلات والتوصيات
function updateFactorInsights(insights) {
    const container = document.querySelector('.factor-impact-list');
    container.innerHTML = insights.map(insight => `
        <div class="alert ${insight.impact > 0 ? 'alert-success' : 'alert-warning'} mb-2">
            <h6 class="alert-heading">${insight.title}</h6>
            <p class="mb-1">${insight.description}</p>
            <small>التأثير: ${insight.impact > 0 ? '+' : ''}${insight.impact}%</small>
        </div>
    `).join('');
}

// إضافة مستمعي الأحداث للتحليلات الجديدة
document.getElementById('externalFactorType').addEventListener('change', updateExternalFactorsAnalysis);
document.getElementById('impactMetric').addEventListener('change', updateExternalFactorsAnalysis);

// تحديث التحليلات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تكوين الرسم البياني للإيرادات الشهرية
    const monthlyRevenueChart = new Chart(
        document.getElementById('monthlyRevenueChart').getContext('2d'),
        {
            type: 'line',
            data: {
                labels: {{ monthly_revenue|safe }}.map(item => item.month),
                datasets: [{
                    label: 'الإيرادات',
                    data: {{ monthly_revenue|safe }}.map(item => item.total_revenue),
                    borderColor: '#4e73df',
                    tension: 0.1,
                    fill: true
                }, {
                    label: 'عدد الحجوزات',
                    data: {{ monthly_revenue|safe }}.map(item => item.booking_count),
                    borderColor: '#1cc88a',
                    tension: 0.1,
                    yAxisID: 'bookings'
                }]
            },
            options: {
                responsive: true,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    y: {
                        type: 'linear',
                        position: 'left',
                        title: {
                            display: true,
                            text: 'الإيرادات (درهم)'
                        }
                    },
                    bookings: {
                        type: 'linear',
                        position: 'right',
                        title: {
                            display: true,
                            text: 'عدد الحجوزات'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        rtl: true,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.datasetIndex === 0) {
                                    label += context.parsed.y.toFixed(2) + ' درهم';
                                } else {
                                    label += context.parsed.y + ' حجز';
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        }
    );

    // معالجة نموذج الفلترة
    document.getElementById('filterForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        const formData = new FormData(this);

        try {
            const response = await fetch('/api/reports/financial/?' + new URLSearchParams(formData));
            const data = await response.json();

            // تحديث البيانات في الرسوم البيانية
            monthlyRevenueChart.data.labels = data.monthly_revenue.map(item => item.month);
            monthlyRevenueChart.data.datasets[0].data = data.monthly_revenue.map(item => item.total_revenue);
            monthlyRevenueChart.data.datasets[1].data = data.monthly_revenue.map(item => item.booking_count);
            monthlyRevenueChart.update();

            // تحديث الملخص
            updateSummary(data.yearly_summary);
        } catch (error) {
            console.error('Error fetching data:', error);
            alert('حدث خطأ أثناء تحديث البيانات');
        }
    });
});

// دالة تصدير التقارير
function exportReport(format) {
    const filters = new FormData(document.getElementById('filterForm'));
    const queryString = new URLSearchParams(filters).toString();

    const exportUrls = {
        'pdf': `/api/reports/export/pdf/?${queryString}`,
        'excel': `/api/reports/export/excel/?${queryString}`,
        'csv': `/api/reports/export/csv/?${queryString}`
    };

    window.location.href = exportUrls[format];
}

// تهيئة جدول البيانات
$(document).ready(function() {
    $('#transactionsTable').DataTable({
        language: {
            url: "{% static 'js/dataTables.arabic.json' %}"
        },
        order: [[3, 'desc']],
        pageLength: 10,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'collection',
                text: 'تصدير',
                buttons: ['copy', 'excel', 'pdf']
            }
        ]
    });
});

function updateSummary(summary) {
    document.querySelector('[data-summary="revenue"]').textContent =
        `${summary.total_revenue.toFixed(2)} درهم`;
    document.querySelector('[data-summary="bookings"]').textContent =
        summary.total_bookings;
    document.querySelector('[data-summary="average"]').textContent =
        `${summary.average_booking_value.toFixed(2)} درهم`;
}

// تحديث المؤشرات الإضافية
function updateKPIs(data) {
    document.querySelector('[data-summary="growth"]').textContent =
        `${data.monthly_growth.toFixed(1)}%`;
    document.querySelector('[data-summary="occupancy"]').textContent =
        `${data.occupancy_rate.toFixed(1)}%`;
    document.querySelector('[data-summary="collection"]').textContent =
        `${data.collection_rate.toFixed(1)}%`;
    document.querySelector('[data-summary="duration"]').textContent =
        `${data.avg_trip_duration} يوم`;
}

// تهيئة الرسم البياني التنبؤي
const forecastChart = new Chart(
    document.getElementById('forecastChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'البيانات الفعلية',
                    data: [],
                    borderColor: '#4e73df',
                    fill: false
                },
                {
                    label: 'التنبؤ',
                    data: [],
                    borderColor: '#1cc88a',
                    borderDash: [5, 5],
                    fill: false
                },
                {
                    label: 'هامش الثقة (95%)',
                    data: [],
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    borderWidth: 0,
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    mode: 'index',
                    intersect: false
                },
                legend: {
                    rtl: true,
                    position: 'top'
                }
            }
        }
    }
);

// دالة تحديث التنبؤات
async function updateForecast(period = 3, model = 'linear') {
    try {
        const response = await fetch(`/api/reports/forecast/?period=${period}&model=${model}`);
        const data = await response.json();

        forecastChart.data.labels = [...data.actual_dates, ...data.forecast_dates];
        forecastChart.data.datasets[0].data = data.actual_values;
        forecastChart.data.datasets[1].data = data.forecast_values;
        forecastChart.data.datasets[2].data = data.confidence_interval;

        forecastChart.update();

        // تحديث مؤشرات التنبؤ
        updateForecastMetrics(data.metrics);
    } catch (error) {
        console.error('خطأ في تحديث التنبؤات:', error);
        notify.error('حدث خطأ أثناء تحديث التنبؤات');
    }
}

// دالة تحديث مؤشرات التنبؤ
function updateForecastMetrics(metrics) {
    const metricsContainer = document.querySelector('#forecastMetrics');
    metricsContainer.innerHTML = `
        <div class="row g-3">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>النمو المتوقع</h6>
                        <h3>${metrics.expected_growth}%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>دقة التنبؤ</h6>
                        <h3>${metrics.accuracy}%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>الإيرادات المتوقعة</h6>
                        <h3>${metrics.expected_revenue} درهم</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6>معامل الثقة</h6>
                        <h3>${metrics.confidence_level}%</h3>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// إضافة مستمعي الأحداث
document.querySelectorAll('[data-forecast-period]').forEach(button => {
    button.addEventListener('click', (e) => {
        document.querySelector('[data-forecast-period].active').classList.remove('active');
        e.target.classList.add('active');
        updateForecast(parseInt(e.target.dataset.forecastPeriod),
                      document.getElementById('forecastModel').value);
    });
});

document.getElementById('forecastModel').addEventListener('change', (e) => {
    const activePeriod = document.querySelector('[data-forecast-period].active');
    updateForecast(parseInt(activePeriod.dataset.forecastPeriod), e.target.value);
});

// تحديث التنبؤات عند تحميل الصفحة
updateForecast();

// تهيئة الرسم البياني لتحليل الموسمية
const seasonalityChart = new Chart(
    document.getElementById('seasonalityChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'الاتجاه العام',
                    data: [],
                    borderColor: '#4e73df',
                    borderWidth: 2,
                    fill: false
                },
                {
                    label: 'البيانات الموسمية',
                    data: [],
                    borderColor: '#1cc88a',
                    borderWidth: 1,
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    mode: 'index',
                    intersect: false
                }
            }
        }
    }
);

// تحليل سلوك العملاء
const customerSegmentChart = new Chart(
    document.getElementById('customerSegmentChart').getContext('2d'),
    {
        type: 'doughnut',
        data: {
            labels: ['عملاء جدد', 'عملاء متكررون', 'عملاء موسميون'],
            datasets: [{
                data: [],
                backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    rtl: true,
                    position: 'bottom'
                }
            }
        }
    }
);

// دالة تحديث تحليل الموسمية
async function updateSeasonalityAnalysis(type = 'seasonal', metric = 'revenue') {
    try {
        const response = await fetch(`/api/reports/seasonality/?type=${type}&metric=${metric}`);
        const data = await response.json();

        seasonalityChart.data.labels = data.labels;
        seasonalityChart.data.datasets[0].data = data.trend;
        seasonalityChart.data.datasets[1].data = data.seasonal;
        seasonalityChart.update();

        updateCompetitiveAnalysis(data.market_comparison);
    } catch (error) {
        console.error('خطأ في تحديث تحليل الموسمية:', error);
        notify.error('حدث خطأ أثناء تحديث التحليل الموسمي');
    }
}

// دالة تحديث المقارنة التنافسية
function updateCompetitiveAnalysis(data) {
    const tbody = document.getElementById('competitiveAnalysis');
    tbody.innerHTML = data.map(item => `
        <tr>
            <td>${item.metric}</td>
            <td>${item.agency_value}</td>
            <td>${item.market_average}</td>
            <td>
                <span class="badge bg-${item.difference >= 0 ? 'success' : 'danger'}">
                    ${item.difference}%
                </span>
            </td>
        </tr>
    `).join('');
}

// دالة تحديث تحليل سلوك العملاء
async function updateCustomerAnalysis() {
    try {
        const response = await fetch('/api/reports/customer-analysis/');
        const data = await response.json();

        customerSegmentChart.data.datasets[0].data = data.segments;
        customerSegmentChart.update();

        document.getElementById('customerInsights').innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>أنماط الحجز</h6>
                    <ul class="list-unstyled">
                        ${data.booking_patterns.map(pattern =>
                            `<li class="mb-2">${pattern}</li>`
                        ).join('')}
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>توصيات التسويق</h6>
                    <ul class="list-unstyled">
                        ${data.marketing_recommendations.map(rec =>
                            `<li class="mb-2">${rec}</li>`
                        ).join('')}
                    </ul>
                </div>
            </div>
        `;
    } catch (error) {
        console.error('خطأ في تحديث تحليل العملاء:', error);
        notify.error('حدث خطأ أثناء تحديث تحليل العملاء');
    }
}

// تهيئة رسم بياني لتحليل العوامل الخارجية
const externalFactorsChart = new Chart(
    document.getElementById('externalFactorsChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'المؤشر الرئيسي',
                    data: [],
                    borderColor: '#4e73df',
                    fill: false
                },
                {
                    label: 'العامل الخارجي',
                    data: [],
                    borderColor: '#1cc88a',
                    borderDash: [5, 5],
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            return label + context.parsed.y.toFixed(2);
                        }
                    }
                }
            }
        }
    }
);

// دالة تحديث تحليل العوامل الخارجية
async function updateExternalFactorsAnalysis() {
    const factorType = document.getElementById('externalFactorType').value;
    const metric = document.getElementById('impactMetric').value;

    try {
        const response = await fetch(`/api/reports/external-factors/?type=${factorType}&metric=${metric}`);
        const data = await response.json();

        // تحديث الرسم البياني
        externalFactorsChart.data.labels = data.dates;
        externalFactorsChart.data.datasets[0].data = data.metric_values;
        externalFactorsChart.data.datasets[1].data = data.factor_values;
        externalFactorsChart.update();

        // تحديث التحليلات والتوصيات
        updateFactorInsights(data.insights);
    } catch (error) {
        console.error('خطأ في تحديث تحليل العوامل الخارجية:', error);
        notify.error('حدث خطأ أثناء تحديث التحليل');
    }
}

// دالة تحديث التحليلات والتوصيات
function updateFactorInsights(insights) {
    const container = document.querySelector('.factor-impact-list');
    container.innerHTML = insights.map(insight => `
        <div class="alert ${insight.impact > 0 ? 'alert-success' : 'alert-warning'} mb-2">
            <h6 class="alert-heading">${insight.title}</h6>
            <p class="mb-1">${insight.description}</p>
            <small>التأثير: ${insight.impact > 0 ? '+' : ''}${insight.impact}%</small>
        </div>
    `).join('');
}

// إضافة مستمعي الأحداث للتحليلات الجديدة
document.getElementById('externalFactorType').addEventListener('change', updateExternalFactorsAnalysis);
document.getElementById('impactMetric').addEventListener('change', updateExternalFactorsAnalysis);

// تحديث التحليلات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تكوين الرسم البياني للإيرادات الشهرية
    const monthlyRevenueChart = new Chart(
        document.getElementById('monthlyRevenueChart').getContext('2d'),
        {
            type: 'line',
            data: {
                labels: {{ monthly_revenue|safe }}.map(item => item.month),
                datasets: [{
                    label: 'الإيرادات',
                    data: {{ monthly_revenue|safe }}.map(item => item.total_revenue),
                    borderColor: '#4e73df',
                    tension: 0.1,
                    fill: true
                }, {
                    label: 'عدد الحجوزات',
                    data: {{ monthly_revenue|safe }}.map(item => item.booking_count),
                    borderColor: '#1cc88a',
                    tension: 0.1,
                    yAxisID: 'bookings'
                }]
            },
            options: {
                responsive: true,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    y: {
                        type: 'linear',
                        position: 'left',
                        title: {
                            display: true,
                            text: 'الإيرادات (درهم)'
                        }
                    },
                    bookings: {
                        type: 'linear',
                        position: 'right',
                        title: {
                            display: true,
                            text: 'عدد الحجوزات'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        rtl: true,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.datasetIndex === 0) {
                                    label += context.parsed.y.toFixed(2) + ' درهم';
                                } else {
                                    label += context.parsed.y + ' حجز';
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        }
    );

    // معالجة نموذج الفلترة
    document.getElementById('filterForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        const formData = new FormData(this);

        try {
            const response = await fetch('/api/reports/financial/?' + new URLSearchParams(formData));
            const data = await response.json();

            // تحديث البيانات في الرسوم البيانية
            monthlyRevenueChart.data.labels = data.monthly_revenue.map(item => item.month);
            monthlyRevenueChart.data.datasets[0].data = data.monthly_revenue.map(item => item.total_revenue);
            monthlyRevenueChart.data.datasets[1].data = data.monthly_revenue.map(item => item.booking_count);
            monthlyRevenueChart.update();

            // تحديث الملخص
            updateSummary(data.yearly_summary);
        } catch (error) {
            console.error('Error fetching data:', error);
            alert('حدث خطأ أثناء تحديث البيانات');
        }
    });
});

// دالة تصدير التقارير
function exportReport(format) {
    const filters = new FormData(document.getElementById('filterForm'));
    const queryString = new URLSearchParams(filters).toString();

    const exportUrls = {
        'pdf': `/api/reports/export/pdf/?${queryString}`,
        'excel': `/api/reports/export/excel/?${queryString}`,
        'csv': `/api/reports/export/csv/?${queryString}`
    };

    window.location.href = exportUrls[format];
}

// تهيئة جدول البيانات
$(document).ready(function() {
    $('#transactionsTable').DataTable({
        language: {
            url: "{% static 'js/dataTables.arabic.json' %}"
        },
        order: [[3, 'desc']],
        pageLength: 10,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'collection',
                text: 'تصدير',
                buttons: ['copy', 'excel', 'pdf']
            }
        ]
    });
});

function updateSummary(summary) {
    document.querySelector('[data-summary="revenue"]').textContent =
        `${summary.total_revenue.toFixed(2)} درهم`;
    document.querySelector('[data-summary="bookings"]').textContent =
        summary.total_bookings;
    document.querySelector('[data-summary="average"]').textContent =
        `${summary.average_booking_value.toFixed(2)} درهم`;
}

// تحديث المؤشرات الإضافية
function updateKPIs(data) {
    document.querySelector('[data-summary="growth"]').textContent =
        `${data.monthly_growth.toFixed(1)}%`;
    document.querySelector('[data-summary="occupancy"]').textContent =
        `${data.occupancy_rate.toFixed(1)}%`;
    document.querySelector('[data-summary="collection"]').textContent =
        `${data.collection_rate.toFixed(1)}%`;
    document.querySelector('[data-summary="duration"]').textContent =
        `${data.avg_trip_duration} يوم`;
}

// تهيئة الرسم البياني التنبؤي
const forecastChart = new Chart(
    document.getElementById('forecastChart').getContext('2d'),
    {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'البيانات الفعلية',
                    data: [],
                    borderColor: '#4e73df',
                    fill: false
                },
                {
                    label: 'التنبؤ',
                    data: [],
                    borderColor: '#1cc88a',
                    borderDash: [5, 5],
                    fill: false
                },
                {
                    label: 'هامش الثقة (95%)',
                    data: [],
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    borderWidth: 0,
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: {
                    rtl: true,
                    mode: 'index',
                    intersect: false
                },
                legend: {
                    rtl: true,
                    position: 'top'
                }
            }
        }
    }
);
