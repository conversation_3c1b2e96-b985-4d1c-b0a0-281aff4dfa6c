from allauth.socialaccount.tests import OAuth2TestsMixin
from allauth.tests import MockedResponse, TestCase

from .provider import YahooProvider


class YahooTests(OAuth2TestsMixin, TestCase):
    provider_id = YahooProvider.id

    def get_mocked_response(self):
        response_data = """
        {
         "sub": "FSVIDUW3D7FSVIDUW3D72F2F",
         "name": "<PERSON>",
         "given_name": "<PERSON>",
         "family_name": "<PERSON><PERSON>",
         "preferred_username": "j.doe",
         "email": "<EMAIL>",
         "email_verified": true,
         "picture": "http://example.com/janedoe/me.jpg"
        }
        """  # noqa
        return MockedResponse(200, response_data)
