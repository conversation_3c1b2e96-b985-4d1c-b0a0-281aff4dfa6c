{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}تفاصيل الموظف - {{ employee.get_full_name }}{% endblock %}

{% block extra_css %}
<style>
.employee-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
}

.employee-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.employee-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
    margin: 0 auto 20px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-label {
    font-weight: 500;
    color: #6c757d;
}

.info-value {
    color: #495057;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.status-active {
    background: #d1edff;
    color: #0c63e4;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

.attendance-chart {
    height: 200px;
}

.leave-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid #667eea;
}

.leave-item.approved {
    border-left-color: #38ef7d;
}

.leave-item.pending {
    border-left-color: #fdbb2d;
}

.leave-item.rejected {
    border-left-color: #ff6b6b;
}

.btn-action {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.metric-card {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.metric-label {
    color: #6c757d;
    font-size: 0.9rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Employee Header -->
    <div class="employee-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <div class="employee-avatar me-4">
                        {{ employee.first_name|first }}{{ employee.last_name|first }}
                    </div>
                    <div>
                        <h1 class="h2 mb-2">{{ employee.get_full_name }}</h1>
                        <p class="mb-1 opacity-75">
                            <i class="fas fa-briefcase me-2"></i>{{ employee.position.title_ar|default:"غير محدد" }}
                        </p>
                        <p class="mb-0 opacity-75">
                            <i class="fas fa-building me-2"></i>{{ employee.department.name_ar|default:"غير محدد" }}
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <span class="status-badge status-{{ employee.is_active|yesno:'active,inactive' }}">
                    {{ employee.is_active|yesno:'نشط,غير نشط' }}
                </span>
                <br>
                <small class="opacity-75 mt-2 d-block">
                    <i class="fas fa-id-badge me-1"></i>{{ employee.employee_id|default:"غير محدد" }}
                </small>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Employee Information -->
        <div class="col-lg-8">
            <!-- Personal Information -->
            <div class="employee-card">
                <h5 class="mb-4"><i class="fas fa-user me-2"></i>المعلومات الشخصية</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-item">
                            <span class="info-label">الاسم الكامل</span>
                            <span class="info-value">{{ employee.get_full_name }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">البريد الإلكتروني</span>
                            <span class="info-value">{{ employee.email }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">رقم الهاتف</span>
                            <span class="info-value">{{ employee.phone|default:"غير محدد" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">تاريخ الميلاد</span>
                            <span class="info-value">{{ employee.date_of_birth|default:"غير محدد" }}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item">
                            <span class="info-label">الجنسية</span>
                            <span class="info-value">{{ employee.nationality|default:"غير محدد" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الحالة الاجتماعية</span>
                            <span class="info-value">{{ employee.marital_status|default:"غير محدد" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">العنوان</span>
                            <span class="info-value">{{ employee.address|default:"غير محدد" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">رقم الهوية</span>
                            <span class="info-value">{{ employee.national_id|default:"غير محدد" }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Employment Information -->
            <div class="employee-card">
                <h5 class="mb-4"><i class="fas fa-briefcase me-2"></i>معلومات العمل</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-item">
                            <span class="info-label">رقم الموظف</span>
                            <span class="info-value">{{ employee.employee_id|default:"غير محدد" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">القسم</span>
                            <span class="info-value">{{ employee.department.name_ar|default:"غير محدد" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">المنصب</span>
                            <span class="info-value">{{ employee.position.title_ar|default:"غير محدد" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">تاريخ التوظيف</span>
                            <span class="info-value">{{ employee.date_joined }}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item">
                            <span class="info-label">الراتب الأساسي</span>
                            <span class="info-value">{{ employee.salary|default:"غير محدد" }} درهم</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">نوع العقد</span>
                            <span class="info-value">{{ employee.contract_type|default:"غير محدد" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الجدول الزمني</span>
                            <span class="info-value">{{ employee.schedule.name|default:"غير محدد" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">المدير المباشر</span>
                            <span class="info-value">{{ employee.manager.get_full_name|default:"غير محدد" }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Leaves -->
            <div class="employee-card">
                <h5 class="mb-4"><i class="fas fa-calendar-times me-2"></i>الإجازات الأخيرة</h5>
                {% if recent_leaves %}
                <div class="row">
                    {% for leave in recent_leaves %}
                    <div class="col-md-6">
                        <div class="leave-item {{ leave.status }}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ leave.get_leave_type_display }}</h6>
                                    <p class="mb-1 text-muted">{{ leave.start_date }} - {{ leave.end_date }}</p>
                                    <small class="text-muted">{{ leave.days_requested }} يوم</small>
                                </div>
                                <span class="badge bg-{{ leave.status|yesno:'success,warning,danger' }}">
                                    {{ leave.get_status_display }}
                                </span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-calendar-check fa-3x mb-3"></i>
                    <p>لا توجد إجازات مسجلة</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Stats -->
            <div class="metric-card">
                <div class="metric-value">{{ attendance_stats.attendance_rate|floatformat:1 }}%</div>
                <div class="metric-label">معدل الحضور</div>
            </div>

            <div class="metric-card">
                <div class="metric-value">{{ leave_stats.total_leaves }}</div>
                <div class="metric-label">إجمالي الإجازات</div>
            </div>

            <div class="metric-card">
                <div class="metric-value">{{ leave_stats.remaining_leaves }}</div>
                <div class="metric-label">الإجازات المتبقية</div>
            </div>

            <!-- Actions -->
            <div class="employee-card">
                <h6 class="mb-3"><i class="fas fa-cogs me-2"></i>الإجراءات</h6>
                <div class="d-grid gap-2">
                    <button class="btn btn-action">
                        <i class="fas fa-edit me-2"></i>تعديل البيانات
                    </button>
                    <button class="btn btn-outline-primary">
                        <i class="fas fa-calendar-plus me-2"></i>طلب إجازة
                    </button>
                    <button class="btn btn-outline-info">
                        <i class="fas fa-clock me-2"></i>سجل الحضور
                    </button>
                    <button class="btn btn-outline-success">
                        <i class="fas fa-money-bill me-2"></i>كشف الراتب
                    </button>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="employee-card">
                <h6 class="mb-3"><i class="fas fa-address-book me-2"></i>معلومات الاتصال</h6>
                <div class="info-item">
                    <span class="info-label">البريد</span>
                    <span class="info-value">{{ employee.email }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الهاتف</span>
                    <span class="info-value">{{ employee.phone|default:"غير محدد" }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الطوارئ</span>
                    <span class="info-value">{{ employee.emergency_contact|default:"غير محدد" }}</span>
                </div>
            </div>

            <!-- Performance -->
            <div class="employee-card">
                <h6 class="mb-3"><i class="fas fa-chart-line me-2"></i>الأداء</h6>
                <div class="text-center">
                    <canvas id="performanceChart" width="200" height="200"></canvas>
                </div>
                <div class="mt-3">
                    <div class="d-flex justify-content-between mb-2">
                        <span>التقييم العام:</span>
                        <strong>{{ employee.performance_rating|default:"غير مقيم" }}</strong>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>آخر تقييم:</span>
                        <span>{{ employee.last_evaluation_date|default:"لم يتم" }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Performance Chart
const performanceCtx = document.getElementById('performanceChart').getContext('2d');
const performanceChart = new Chart(performanceCtx, {
    type: 'doughnut',
    data: {
        labels: ['الحضور', 'الأداء', 'التعاون', 'الالتزام'],
        datasets: [{
            data: [{{ attendance_stats.attendance_rate|default:0 }}, 85, 90, 88],
            backgroundColor: [
                '#667eea',
                '#38ef7d',
                '#fdbb2d',
                '#ff6b6b'
            ],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    fontSize: 12
                }
            }
        }
    }
});
</script>
{% endblock %}
