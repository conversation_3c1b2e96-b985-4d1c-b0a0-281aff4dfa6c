# Generated by Django 2.0.1 on 2018-02-10 12:26
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('django_celery_beat', '0005_add_solarschedule_events_choices'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='crontabschedule',
            name='day_of_month',
            field=models.CharField(default='*', max_length=124,
                                   verbose_name='day of month'),
        ),
        migrations.AlterField(
            model_name='crontabschedule',
            name='hour',
            field=models.CharField(default='*', max_length=96,
                                   verbose_name='hour'),
        ),
        migrations.AlterField(
            model_name='crontabschedule',
            name='minute',
            field=models.CharField(default='*', max_length=240,
                                   verbose_name='minute'),
        ),
    ]
