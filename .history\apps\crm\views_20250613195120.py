"""
Views for CRM app.
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count, Avg, Sum
from django.utils import timezone
from django.http import JsonResponse
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.urls import reverse_lazy
from .models import Client
from .forms import ClientForm, ClientSearchForm


@login_required
def client_list(request):
    """List all clients with search and filtering."""
    clients = Client.objects.all().select_related('nationality')

    # Search functionality
    search_form = ClientSearchForm(request.GET)
    if search_form.is_valid():
        search_query = search_form.cleaned_data.get('search')
        client_type = search_form.cleaned_data.get('client_type')
        nationality = search_form.cleaned_data.get('nationality')
        vip_status = search_form.cleaned_data.get('vip_status')

        if search_query:
            clients = clients.filter(
                Q(first_name_ar__icontains=search_query) |
                Q(last_name_ar__icontains=search_query) |
                Q(first_name_en__icontains=search_query) |
                Q(last_name_en__icontains=search_query) |
                Q(email__icontains=search_query) |
                Q(phone__icontains=search_query)
            )

        if client_type:
            clients = clients.filter(client_type=client_type)

        if nationality:
            clients = clients.filter(nationality=nationality)

        if vip_status is not None:
            clients = clients.filter(vip_status=vip_status)

    # Pagination
    paginator = Paginator(clients, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'total_clients': clients.count(),
    }

    return render(request, 'crm/client_list.html', context)


@login_required
def client_detail(request, pk):
    """Display client details."""
    client = get_object_or_404(Client, pk=pk)

    # Get client statistics
    stats = {
        'total_reservations': client.reservations.count(),
        'confirmed_reservations': client.reservations.filter(status='confirmed').count(),
        'total_spent': client.reservations.filter(status='confirmed').aggregate(
            total=models.Sum('total_amount')
        )['total'] or 0,
        'last_reservation': client.reservations.order_by('-booking_date').first(),
    }

    # Recent reservations
    recent_reservations = client.reservations.order_by('-booking_date')[:5]

    context = {
        'client': client,
        'stats': stats,
        'recent_reservations': recent_reservations,
    }

    return render(request, 'crm/client_detail.html', context)


@login_required
def client_create(request):
    """Create new client."""
    if request.method == 'POST':
        form = ClientForm(request.POST)
        if form.is_valid():
            client = form.save(commit=False)
            client.created_by = request.user
            client.save()
            messages.success(request, f'تم إنشاء العميل {client.full_name_ar} بنجاح')
            return redirect('crm:client_detail', pk=client.pk)
    else:
        form = ClientForm()

    context = {
        'form': form,
        'title': 'إضافة عميل جديد',
    }

    return render(request, 'crm/client_form.html', context)


@login_required
def client_update(request, pk):
    """Update client information."""
    client = get_object_or_404(Client, pk=pk)

    if request.method == 'POST':
        form = ClientForm(request.POST, instance=client)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث بيانات العميل {client.full_name_ar} بنجاح')
            return redirect('crm:client_detail', pk=client.pk)
    else:
        form = ClientForm(instance=client)

    context = {
        'form': form,
        'client': client,
        'title': f'تعديل بيانات {client.full_name_ar}',
    }

    return render(request, 'crm/client_form.html', context)


@login_required
def client_delete(request, pk):
    """Delete client."""
    client = get_object_or_404(Client, pk=pk)

    if request.method == 'POST':
        client_name = client.full_name_ar
        client.delete()
        messages.success(request, f'تم حذف العميل {client_name} بنجاح')
        return redirect('crm:client_list')

    context = {
        'client': client,
    }

    return render(request, 'crm/client_confirm_delete.html', context)


@login_required
def dashboard(request):
    """CRM Dashboard."""
    # Client statistics
    total_clients = Client.objects.count()
    new_clients_this_month = Client.objects.filter(
        created_at__month=timezone.now().month,
        created_at__year=timezone.now().year
    ).count()
    vip_clients = Client.objects.filter(vip_status=True).count()

    # Client types breakdown
    client_types = Client.objects.values('client_type').annotate(
        count=Count('id')
    ).order_by('-count')

    # Top nationalities
    top_nationalities = Client.objects.values(
        'nationality__name_ar'
    ).annotate(
        count=Count('id')
    ).order_by('-count')[:5]

    # Recent clients
    recent_clients = Client.objects.order_by('-created_at')[:10]

    # Monthly client registration trend
    from django.db.models import TruncMonth
    monthly_registrations = Client.objects.annotate(
        month=TruncMonth('created_at')
    ).values('month').annotate(
        count=Count('id')
    ).order_by('month')

    context = {
        'total_clients': total_clients,
        'new_clients_this_month': new_clients_this_month,
        'vip_clients': vip_clients,
        'client_types': client_types,
        'top_nationalities': top_nationalities,
        'recent_clients': recent_clients,
        'monthly_registrations': monthly_registrations,
    }

    return render(request, 'crm/dashboard.html', context)


@login_required
def client_search_ajax(request):
    """AJAX search for clients."""
    query = request.GET.get('q', '')

    if len(query) < 2:
        return JsonResponse({'results': []})

    clients = Client.objects.filter(
        Q(first_name_ar__icontains=query) |
        Q(last_name_ar__icontains=query) |
        Q(first_name_en__icontains=query) |
        Q(last_name_en__icontains=query) |
        Q(email__icontains=query) |
        Q(phone__icontains=query)
    )[:10]

    results = []
    for client in clients:
        results.append({
            'id': client.id,
            'text': f"{client.full_name_ar} - {client.email}",
            'email': client.email,
            'phone': client.phone,
        })

    return JsonResponse({'results': results})


@login_required
def client_toggle_vip(request, pk):
    """Toggle client VIP status."""
    if request.method == 'POST':
        client = get_object_or_404(Client, pk=pk)
        client.vip_status = not client.vip_status
        client.save()

        status = 'VIP' if client.vip_status else 'عادي'
        messages.success(request, f'تم تغيير حالة العميل {client.full_name_ar} إلى {status}')

        return JsonResponse({
            'success': True,
            'vip_status': client.vip_status,
            'message': f'تم تغيير حالة العميل إلى {status}'
        })

    return JsonResponse({'success': False})


# Class-based views
class ClientListView(ListView):
    """List view for clients."""
    model = Client
    template_name = 'crm/client_list.html'
    context_object_name = 'clients'
    paginate_by = 20

    def get_queryset(self):
        return Client.objects.select_related('nationality').order_by('-created_at')


class ClientDetailView(DetailView):
    """Detail view for client."""
    model = Client
    template_name = 'crm/client_detail.html'
    context_object_name = 'client'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        client = self.object

        # Add statistics
        context['stats'] = {
            'total_reservations': client.reservations.count(),
            'confirmed_reservations': client.reservations.filter(status='confirmed').count(),
            'last_reservation': client.reservations.order_by('-booking_date').first(),
        }

        # Recent reservations
        context['recent_reservations'] = client.reservations.order_by('-booking_date')[:5]

        return context


class ClientCreateView(CreateView):
    """Create view for client."""
    model = Client
    form_class = ClientForm
    template_name = 'crm/client_form.html'
    success_url = reverse_lazy('crm:client_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(self.request, f'تم إنشاء العميل {form.instance.full_name_ar} بنجاح')
        return super().form_valid(form)


class ClientUpdateView(UpdateView):
    """Update view for client."""
    model = Client
    form_class = ClientForm
    template_name = 'crm/client_form.html'

    def get_success_url(self):
        return reverse_lazy('crm:client_detail', kwargs={'pk': self.object.pk})

    def form_valid(self, form):
        messages.success(self.request, f'تم تحديث بيانات العميل {form.instance.full_name_ar} بنجاح')
        return super().form_valid(form)
