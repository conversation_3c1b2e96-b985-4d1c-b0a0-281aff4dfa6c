"""
API views for Finance app.
"""
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Sum, Count, Q
from django.utils import timezone
from datetime import datetime, timedelta
from .models import Invoice, Payment, Expense, BankAccount
from .serializers import (
    InvoiceSerializer, PaymentSerializer, ExpenseSerializer,
    BankAccountSerializer, FinancialSummarySerializer
)


class InvoiceViewSet(viewsets.ModelViewSet):
    """API viewset for invoices."""

    queryset = Invoice.objects.all()
    serializer_class = InvoiceSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = Invoice.objects.select_related('client', 'reservation').order_by('-issue_date')

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by client
        client_id = self.request.query_params.get('client')
        if client_id:
            queryset = queryset.filter(client_id=client_id)

        # Filter by date range
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')
        if date_from:
            queryset = queryset.filter(issue_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(issue_date__lte=date_to)

        # Search
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(invoice_number__icontains=search) |
                Q(client__first_name_ar__icontains=search) |
                Q(client__last_name_ar__icontains=search)
            )

        return queryset

    @action(detail=True, methods=['post'])
    def send_invoice(self, request, pk=None):
        """Send invoice to client."""
        invoice = self.get_object()
        if invoice.status == 'draft':
            invoice.status = 'sent'
            invoice.save()
            return Response({'message': 'تم إرسال الفاتورة بنجاح'})
        return Response(
            {'error': 'لا يمكن إرسال هذه الفاتورة'},
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=True, methods=['post'])
    def mark_paid(self, request, pk=None):
        """Mark invoice as paid."""
        invoice = self.get_object()
        if invoice.status in ['draft', 'sent']:
            invoice.status = 'paid'
            invoice.save()
            return Response({'message': 'تم تأكيد دفع الفاتورة'})
        return Response(
            {'error': 'لا يمكن تأكيد دفع هذه الفاتورة'},
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=False, methods=['get'])
    def overdue(self, request):
        """Get overdue invoices."""
        today = timezone.now().date()
        overdue_invoices = self.get_queryset().filter(
            due_date__lt=today,
            status__in=['draft', 'sent']
        )
        serializer = self.get_serializer(overdue_invoices, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get invoice statistics."""
        today = timezone.now().date()
        current_month = today.replace(day=1)

        stats = {
            'total_invoices': self.get_queryset().count(),
            'draft_invoices': self.get_queryset().filter(status='draft').count(),
            'sent_invoices': self.get_queryset().filter(status='sent').count(),
            'paid_invoices': self.get_queryset().filter(status='paid').count(),
            'overdue_invoices': self.get_queryset().filter(
                due_date__lt=today,
                status__in=['draft', 'sent']
            ).count(),
            'monthly_invoices': self.get_queryset().filter(
                issue_date__gte=current_month
            ).count(),
            'total_amount': self.get_queryset().aggregate(
                total=Sum('total_amount')
            )['total'] or 0,
            'paid_amount': self.get_queryset().filter(
                status='paid'
            ).aggregate(total=Sum('total_amount'))['total'] or 0,
        }

        return Response(stats)


class PaymentViewSet(viewsets.ModelViewSet):
    """API viewset for payments."""

    queryset = Payment.objects.all()
    serializer_class = PaymentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = Payment.objects.select_related(
            'invoice__client', 'reservation__client'
        ).order_by('-payment_date')

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by payment method
        method = self.request.query_params.get('payment_method')
        if method:
            queryset = queryset.filter(payment_method=method)

        # Filter by date range
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')
        if date_from:
            queryset = queryset.filter(payment_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(payment_date__lte=date_to)

        return queryset

    @action(detail=True, methods=['post'])
    def confirm_payment(self, request, pk=None):
        """Confirm payment."""
        payment = self.get_object()
        if payment.status == 'pending':
            payment.status = 'completed'
            payment.save()

            # Update invoice status if fully paid
            if payment.invoice:
                total_payments = payment.invoice.payments.filter(
                    status='completed'
                ).aggregate(total=Sum('amount'))['total'] or 0

                if total_payments >= payment.invoice.total_amount:
                    payment.invoice.status = 'paid'
                    payment.invoice.save()

            return Response({'message': 'تم تأكيد الدفع بنجاح'})

        return Response(
            {'error': 'لا يمكن تأكيد هذا الدفع'},
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get payment statistics."""
        today = timezone.now().date()
        current_month = today.replace(day=1)

        stats = {
            'total_payments': self.get_queryset().count(),
            'pending_payments': self.get_queryset().filter(status='pending').count(),
            'completed_payments': self.get_queryset().filter(status='completed').count(),
            'failed_payments': self.get_queryset().filter(status='failed').count(),
            'monthly_payments': self.get_queryset().filter(
                payment_date__gte=current_month
            ).count(),
            'total_amount': self.get_queryset().filter(
                status='completed'
            ).aggregate(total=Sum('amount'))['total'] or 0,
            'monthly_amount': self.get_queryset().filter(
                status='completed',
                payment_date__gte=current_month
            ).aggregate(total=Sum('amount'))['total'] or 0,
        }

        return Response(stats)


class ExpenseViewSet(viewsets.ModelViewSet):
    """API viewset for expenses."""

    queryset = Expense.objects.all()
    serializer_class = ExpenseSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = Expense.objects.select_related(
            'vendor', 'submitted_by'
        ).order_by('-expense_date')

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by category
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category=category)

        # Filter by vendor
        vendor_id = self.request.query_params.get('vendor')
        if vendor_id:
            queryset = queryset.filter(vendor_id=vendor_id)

        return queryset

    def perform_create(self, serializer):
        """Set submitted_by to current user."""
        serializer.save(submitted_by=self.request.user)

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve expense."""
        expense = self.get_object()
        if expense.status == 'pending':
            expense.status = 'approved'
            expense.approved_by = request.user
            expense.approval_date = timezone.now()
            expense.save()
            return Response({'message': 'تم اعتماد المصروف'})

        return Response(
            {'error': 'لا يمكن اعتماد هذا المصروف'},
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """Reject expense."""
        expense = self.get_object()
        if expense.status == 'pending':
            expense.status = 'rejected'
            expense.save()
            return Response({'message': 'تم رفض المصروف'})

        return Response(
            {'error': 'لا يمكن رفض هذا المصروف'},
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get expense statistics."""
        today = timezone.now().date()
        current_month = today.replace(day=1)

        stats = {
            'total_expenses': self.get_queryset().count(),
            'pending_expenses': self.get_queryset().filter(status='pending').count(),
            'approved_expenses': self.get_queryset().filter(status='approved').count(),
            'paid_expenses': self.get_queryset().filter(status='paid').count(),
            'rejected_expenses': self.get_queryset().filter(status='rejected').count(),
            'monthly_expenses': self.get_queryset().filter(
                expense_date__gte=current_month
            ).count(),
            'total_amount': self.get_queryset().filter(
                status='paid'
            ).aggregate(total=Sum('amount'))['total'] or 0,
            'monthly_amount': self.get_queryset().filter(
                status='paid',
                expense_date__gte=current_month
            ).aggregate(total=Sum('amount'))['total'] or 0,
        }

        return Response(stats)


class BankAccountViewSet(viewsets.ModelViewSet):
    """API viewset for bank accounts."""

    queryset = BankAccount.objects.all()
    serializer_class = BankAccountSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = BankAccount.objects.order_by('account_name')

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get bank accounts summary."""
        accounts = self.get_queryset().filter(is_active=True)

        summary = {
            'total_accounts': accounts.count(),
            'total_balance': accounts.aggregate(
                total=Sum('balance')
            )['total'] or 0,
            'accounts_by_currency': accounts.values('currency').annotate(
                count=Count('id'),
                total_balance=Sum('balance')
            ).order_by('currency'),
        }

        return Response(summary)
