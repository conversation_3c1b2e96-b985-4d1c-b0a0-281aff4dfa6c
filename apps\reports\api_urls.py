"""
API URL configuration for reports app.
"""
from django.urls import path
from . import api_views

urlpatterns = [
    # Dashboard APIs
    path('dashboard/stats/', api_views.DashboardStatsView.as_view(), name='dashboard-stats'),
    path('dashboard/charts/', api_views.DashboardChartsView.as_view(), name='dashboard-charts'),

    # Sales Report APIs
    path('sales/summary/', api_views.SalesSummaryView.as_view(), name='sales-summary'),
    path('sales/trends/', api_views.SalesTrendsView.as_view(), name='sales-trends'),
    path('sales/by-package/', api_views.PackageSalesView.as_view(), name='package-sales'),
    path('sales/by-agent/', api_views.AgentSalesView.as_view(), name='agent-sales'),

    # Client Report APIs
    path('clients/summary/', api_views.ClientSummaryView.as_view(), name='client-summary'),
    path('clients/demographics/', api_views.ClientDemographicsView.as_view(), name='client-demographics'),
    path('clients/acquisition/', api_views.ClientAcquisitionView.as_view(), name='client-acquisition'),

    # Financial Report APIs
    path('financial/summary/', api_views.FinancialSummaryView.as_view(), name='financial-summary'),
    path('financial/revenue/', api_views.RevenueAnalysisView.as_view(), name='revenue-analysis'),
    path('financial/expenses/', api_views.ExpenseAnalysisView.as_view(), name='expense-analysis'),
    path('financial/profit-loss/', api_views.ProfitLossView.as_view(), name='profit-loss'),

    # Operational Report APIs
    path('operational/occupancy/', api_views.OccupancyRatesView.as_view(), name='occupancy-rates'),
    path('operational/performance/', api_views.PerformanceMetricsView.as_view(), name='performance-metrics'),

    # Export APIs
    path('export/excel/', api_views.ExcelExportView.as_view(), name='excel-export'),
    path('export/pdf/', api_views.PDFExportView.as_view(), name='pdf-export'),
    path('export/csv/', api_views.CSVExportView.as_view(), name='csv-export'),
]
