"""
API URL configuration for tours app.
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import api_views

router = DefaultRouter()
router.register(r'packages', api_views.TourPackageViewSet)
router.register(r'destinations', api_views.DestinationViewSet)
router.register(r'categories', api_views.TourCategoryViewSet)
router.register(r'itineraries', api_views.ItineraryViewSet)
router.register(r'availability', api_views.PackageAvailabilityViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('packages/<int:package_id>/itineraries/', api_views.PackageItineraryListView.as_view(), name='package-itineraries'),
    path('packages/<int:package_id>/availability/', api_views.PackageAvailabilityListView.as_view(), name='package-availability'),
    path('search/', api_views.PackageSearchView.as_view(), name='package-search'),
]
