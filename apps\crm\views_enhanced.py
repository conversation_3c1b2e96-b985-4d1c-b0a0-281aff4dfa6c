"""
Enhanced views for CRM module with modern UI and AJAX functionality.
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
import json

from .models import Client
from .forms_enhanced import EnhancedClientForm, ClientSearchForm


@login_required
def client_dashboard(request):
    """Enhanced client dashboard with statistics and charts."""
    from django.utils import timezone

    # Get statistics
    total_clients = Client.objects.count()
    vip_clients = Client.objects.filter(vip_status=True).count()
    new_clients_this_month = Client.objects.filter(
        created_at__month=timezone.now().month,
        created_at__year=timezone.now().year
    ).count()

    # Client type distribution
    client_types = Client.objects.values('client_type').annotate(
        count=Count('id')
    ).order_by('-count')

    # Recent clients
    recent_clients = Client.objects.order_by('-created_at')[:5]

    context = {
        'total_clients': total_clients,
        'vip_clients': vip_clients,
        'new_clients_this_month': new_clients_this_month,
        'client_types': client_types,
        'recent_clients': recent_clients,
        'page_title': _('لوحة تحكم العملاء'),
    }

    return render(request, 'crm/dashboard_modern.html', context)


@login_required
def client_list(request):
    """Enhanced client list with advanced search and filtering."""

    form = ClientSearchForm(request.GET)
    clients = Client.objects.all()

    # Apply search filters
    if form.is_valid():
        search_query = form.cleaned_data.get('search_query')
        if search_query:
            clients = clients.filter(
                Q(first_name_ar__icontains=search_query) |
                Q(last_name_ar__icontains=search_query) |
                Q(first_name_fr__icontains=search_query) |
                Q(last_name_fr__icontains=search_query) |
                Q(email__icontains=search_query) |
                Q(phone__icontains=search_query) |
                Q(client_code__icontains=search_query)
            )

        client_type = form.cleaned_data.get('client_type')
        if client_type:
            clients = clients.filter(client_type=client_type)

        vip_status = form.cleaned_data.get('vip_status')
        if vip_status:
            clients = clients.filter(vip_status=(vip_status == 'true'))

        date_from = form.cleaned_data.get('date_from')
        if date_from:
            clients = clients.filter(created_at__date__gte=date_from)

        date_to = form.cleaned_data.get('date_to')
        if date_to:
            clients = clients.filter(created_at__date__lte=date_to)

    # Pagination
    paginator = Paginator(clients, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'form': form,
        'page_obj': page_obj,
        'clients': page_obj,
        'total_count': clients.count(),
        'page_title': _('قائمة العملاء'),
    }

    # Update context for modern template
    context.update({
        'search_form': form,
        'stats': {
            'total_clients': Client.objects.count(),
            'vip_clients': Client.objects.filter(vip_status=True).count(),
            'individual_clients': Client.objects.filter(client_type='individual').count(),
            'corporate_clients': Client.objects.filter(client_type='corporate').count(),
        }
    })

    return render(request, 'crm/client_list_modern.html', context)


@login_required
def client_create(request):
    """Enhanced client creation with AJAX validation."""

    if request.method == 'POST':
        form = EnhancedClientForm(request.POST)
        if form.is_valid():
            client = form.save()
            messages.success(request, _('تم إنشاء العميل بنجاح'))

            # Return JSON response for AJAX requests
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': True,
                    'message': _('تم إنشاء العميل بنجاح'),
                    'client_id': client.id,
                    'redirect_url': f'/crm/clients/{client.id}/'
                })

            return redirect('crm:client_detail', pk=client.id)
        else:
            # Return validation errors for AJAX requests
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': False,
                    'errors': form.errors
                })
    else:
        form = EnhancedClientForm()

    context = {
        'form': form,
        'page_title': _('إضافة عميل جديد'),
        'form_action': 'create',
    }

    return render(request, 'crm/client_form_enhanced.html', context)


@login_required
def client_detail(request, pk):
    """Enhanced client detail view with tabs and related data."""

    client = get_object_or_404(Client, pk=pk)

    # Get related data
    # reservations = client.reservations.all()[:5]
    # payments = client.payments.all()[:5]
    # documents = client.documents.all()[:5]

    context = {
        'client': client,
        # 'reservations': reservations,
        # 'payments': payments,
        # 'documents': documents,
        'page_title': f'{client.full_name_ar} - {_("تفاصيل العميل")}',
    }

    return render(request, 'crm/client_detail_enhanced.html', context)


@login_required
def client_edit(request, pk):
    """Enhanced client editing."""

    client = get_object_or_404(Client, pk=pk)

    if request.method == 'POST':
        form = EnhancedClientForm(request.POST, instance=client)
        if form.is_valid():
            client = form.save()
            messages.success(request, _('تم تحديث بيانات العميل بنجاح'))

            # Return JSON response for AJAX requests
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': True,
                    'message': _('تم تحديث بيانات العميل بنجاح'),
                    'redirect_url': f'/crm/clients/{client.id}/'
                })

            return redirect('crm:client_detail', pk=client.id)
        else:
            # Return validation errors for AJAX requests
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': False,
                    'errors': form.errors
                })
    else:
        form = EnhancedClientForm(instance=client)

    context = {
        'form': form,
        'client': client,
        'page_title': f'{_("تعديل")} {client.full_name_ar}',
        'form_action': 'edit',
    }

    return render(request, 'crm/client_form_enhanced.html', context)


@login_required
@require_http_methods(["DELETE"])
def client_delete(request, pk):
    """AJAX client deletion."""

    client = get_object_or_404(Client, pk=pk)
    client_name = client.full_name_ar

    try:
        client.delete()
        return JsonResponse({
            'success': True,
            'message': f'{_("تم حذف العميل")} {client_name} {_("بنجاح")}'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'{_("خطأ في حذف العميل")}: {str(e)}'
        })


@login_required
def client_search_ajax(request):
    """AJAX client search for autocomplete."""

    query = request.GET.get('q', '')
    if len(query) < 2:
        return JsonResponse({'results': []})

    clients = Client.objects.filter(
        Q(first_name_ar__icontains=query) |
        Q(last_name_ar__icontains=query) |
        Q(first_name_fr__icontains=query) |
        Q(last_name_fr__icontains=query) |
        Q(email__icontains=query) |
        Q(phone__icontains=query) |
        Q(client_code__icontains=query)
    )[:10]

    results = []
    for client in clients:
        results.append({
            'id': client.id,
            'text': f"{client.full_name_ar} ({client.client_code})",
            'email': client.email,
            'phone': client.phone,
            'client_type': client.get_client_type_display(),
            'vip_status': client.vip_status
        })

    return JsonResponse({'results': results})


@login_required
def client_export(request):
    """Export clients to Excel/CSV."""

    import csv
    from django.http import HttpResponse

    # Get filtered clients based on search parameters
    form = ClientSearchForm(request.GET)
    clients = Client.objects.all()

    if form.is_valid():
        # Apply same filters as in client_list view
        search_query = form.cleaned_data.get('search_query')
        if search_query:
            clients = clients.filter(
                Q(first_name_ar__icontains=search_query) |
                Q(last_name_ar__icontains=search_query) |
                Q(email__icontains=search_query) |
                Q(phone__icontains=search_query)
            )

        client_type = form.cleaned_data.get('client_type')
        if client_type:
            clients = clients.filter(client_type=client_type)

    # Create CSV response
    response = HttpResponse(content_type='text/csv; charset=utf-8')
    response['Content-Disposition'] = 'attachment; filename="clients.csv"'

    # Add BOM for proper UTF-8 encoding in Excel
    response.write('\ufeff')

    writer = csv.writer(response)

    # Write header
    writer.writerow([
        'رمز العميل', 'الاسم بالعربية', 'الاسم بالفرنسية', 'البريد الإلكتروني',
        'الهاتف', 'نوع العميل', 'عميل مميز', 'تاريخ الإنشاء'
    ])

    # Write data
    for client in clients:
        writer.writerow([
            client.client_code,
            client.full_name_ar,
            client.full_name_fr,
            client.email,
            client.phone,
            client.get_client_type_display(),
            'نعم' if client.vip_status else 'لا',
            client.created_at.strftime('%Y-%m-%d')
        ])

    return response
