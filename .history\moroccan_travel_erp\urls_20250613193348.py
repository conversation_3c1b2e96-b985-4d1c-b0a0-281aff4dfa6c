"""
Simplified URL configuration for Moroccan Travel Agency ERP project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

# Error handlers
handler404 = 'apps.core.error_handlers.handler404'
handler500 = 'apps.core.error_handlers.handler500'
handler403 = 'apps.core.error_handlers.handler403'
handler400 = 'apps.core.error_handlers.handler400'
from django.views.generic import TemplateView
from apps.core.views import DashboardView, QuickStatsView, SystemSettingsView

urlpatterns = [
    # Admin
    path('admin/', admin.site.urls),

    # Authentication
    path('accounts/', include('apps.accounts.urls')),

    # Main dashboard
    path('', DashboardView.as_view(), name='dashboard'),

    # Quick stats
    path('stats/', QuickStatsView.as_view(), name='quick_stats'),

    # Reports
    path('reports/', include('apps.reports.urls')),

    # Finance
    path('finance/', include('apps.finance.urls')),

    # Reservations
    path('reservations/', include('apps.reservations.urls')),

    # HR
    path('hr/', include('apps.hr.urls')),

    # Suppliers
    path('suppliers/', include('apps.suppliers.urls')),

    # Settings
    path('settings/', SystemSettingsView.as_view(), name='system_settings'),

    # API URLs
    path('api/finance/', include('apps.finance.api_urls')),

    # API
    path('api/', include('api_urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
