"""
Backup and restore management system.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from apps.core.models import TimeStampedModel


class BackupSchedule(TimeStampedModel):
    """Backup scheduling configuration."""

    BACKUP_TYPES = [
        ('full', _('نسخة كاملة')),
        ('incremental', _('نسخة تزايدية')),
        ('differential', _('نسخة تفاضلية')),
    ]

    FREQUENCY_CHOICES = [
        ('daily', _('يومي')),
        ('weekly', _('أسبوعي')),
        ('monthly', _('شهري')),
        ('custom', _('مخصص')),
    ]

    name = models.CharField(_('اسم الجدولة'), max_length=100)
    description = models.TextField(_('الوصف'), blank=True)

    # Backup Configuration
    backup_type = models.CharField(_('نوع النسخة'), max_length=20, choices=BACKUP_TYPES, default='full')
    frequency = models.CharField(_('التكرار'), max_length=20, choices=FREQUENCY_CHOICES, default='daily')

    # Timing
    scheduled_time = models.TimeField(_('الوقت المجدول'))
    cron_expression = models.CharField(_('تعبير Cron'), max_length=100, blank=True)

    # What to backup
    include_database = models.BooleanField(_('تضمين قاعدة البيانات'), default=True)
    include_media = models.BooleanField(_('تضمين ملفات الوسائط'), default=True)
    include_static = models.BooleanField(_('تضمين الملفات الثابتة'), default=False)
    include_logs = models.BooleanField(_('تضمين ملفات السجلات'), default=False)

    # Storage
    storage_location = models.CharField(_('موقع التخزين'), max_length=500)
    max_backups_to_keep = models.PositiveIntegerField(_('عدد النسخ المحفوظة'), default=7)

    # Compression and Encryption
    compress_backup = models.BooleanField(_('ضغط النسخة'), default=True)
    encrypt_backup = models.BooleanField(_('تشفير النسخة'), default=True)

    # Notifications
    notify_on_success = models.BooleanField(_('إشعار عند النجاح'), default=False)
    notify_on_failure = models.BooleanField(_('إشعار عند الفشل'), default=True)
    notification_emails = models.JSONField(_('بريد الإشعارات'), default=list, blank=True)

    # Status
    is_active = models.BooleanField(_('نشط'), default=True)
    last_run = models.DateTimeField(_('آخر تشغيل'), null=True, blank=True)
    next_run = models.DateTimeField(_('التشغيل القادم'), null=True, blank=True)

    class Meta:
        verbose_name = _('جدولة النسخ الاحتياطية')
        verbose_name_plural = _('جدولة النسخ الاحتياطية')
        ordering = ['name']

    def __str__(self):
        return self.name


class BackupRecord(TimeStampedModel):
    """Record of backup operations."""

    STATUS_CHOICES = [
        ('running', _('قيد التشغيل')),
        ('completed', _('مكتمل')),
        ('failed', _('فشل')),
        ('cancelled', _('ملغي')),
        ('corrupted', _('تالف')),
    ]

    schedule = models.ForeignKey(
        BackupSchedule,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('الجدولة')
    )

    # Backup Details
    backup_name = models.CharField(_('اسم النسخة'), max_length=200)
    backup_type = models.CharField(_('نوع النسخة'), max_length=20, choices=BackupSchedule.BACKUP_TYPES)

    # Timing
    started_at = models.DateTimeField(_('بدء في'), auto_now_add=True)
    completed_at = models.DateTimeField(_('انتهى في'), null=True, blank=True)
    duration_seconds = models.PositiveIntegerField(_('المدة (ثانية)'), null=True, blank=True)

    # File Information
    file_path = models.CharField(_('مسار الملف'), max_length=500)
    file_size_bytes = models.BigIntegerField(_('حجم الملف (بايت)'), default=0)
    checksum = models.CharField(_('المجموع التحققي'), max_length=64, blank=True)

    # Status and Results
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='running')
    success = models.BooleanField(_('نجح'), default=False)
    error_message = models.TextField(_('رسالة الخطأ'), blank=True)

    # Statistics
    database_size_bytes = models.BigIntegerField(_('حجم قاعدة البيانات (بايت)'), default=0)
    media_size_bytes = models.BigIntegerField(_('حجم الوسائط (بايت)'), default=0)
    total_files = models.PositiveIntegerField(_('إجمالي الملفات'), default=0)

    # Verification
    verified = models.BooleanField(_('موثق'), default=False)
    verification_date = models.DateTimeField(_('تاريخ التوثيق'), null=True, blank=True)

    # Triggered by
    triggered_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('شغل بواسطة')
    )

    notes = models.TextField(_('ملاحظات'), blank=True)

    class Meta:
        verbose_name = _('سجل النسخة الاحتياطية')
        verbose_name_plural = _('سجلات النسخ الاحتياطية')
        ordering = ['-started_at']
        indexes = [
            models.Index(fields=['status', 'started_at']),
            models.Index(fields=['schedule']),
        ]

    def __str__(self):
        return f"{self.backup_name} - {self.started_at.date()}"

    @property
    def file_size_mb(self):
        """Get file size in MB."""
        return round(self.file_size_bytes / (1024 * 1024), 2)

    @property
    def file_size_gb(self):
        """Get file size in GB."""
        return round(self.file_size_bytes / (1024 * 1024 * 1024), 2)


class RestoreOperation(TimeStampedModel):
    """Record of restore operations."""

    STATUS_CHOICES = [
        ('pending', _('معلق')),
        ('running', _('قيد التشغيل')),
        ('completed', _('مكتمل')),
        ('failed', _('فشل')),
        ('cancelled', _('ملغي')),
    ]

    RESTORE_TYPES = [
        ('full', _('استعادة كاملة')),
        ('database_only', _('قاعدة البيانات فقط')),
        ('media_only', _('الوسائط فقط')),
        ('selective', _('استعادة انتقائية')),
    ]

    backup_record = models.ForeignKey(
        BackupRecord,
        on_delete=models.CASCADE,
        verbose_name=_('سجل النسخة الاحتياطية')
    )

    # Restore Details
    restore_type = models.CharField(_('نوع الاستعادة'), max_length=20, choices=RESTORE_TYPES, default='full')
    target_location = models.CharField(_('الموقع المستهدف'), max_length=500, blank=True)

    # Timing
    started_at = models.DateTimeField(_('بدء في'), auto_now_add=True)
    completed_at = models.DateTimeField(_('انتهى في'), null=True, blank=True)
    duration_seconds = models.PositiveIntegerField(_('المدة (ثانية)'), null=True, blank=True)

    # Status
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='pending')
    success = models.BooleanField(_('نجح'), default=False)
    error_message = models.TextField(_('رسالة الخطأ'), blank=True)

    # Progress
    progress_percentage = models.PositiveIntegerField(_('نسبة التقدم'), default=0)
    current_step = models.CharField(_('الخطوة الحالية'), max_length=200, blank=True)

    # Options
    overwrite_existing = models.BooleanField(_('استبدال الموجود'), default=False)
    create_backup_before_restore = models.BooleanField(_('إنشاء نسخة قبل الاستعادة'), default=True)

    # Triggered by
    triggered_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('شغل بواسطة')
    )

    # Approval (for production environments)
    requires_approval = models.BooleanField(_('يتطلب موافقة'), default=False)
    approved_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_restores',
        verbose_name=_('وافق عليه')
    )
    approval_date = models.DateTimeField(_('تاريخ الموافقة'), null=True, blank=True)

    notes = models.TextField(_('ملاحظات'), blank=True)

    class Meta:
        verbose_name = _('عملية الاستعادة')
        verbose_name_plural = _('عمليات الاستعادة')
        ordering = ['-started_at']
        indexes = [
            models.Index(fields=['status', 'started_at']),
            models.Index(fields=['backup_record']),
        ]

    def __str__(self):
        return f"استعادة {self.backup_record.backup_name} - {self.started_at.date()}"


class BackupStorage(TimeStampedModel):
    """Backup storage locations and configurations."""

    STORAGE_TYPES = [
        ('local', _('محلي')),
        ('ftp', _('FTP')),
        ('sftp', _('SFTP')),
        ('s3', _('Amazon S3')),
        ('google_drive', _('Google Drive')),
        ('dropbox', _('Dropbox')),
        ('azure', _('Azure Blob')),
    ]

    name = models.CharField(_('اسم التخزين'), max_length=100)
    storage_type = models.CharField(_('نوع التخزين'), max_length=20, choices=STORAGE_TYPES)

    # Connection Details
    host = models.CharField(_('المضيف'), max_length=200, blank=True)
    port = models.PositiveIntegerField(_('المنفذ'), null=True, blank=True)
    username = models.CharField(_('اسم المستخدم'), max_length=100, blank=True)
    password = models.CharField(_('كلمة المرور'), max_length=200, blank=True)  # Should be encrypted

    # Paths and Configuration
    base_path = models.CharField(_('المسار الأساسي'), max_length=500)
    configuration = models.JSONField(_('التكوين'), default=dict, blank=True)

    # Capacity and Limits
    total_capacity_gb = models.PositiveIntegerField(_('السعة الإجمالية (جيجابايت)'), null=True, blank=True)
    used_space_gb = models.DecimalField(_('المساحة المستخدمة (جيجابايت)'), max_digits=10, decimal_places=2, default=0)

    # Status
    is_active = models.BooleanField(_('نشط'), default=True)
    is_default = models.BooleanField(_('افتراضي'), default=False)
    last_tested = models.DateTimeField(_('آخر اختبار'), null=True, blank=True)
    test_status = models.CharField(_('حالة الاختبار'), max_length=20, blank=True)

    notes = models.TextField(_('ملاحظات'), blank=True)

    class Meta:
        verbose_name = _('تخزين النسخ الاحتياطية')
        verbose_name_plural = _('تخزين النسخ الاحتياطية')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.get_storage_type_display()})"

    @property
    def available_space_gb(self):
        """Calculate available space."""
        if self.total_capacity_gb:
            return max(0, self.total_capacity_gb - float(self.used_space_gb))
        return None

    @property
    def usage_percentage(self):
        """Calculate usage percentage."""
        if self.total_capacity_gb and self.total_capacity_gb > 0:
            return (float(self.used_space_gb) / self.total_capacity_gb) * 100
        return 0
