"""
API views for accounts app.
"""
from rest_framework import viewsets, generics, status
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from .models import Role, UserSession
from .serializers import (
    UserSerializer,
    RoleSerializer,
    PermissionSerializer,
    UserSessionSerializer,
    LoginSerializer,
    ProfileSerializer,
    PasswordChangeSerializer,
    PasswordResetSerializer,
    PasswordResetConfirmSerializer,
)

User = get_user_model()


class UserViewSet(viewsets.ModelViewSet):
    """API endpoint for managing users."""
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter queryset based on search parameters."""
        queryset = super().get_queryset()
        search = self.request.query_params.get('search', '')
        if search:
            queryset = queryset.filter(
                Q(username__icontains=search) |
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(email__icontains=search)
            )
        return queryset


class RoleViewSet(viewsets.ModelViewSet):
    """API endpoint for managing roles."""
    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    permission_classes = [IsAuthenticated]


class PermissionViewSet(viewsets.ModelViewSet):
    """API endpoint for managing permissions."""
    queryset = Permission.objects.all()
    serializer_class = PermissionSerializer
    permission_classes = [IsAuthenticated]


class UserSessionViewSet(viewsets.ModelViewSet):
    """API endpoint for managing user sessions."""
    queryset = UserSession.objects.all()
    serializer_class = UserSessionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter sessions to show only the user's own sessions unless they're staff."""
        if self.request.user.is_staff:
            return super().get_queryset()
        return super().get_queryset().filter(user=self.request.user)


class LoginView(TokenObtainPairView):
    """API endpoint for user login."""
    serializer_class = LoginSerializer
    permission_classes = [AllowAny]


class LogoutView(APIView):
    """API endpoint for user logout."""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Handle logout by blacklisting the refresh token."""
        try:
            refresh_token = request.data["refresh_token"]
            token = RefreshToken(refresh_token)
            token.blacklist()
            return Response({"detail": _("Successfully logged out.")})
        except Exception:
            return Response(
                {"detail": _("Invalid token.")},
                status=status.HTTP_400_BAD_REQUEST
            )


class RefreshTokenView(TokenRefreshView):
    """API endpoint for refreshing authentication tokens."""
    pass


class ProfileView(generics.RetrieveUpdateAPIView):
    """API endpoint for managing user profile."""
    serializer_class = ProfileSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        """Return the current user's profile."""
        return self.request.user


class UserRoleListView(generics.ListAPIView):
    """API endpoint for listing user roles."""
    serializer_class = RoleSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Return roles for the specified user."""
        user_id = self.kwargs.get('user_id')
        user = User.objects.get(id=user_id)
        return user.roles.all()


class UserPermissionListView(generics.ListAPIView):
    """API endpoint for listing user permissions."""
    serializer_class = PermissionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Return permissions for the specified user."""
        user_id = self.kwargs.get('user_id')
        user = User.objects.get(id=user_id)
        return user.user_permissions.all()


class UserSearchView(generics.ListAPIView):
    """API endpoint for searching users."""
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter users based on search parameters."""
        queryset = User.objects.all()
        search = self.request.query_params.get('q', '')
        if search:
            queryset = queryset.filter(
                Q(username__icontains=search) |
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(email__icontains=search)
            )
        return queryset


class PasswordChangeView(generics.GenericAPIView):
    """API endpoint for changing password."""
    serializer_class = PasswordChangeSerializer
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Handle password change request."""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response({"detail": _("Password successfully changed.")})


class PasswordResetView(generics.GenericAPIView):
    """API endpoint for requesting password reset."""
    serializer_class = PasswordResetSerializer
    permission_classes = [AllowAny]

    def post(self, request):
        """Handle password reset request."""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response({"detail": _("Password reset e-mail has been sent.")})


class PasswordResetConfirmView(generics.GenericAPIView):
    """API endpoint for confirming password reset."""
    serializer_class = PasswordResetConfirmSerializer
    permission_classes = [AllowAny]

    def post(self, request):
        """Handle password reset confirmation."""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response({"detail": _("Password has been reset.")})
