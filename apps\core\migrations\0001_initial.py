# Generated by Django 4.2.7 on 2025-06-12 17:35

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Country',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name_ar', models.CharField(max_length=100, verbose_name='الاسم بالعربية')),
                ('name_fr', models.Char<PERSON>ield(max_length=100, verbose_name='الاسم بالفرنسية')),
                ('name_en', models.Char<PERSON>ield(max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('code', models.Char<PERSON>ield(max_length=3, unique=True, verbose_name='الرمز')),
                ('phone_code', models.Char<PERSON><PERSON>(max_length=5, verbose_name='رمز الهاتف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
            ],
            options={
                'verbose_name': 'دولة',
                'verbose_name_plural': 'الدول',
                'ordering': ['name_ar'],
            },
        ),
        migrations.CreateModel(
            name='SystemSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=100, unique=True, verbose_name='المفتاح')),
                ('value', models.TextField(verbose_name='القيمة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
            ],
            options={
                'verbose_name': 'إعداد النظام',
                'verbose_name_plural': 'إعدادات النظام',
            },
        ),
        migrations.CreateModel(
            name='City',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name_ar', models.CharField(max_length=100, verbose_name='الاسم بالعربية')),
                ('name_fr', models.CharField(max_length=100, verbose_name='الاسم بالفرنسية')),
                ('name_en', models.CharField(max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.country', verbose_name='الدولة')),
            ],
            options={
                'verbose_name': 'مدينة',
                'verbose_name_plural': 'المدن',
                'ordering': ['name_ar'],
            },
        ),
    ]
