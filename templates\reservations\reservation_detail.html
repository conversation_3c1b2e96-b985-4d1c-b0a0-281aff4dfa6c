{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}تفاصيل الحجز {{ reservation.reservation_number }}{% endblock %}

{% block extra_css %}
<style>
.reservation-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
}

.reservation-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.status-badge {
    padding: 8px 16px;
    border-radius: 25px;
    font-weight: 500;
    font-size: 0.9rem;
}

.status-pending { background: #fff3cd; color: #856404; }
.status-confirmed { background: #d1edff; color: #0c63e4; }
.status-cancelled { background: #f8d7da; color: #721c24; }

.payment-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.payment-paid { background: #d4edda; color: #155724; }
.payment-partial { background: #fff3cd; color: #856404; }
.payment-pending { background: #f8d7da; color: #721c24; }

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-label {
    font-weight: 500;
    color: #6c757d;
}

.info-value {
    color: #495057;
}

.package-showcase {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
}

.participant-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid #667eea;
}

.service-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid #38ef7d;
}

.timeline-item {
    position: relative;
    padding-left: 30px;
    margin-bottom: 20px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 8px;
    top: 8px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #667eea;
}

.timeline-item::after {
    content: '';
    position: absolute;
    left: 13px;
    top: 20px;
    width: 2px;
    height: calc(100% - 12px);
    background: #e9ecef;
}

.timeline-item:last-child::after {
    display: none;
}

.btn-action {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.amount-summary {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.progress-payment {
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
    overflow: hidden;
}

.progress-payment .progress-bar {
    height: 100%;
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    transition: width 0.3s ease;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Reservation Header -->
    <div class="reservation-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="h2 mb-2">{{ reservation.reservation_number }}</h1>
                <p class="mb-1 opacity-75">
                    <i class="fas fa-user me-2"></i>{{ reservation.client.full_name_ar }}
                </p>
                <p class="mb-0 opacity-75">
                    <i class="fas fa-calendar me-2"></i>{{ reservation.departure_date }} - {{ reservation.return_date|default:"مفتوح" }}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <span class="status-badge status-{{ reservation.status }}">
                    {{ reservation.get_status_display }}
                </span>
                <br>
                <small class="payment-badge payment-{{ reservation.payment_status }} mt-2 d-inline-block">
                    {{ reservation.get_payment_status_display }}
                </small>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Package Information -->
            <div class="package-showcase">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4 class="mb-2">{{ reservation.package.title_ar }}</h4>
                        <p class="mb-2 text-muted">{{ reservation.package.category.name_ar }}</p>
                        <p class="mb-0">{{ reservation.package.description_ar|truncatewords:20 }}</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="badge bg-primary fs-6 mb-2">{{ reservation.package.duration }} أيام</div>
                        <br>
                        <span class="h5 text-primary">{{ reservation.package.price_per_person }} درهم/شخص</span>
                    </div>
                </div>
            </div>

            <!-- Client Information -->
            <div class="reservation-card">
                <h5 class="mb-4"><i class="fas fa-user me-2"></i>معلومات العميل</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-item">
                            <span class="info-label">الاسم الكامل</span>
                            <span class="info-value">{{ reservation.client.full_name_ar }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">البريد الإلكتروني</span>
                            <span class="info-value">{{ reservation.client.email }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">رقم الهاتف</span>
                            <span class="info-value">{{ reservation.client.phone|default:"غير محدد" }}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item">
                            <span class="info-label">الجنسية</span>
                            <span class="info-value">{{ reservation.client.nationality|default:"غير محدد" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">تاريخ الميلاد</span>
                            <span class="info-value">{{ reservation.client.date_of_birth|default:"غير محدد" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">رقم الهوية</span>
                            <span class="info-value">{{ reservation.client.passport_number|default:"غير محدد" }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trip Details -->
            <div class="reservation-card">
                <h5 class="mb-4"><i class="fas fa-plane me-2"></i>تفاصيل الرحلة</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-item">
                            <span class="info-label">تاريخ المغادرة</span>
                            <span class="info-value">{{ reservation.departure_date }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">تاريخ العودة</span>
                            <span class="info-value">{{ reservation.return_date|default:"مفتوح" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">مدة الرحلة</span>
                            <span class="info-value">{{ reservation.package.duration }} أيام</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item">
                            <span class="info-label">البالغين</span>
                            <span class="info-value">{{ reservation.adults }} شخص</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الأطفال</span>
                            <span class="info-value">{{ reservation.children }} طفل</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الرضع</span>
                            <span class="info-value">{{ reservation.infants }} رضيع</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Participants -->
            <div class="reservation-card">
                <h5 class="mb-4"><i class="fas fa-users me-2"></i>المشاركين</h5>
                {% if participants %}
                <div class="row">
                    {% for participant in participants %}
                    <div class="col-md-6">
                        <div class="participant-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ participant.full_name }}</h6>
                                    <p class="mb-1 text-muted">{{ participant.get_participant_type_display }}</p>
                                    <small class="text-muted">{{ participant.passport_number|default:"لا يوجد جواز" }}</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-info">{{ participant.age|default:"غير محدد" }} سنة</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-users fa-3x mb-3"></i>
                    <p>لم يتم إضافة مشاركين بعد</p>
                </div>
                {% endif %}
            </div>

            <!-- Additional Services -->
            <div class="reservation-card">
                <h5 class="mb-4"><i class="fas fa-concierge-bell me-2"></i>الخدمات الإضافية</h5>
                {% if additional_services %}
                <div class="row">
                    {% for service in additional_services %}
                    <div class="col-md-6">
                        <div class="service-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ service.service.name_ar }}</h6>
                                    <p class="mb-0 text-muted">{{ service.service.description_ar|truncatewords:10 }}</p>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-success">{{ service.price }} درهم</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-concierge-bell fa-3x mb-3"></i>
                    <p>لا توجد خدمات إضافية</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Payment Summary -->
            <div class="reservation-card">
                <h6 class="mb-3"><i class="fas fa-credit-card me-2"></i>ملخص المدفوعات</h6>

                <div class="amount-summary">
                    <div class="d-flex justify-content-between mb-2">
                        <span>المبلغ الإجمالي:</span>
                        <strong>{{ reservation.total_amount }} درهم</strong>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>المدفوع:</span>
                        <span class="text-success">{{ reservation.paid_amount|default:0 }} درهم</span>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <span>المتبقي:</span>
                        <span class="text-danger">{{ reservation.remaining_amount }} درهم</span>
                    </div>

                    <div class="progress-payment mb-2">
                        <div class="progress-bar" style="width: {% widthratio reservation.paid_amount reservation.total_amount 100 %}%"></div>
                    </div>
                    <small class="text-muted">
                        تم دفع {% widthratio reservation.paid_amount reservation.total_amount 100 %}% من المبلغ
                    </small>
                </div>
            </div>

            <!-- Actions -->
            <div class="reservation-card">
                <h6 class="mb-3"><i class="fas fa-cogs me-2"></i>الإجراءات</h6>
                <div class="d-grid gap-2">
                    {% if reservation.status == 'pending' %}
                    <button class="btn btn-success">
                        <i class="fas fa-check me-2"></i>تأكيد الحجز
                    </button>
                    <button class="btn btn-outline-danger">
                        <i class="fas fa-times me-2"></i>إلغاء الحجز
                    </button>
                    {% endif %}

                    <button class="btn btn-action">
                        <i class="fas fa-edit me-2"></i>تعديل الحجز
                    </button>
                    <button class="btn btn-outline-primary">
                        <i class="fas fa-file-invoice me-2"></i>إنشاء فاتورة
                    </button>
                    <button class="btn btn-outline-info">
                        <i class="fas fa-print me-2"></i>طباعة الحجز
                    </button>
                    <button class="btn btn-outline-secondary">
                        <i class="fas fa-envelope me-2"></i>إرسال تأكيد
                    </button>
                </div>
            </div>

            <!-- Timeline -->
            <div class="reservation-card">
                <h6 class="mb-3"><i class="fas fa-history me-2"></i>سجل الأحداث</h6>
                <div class="timeline">
                    <div class="timeline-item">
                        <div>
                            <strong>تم إنشاء الحجز</strong>
                            <br>
                            <small class="text-muted">{{ reservation.booking_date }}</small>
                        </div>
                    </div>

                    {% if reservation.status == 'confirmed' %}
                    <div class="timeline-item">
                        <div>
                            <strong>تم تأكيد الحجز</strong>
                            <br>
                            <small class="text-muted">{{ reservation.confirmed_at|default:"غير محدد" }}</small>
                        </div>
                    </div>
                    {% endif %}

                    {% if reservation.paid_amount > 0 %}
                    <div class="timeline-item">
                        <div>
                            <strong>تم استلام دفعة</strong>
                            <br>
                            <small class="text-muted">{{ reservation.paid_amount }} درهم</small>
                        </div>
                    </div>
                    {% endif %}

                    <div class="timeline-item">
                        <div>
                            <strong>آخر تحديث</strong>
                            <br>
                            <small class="text-muted">{{ reservation.updated_at }}</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Info -->
            <div class="reservation-card">
                <h6 class="mb-3"><i class="fas fa-info-circle me-2"></i>معلومات سريعة</h6>
                <div class="info-item">
                    <span class="info-label">تاريخ الحجز</span>
                    <span class="info-value">{{ reservation.booking_date }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">المسؤول</span>
                    <span class="info-value">{{ reservation.created_by.get_full_name|default:"النظام" }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">المصدر</span>
                    <span class="info-value">{{ reservation.source|default:"مباشر" }}</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Confirm reservation
function confirmReservation() {
    if (confirm('هل أنت متأكد من تأكيد هذا الحجز؟')) {
        // AJAX call to confirm reservation
        fetch(`/api/reservations/{{ reservation.id }}/confirm/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء تأكيد الحجز');
            }
        });
    }
}

// Cancel reservation
function cancelReservation() {
    if (confirm('هل أنت متأكد من إلغاء هذا الحجز؟')) {
        // AJAX call to cancel reservation
        fetch(`/api/reservations/{{ reservation.id }}/cancel/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء إلغاء الحجز');
            }
        });
    }
}
</script>
{% endblock %}
