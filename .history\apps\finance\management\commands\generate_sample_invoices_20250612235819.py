"""
Management command to generate sample invoices for testing.
"""
from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal
import random
from datetime import timedelta

from apps.finance.models import Invoice, InvoiceItem, Payment
from apps.crm.models import Client
from apps.reservations.models import Reservation


class Command(BaseCommand):
    help = 'Generate sample invoices for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=10,
            help='Number of invoices to create'
        )
        parser.add_argument(
            '--with-payments',
            action='store_true',
            help='Create payments for some invoices'
        )

    def handle(self, *args, **options):
        count = options['count']
        with_payments = options['with_payments']

        self.stdout.write(f'Creating {count} sample invoices...')

        # Get clients and reservations
        clients = list(Client.objects.filter(is_active=True))
        reservations = list(Reservation.objects.all())

        if not clients:
            self.stdout.write(
                self.style.ERROR('No active clients found. Please create some clients first.')
            )
            return

        created_invoices = 0
        created_payments = 0

        for i in range(count):
            try:
                # Random client
                client = random.choice(clients)

                # Random reservation (optional)
                reservation = random.choice(reservations) if reservations and random.choice([True, False]) else None

                # Random dates
                issue_date = timezone.now().date() - timedelta(days=random.randint(1, 90))
                due_date = issue_date + timedelta(days=random.randint(15, 45))

                # Random amounts
                subtotal = Decimal(str(random.uniform(1000, 10000))).quantize(Decimal('0.01'))
                tax_rate = Decimal('20.00')  # 20% VAT
                tax_amount = (subtotal * tax_rate / 100).quantize(Decimal('0.01'))
                discount_amount = Decimal(str(random.uniform(0, 500))).quantize(Decimal('0.01'))
                total_amount = subtotal + tax_amount - discount_amount

                # Random status
                status = random.choice(['draft', 'sent', 'paid'])

                # Create invoice
                invoice = Invoice.objects.create(
                    client=client,
                    reservation=reservation,
                    issue_date=issue_date,
                    due_date=due_date,
                    subtotal=subtotal,
                    tax_rate=tax_rate,
                    tax_amount=tax_amount,
                    discount_amount=discount_amount,
                    total_amount=total_amount,
                    status=status,
                    notes=f'Sample invoice #{i+1} for testing purposes'
                )

                # Create invoice items
                items_count = random.randint(1, 5)
                for j in range(items_count):
                    services = [
                        'خدمة الإقامة',
                        'خدمة النقل',
                        'جولة سياحية',
                        'وجبات',
                        'تأمين السفر',
                        'رسوم التأشيرة',
                        'خدمات إضافية'
                    ]

                    description = random.choice(services)
                    quantity = random.randint(1, 10)
                    unit_price = Decimal(str(random.uniform(50, 500))).quantize(Decimal('0.01'))
                    total_price = quantity * unit_price

                    InvoiceItem.objects.create(
                        invoice=invoice,
                        description=description,
                        quantity=quantity,
                        unit_price=unit_price,
                        total_price=total_price
                    )

                created_invoices += 1

                # Create payment if requested and invoice is paid
                if with_payments and status == 'paid':
                    payment_amount = total_amount
                    if random.choice([True, False]):
                        # Partial payment
                        payment_amount = (total_amount * Decimal(str(random.uniform(0.3, 0.9)))).quantize(Decimal('0.01'))

                    payment_date = issue_date + timedelta(days=random.randint(1, 30))
                    payment_method = random.choice(['cash', 'bank_transfer', 'credit_card', 'check'])

                    Payment.objects.create(
                        invoice=invoice,
                        amount=payment_amount,
                        payment_date=payment_date,
                        payment_method=payment_method,
                        reference_number=f'PAY-{random.randint(10000, 99999)}',
                        status='completed',
                        notes=f'Sample payment for invoice {invoice.invoice_number}'
                    )
                    created_payments += 1

                if (i + 1) % 10 == 0:
                    self.stdout.write(f'Created {i + 1} invoices...')

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error creating invoice {i+1}: {str(e)}')
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {created_invoices} invoices'
                + (f' and {created_payments} payments' if with_payments else '')
            )
        )

        # Display summary
        self.stdout.write('\n📊 Summary:')
        self.stdout.write(f'  💰 Total invoices: {Invoice.objects.count()}')
        self.stdout.write(f'  📄 Draft invoices: {Invoice.objects.filter(status="draft").count()}')
        self.stdout.write(f'  📤 Sent invoices: {Invoice.objects.filter(status="sent").count()}')
        self.stdout.write(f'  ✅ Paid invoices: {Invoice.objects.filter(status="paid").count()}')
        self.stdout.write(f'  💳 Total payments: {Payment.objects.count()}')

        total_revenue = Invoice.objects.filter(status='paid').aggregate(
            total=models.Sum('total_amount')
        )['total'] or 0
        self.stdout.write(f'  💵 Total revenue: {total_revenue} MAD')
