"""
Views for the core app.
"""
from django.shortcuts import render
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from .error_handlers import ErrorHandlingMixin, SafeQueryMixin
from django.db.models import Count, Sum
from django.utils.translation import gettext_lazy as _


class DashboardView(ErrorHandlingMixin, SafeQueryMixin, TemplateView):
    """Main dashboard view."""
    template_name = 'core/dashboard_simple.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Import here to avoid circular imports
        from apps.crm.models import Client
        from apps.tours.models import TourPackage, Destination
        from apps.accounts.models import User
        from apps.core.utils import get_client_statistics, get_package_statistics
        from datetime import datetime, timedelta

        # Get comprehensive statistics
        client_stats = get_client_statistics()
        package_stats = get_package_statistics()

        # Date ranges for recent activity
        today = datetime.now().date()
        last_7_days = today - timedelta(days=7)
        last_30_days = today - timedelta(days=30)

        # Dashboard statistics
        context.update({
            'total_clients': client_stats['total_clients'],
            'total_packages': package_stats['total_packages'],
            'featured_packages': package_stats['featured_packages'],
            'total_destinations': package_stats['total_destinations'],
            'total_users': User.objects.filter(is_active=True).count(),
            'vip_clients': client_stats['vip_clients'],
            'individual_clients': client_stats['individual_clients'],
            'corporate_clients': client_stats['corporate_clients'],
            'active_bookings': 0,  # Will be implemented when reservations module is added
            'monthly_sales': 0,    # Will be implemented when finance module is added
            'recent_clients': Client.objects.order_by('-created_at')[:5],
            'recent_packages': TourPackage.objects.filter(is_active=True).order_by('-created_at')[:3],
            'new_clients_7_days': Client.objects.filter(created_at__gte=last_7_days).count(),
            'new_clients_30_days': Client.objects.filter(created_at__gte=last_30_days).count(),
            'avg_package_price': package_stats['avg_price'],
            'top_nationalities': client_stats['top_nationalities'][:3],
        })

        return context


class QuickStatsView(TemplateView):
    """Quick statistics view."""
    template_name = 'core/quick_stats.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Import here to avoid circular imports
        from apps.crm.models import Client
        from apps.tours.models import TourPackage, Destination
        from apps.accounts.models import User

        # Detailed statistics
        context.update({
            'total_clients': Client.objects.count(),
            'individual_clients': Client.objects.filter(client_type='individual').count(),
            'corporate_clients': Client.objects.filter(client_type='corporate').count(),
            'vip_clients': Client.objects.filter(vip_status=True).count(),
            'total_packages': TourPackage.objects.filter(is_active=True).count(),
            'active_packages': TourPackage.objects.filter(is_active=True).count(),
            'featured_packages': TourPackage.objects.filter(is_featured=True, is_active=True).count(),
            'total_destinations': Destination.objects.filter(is_active=True).count(),
            'total_users': User.objects.filter(is_active=True).count(),
        })

        return context


class SystemSettingsView(TemplateView):
    """System settings and configuration view."""
    template_name = 'core/settings.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Import here to avoid circular imports
        from apps.crm.models import Client
        from apps.tours.models import TourPackage, Destination
        from apps.accounts.models import User

        # System statistics for settings page
        context.update({
            'total_clients': Client.objects.count(),
            'total_packages': TourPackage.objects.filter(is_active=True).count(),
            'total_destinations': Destination.objects.filter(is_active=True).count(),
            'total_users': User.objects.filter(is_active=True).count(),
            'vip_clients': Client.objects.filter(vip_status=True).count(),
            'featured_packages': TourPackage.objects.filter(is_featured=True, is_active=True).count(),
        })

        return context


class SettingsView(LoginRequiredMixin, TemplateView):
    """System settings view."""
    template_name = 'core/settings.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from .models import SystemSettings

        context['settings'] = SystemSettings.objects.filter(is_active=True)
        return context
