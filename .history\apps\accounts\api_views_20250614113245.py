"""
API views for accounts app.
"""
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission
from django.db.models import Q, QuerySet
from django.utils.translation import gettext_lazy as _
from rest_framework import viewsets, generics, status
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.request import Request
from rest_framework.views import APIView
from rest_framework_simplejwt.views import TokenRefreshView
from rest_framework_simplejwt.tokens import RefreshToken
from .models import Role, UserSession
from .serializers import (
    UserSerializer,
    RoleSerializer,
    PermissionSerializer,
    UserSessionSerializer,
    LoginSerializer,
    ProfileSerializer,
    PasswordChangeSerializer,
    PasswordResetSerializer,
    PasswordResetConfirmSerializer,
)

User = get_user_model()


class UserViewSet(viewsets.ModelViewSet):
    """API endpoint for managing users."""
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self) -> 'QuerySet':
        """Filter queryset based on search parameters."""
        queryset = super().get_queryset()
        search = self.request.query_params.get('search', '')
        if search:
            q = (Q(username__icontains=search)
                | Q(first_name__icontains=search)
                | Q(last_name__icontains=search)
                | Q(email__icontains=search))
            queryset = queryset.filter(q)
        return queryset


class RoleViewSet(viewsets.ModelViewSet):
    """API endpoint for managing roles."""
    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    permission_classes = [IsAuthenticated]


class PermissionViewSet(viewsets.ReadOnlyModelViewSet):
    """API endpoint for viewing permissions."""
    queryset = Permission.objects.all()
    serializer_class = PermissionSerializer
    permission_classes = [IsAuthenticated]


class UserSessionViewSet(viewsets.ModelViewSet):
    """API endpoint for managing user sessions."""
    queryset = UserSession.objects.all()
    serializer_class = UserSessionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self) -> 'QuerySet':
        """Filter sessions to only show current user's sessions."""
        return super().get_queryset().filter(user=self.request.user)


class LoginView(APIView):
    """API endpoint for user login."""
    permission_classes = [AllowAny]
    serializer_class = LoginSerializer

    def post(self, request: Request) -> Response:
        """Handle login request."""
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.validated_data['user']
        refresh = RefreshToken.for_user(user)
        UserSession.objects.create(
            user=user,
            ip_address=request.META.get('REMOTE_ADDR', ''),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )
        return Response({
            'refresh': str(refresh),
            'access': str(refresh.access_token),
        })


class LogoutView(APIView):
    """API endpoint for user logout."""
    permission_classes = [IsAuthenticated]

    def post(self, request: Request) -> Response:
        """Handle logout request."""
        try:
            refresh_token = request.data["refresh"]
            token = RefreshToken(refresh_token)
            token.blacklist()
            UserSession.objects.filter(
                user=request.user,
                is_active=True
            ).update(is_active=False)
            return Response(status=status.HTTP_205_RESET_CONTENT)
        except Exception:
            return Response(status=status.HTTP_400_BAD_REQUEST)


class RefreshTokenView(TokenRefreshView):
    """API endpoint for refreshing access tokens."""
    pass


class ProfileView(generics.RetrieveUpdateAPIView):
    """API endpoint for managing user profile."""
    serializer_class = ProfileSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        """Get the user object."""
        return self.request.user


class UserRoleListView(generics.ListAPIView):
    """API endpoint for listing user roles."""
    serializer_class = RoleSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self) -> 'QuerySet':
        """Get roles for the specified user."""
        user_id = self.kwargs['user_id']
        user = User.objects.get(id=user_id)
        return user.roles.all()


class UserPermissionListView(generics.ListAPIView):
    """API endpoint for listing user permissions."""
    serializer_class = PermissionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self) -> 'QuerySet':
        """Get permissions for the specified user."""
        user_id = self.kwargs['user_id']
        user = User.objects.get(id=user_id)
        return user.user_permissions.all()


class UserSearchView(generics.ListAPIView):
    """API endpoint for searching users."""
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self) -> 'QuerySet':
        """Search users based on query parameters."""
        search = self.request.query_params.get('q', '')
        q = (Q(username__icontains=search)
            | Q(first_name__icontains=search)
            | Q(last_name__icontains=search)
            | Q(email__icontains=search))
        return User.objects.filter(q)


class PasswordChangeView(APIView):
    """API endpoint for changing password."""
    permission_classes = [IsAuthenticated]

    def post(self, request: Request) -> Response:
        """Handle password change request."""
        serializer = PasswordChangeSerializer(
            data=request.data,
            context={'request': request}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response({'detail': _('Password changed successfully.')})


class PasswordResetView(APIView):
    """API endpoint for requesting password reset."""
    permission_classes = [AllowAny]

    def post(self, request: Request) -> Response:
        """Handle password reset request."""
        serializer = PasswordResetSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response({
            'detail': _('Password reset email has been sent.')
        })


class PasswordResetConfirmView(APIView):
    """API endpoint for confirming password reset."""
    permission_classes = [AllowAny]

    def post(self, request: Request) -> Response:
        """Handle password reset confirmation."""
        serializer = PasswordResetConfirmSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response({
            'detail': _('Password has been reset successfully.')
        })
