{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}
{% load static %}

{% block title %}
    {% if form_action == 'create' %}
        {% trans "إضافة عميل جديد" %}
    {% else %}
        {% trans "تعديل بيانات العميل" %}
    {% endif %}
{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --shadow-soft: 0 10px 30px rgba(0, 0, 0, 0.1);
        --shadow-hover: 0 15px 40px rgba(0, 0, 0, 0.15);
        --border-radius: 15px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    .form-wizard-container {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-soft);
        overflow: hidden;
        margin: 20px 0;
    }

    .form-wizard-header {
        background: var(--primary-gradient);
        color: white;
        padding: 30px;
        text-align: center;
        position: relative;
    }

    .form-wizard-header::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 20px solid transparent;
        border-right: 20px solid transparent;
        border-top: 10px solid #764ba2;
    }

    .wizard-steps {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 30px 20px;
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
    }

    .wizard-step {
        display: flex;
        align-items: center;
        margin: 0 15px;
        position: relative;
    }

    .step-circle {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: #e9ecef;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 18px;
        transition: var(--transition);
        position: relative;
        z-index: 2;
    }

    .step-circle.active {
        background: var(--primary-gradient);
        color: white;
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .step-circle.completed {
        background: var(--success-gradient);
        color: white;
    }

    .step-circle.completed::after {
        content: '✓';
        font-size: 20px;
    }

    .step-line {
        width: 80px;
        height: 3px;
        background: #e9ecef;
        position: relative;
        z-index: 1;
    }

    .step-line.completed {
        background: var(--success-gradient);
    }

    .step-label {
        position: absolute;
        top: 60px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 12px;
        font-weight: 500;
        color: #6c757d;
        white-space: nowrap;
    }

    .step-label.active {
        color: #667eea;
        font-weight: 600;
    }

    .form-content {
        padding: 40px;
    }

    .floating-label {
        position: relative;
        margin-bottom: 25px;
    }

    .floating-label .form-control,
    .floating-label .form-select {
        height: 60px;
        padding: 20px 15px 10px 15px;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        font-size: 16px;
        transition: var(--transition);
        background: #fff;
    }

    .floating-label .form-control:focus,
    .floating-label .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.15);
        outline: none;
    }

    .floating-label label {
        position: absolute;
        top: 20px;
        right: 15px;
        font-size: 16px;
        color: #6c757d;
        transition: var(--transition);
        pointer-events: none;
        background: white;
        padding: 0 5px;
    }

    .floating-label .form-control:focus + label,
    .floating-label .form-control:not(:placeholder-shown) + label,
    .floating-label .form-select:focus + label,
    .floating-label .form-select:not([value=""]) + label {
        top: -8px;
        font-size: 12px;
        color: #667eea;
        font-weight: 600;
    }

    .form-section {
        display: none;
        animation: fadeInUp 0.5s ease-out;
    }

    .form-section.active {
        display: block;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .section-title {
        font-size: 24px;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 30px;
        display: flex;
        align-items: center;
    }

    .section-title i {
        margin-left: 15px;
        color: #667eea;
    }

    .form-navigation {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30px 40px;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
    }

    .btn-wizard {
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 500;
        font-size: 16px;
        transition: var(--transition);
        border: none;
        min-width: 120px;
    }

    .btn-wizard.btn-primary {
        background: var(--primary-gradient);
        color: white;
    }

    .btn-wizard.btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-hover);
    }

    .btn-wizard.btn-outline-secondary {
        border: 2px solid #6c757d;
        color: #6c757d;
        background: white;
    }

    .btn-wizard.btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-2px);
    }

    .btn-wizard.btn-success {
        background: var(--success-gradient);
        color: white;
    }

    .btn-wizard.btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(17, 153, 142, 0.4);
    }

    .form-check-modern {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        background: #f8f9fa;
        border-radius: 12px;
        margin-bottom: 15px;
        transition: var(--transition);
        cursor: pointer;
    }

    .form-check-modern:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }

    .form-check-modern input[type="checkbox"] {
        width: 20px;
        height: 20px;
        margin-left: 15px;
        accent-color: #667eea;
    }

    .form-check-modern label {
        margin: 0;
        font-weight: 500;
        cursor: pointer;
    }

    .progress-container {
        padding: 0 40px 20px;
        background: #f8f9fa;
    }

    .progress-bar-custom {
        height: 6px;
        background: #e9ecef;
        border-radius: 3px;
        overflow: hidden;
    }

    .progress-fill {
        height: 100%;
        background: var(--primary-gradient);
        border-radius: 3px;
        transition: width 0.5s ease;
    }

    .validation-message {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        display: none;
    }

    .validation-message.show {
        display: block;
        animation: slideDown 0.3s ease;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .field-error {
        color: #dc3545;
        font-size: 14px;
        margin-top: 5px;
        display: block;
    }

    .form-control.is-invalid,
    .form-select.is-invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.15);
    }

    .save-indicator {
        position: fixed;
        top: 20px;
        left: 20px;
        background: var(--success-gradient);
        color: white;
        padding: 10px 20px;
        border-radius: 25px;
        font-size: 14px;
        font-weight: 500;
        opacity: 0;
        transform: translateY(-20px);
        transition: var(--transition);
        z-index: 1000;
    }

    .save-indicator.show {
        opacity: 1;
        transform: translateY(0);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .wizard-steps {
            flex-wrap: wrap;
            padding: 20px 10px;
        }

        .wizard-step {
            margin: 10px 5px;
        }

        .step-line {
            display: none;
        }

        .form-content {
            padding: 20px;
        }

        .form-navigation {
            padding: 20px;
            flex-direction: column;
            gap: 15px;
        }

        .btn-wizard {
            width: 100%;
        }
    }
</style>
{% endblock %}
