"""
URL configuration for suppliers app.
"""
from django.urls import path
from . import views

app_name = 'suppliers'

urlpatterns = [
    # Supplier URLs
    path('', views.SupplierListView.as_view(), name='supplier_list'),
    path('suppliers/', views.SupplierListView.as_view(), name='supplier_list'),
    path('suppliers/add/', views.SupplierCreateView.as_view(), name='supplier_add'),
    path('suppliers/<int:pk>/', views.SupplierDetailView.as_view(), name='supplier_detail'),
    path('suppliers/<int:pk>/edit/', views.SupplierUpdateView.as_view(), name='supplier_edit'),
    path('suppliers/<int:pk>/delete/', views.SupplierDeleteView.as_view(), name='supplier_delete'),

    # Contract URLs
    path('contracts/', views.ContractListView.as_view(), name='contract_list'),
    path('contracts/add/', views.ContractCreateView.as_view(), name='contract_add'),
    path('contracts/<int:pk>/', views.ContractDetailView.as_view(), name='contract_detail'),
    path('contracts/<int:pk>/edit/', views.ContractUpdateView.as_view(), name='contract_edit'),
    path('contracts/<int:pk>/renew/', views.ContractRenewView.as_view(), name='contract_renew'),

    # Evaluation URLs
    path('suppliers/<int:supplier_id>/evaluations/', views.EvaluationListView.as_view(), name='evaluation_list'),
    path('suppliers/<int:supplier_id>/evaluations/add/', views.EvaluationCreateView.as_view(), name='evaluation_add'),
    path('evaluations/<int:pk>/edit/', views.EvaluationUpdateView.as_view(), name='evaluation_edit'),

    # Reports
    path('reports/performance/', views.SupplierPerformanceReportView.as_view(), name='performance_report'),
]
