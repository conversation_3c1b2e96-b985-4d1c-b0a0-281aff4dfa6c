{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}{{ title|default:"إدارة الباقة السياحية" }}{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .form-section {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #e9ecef;
    }
    
    .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    
    .form-section h4 {
        color: #495057;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #007bff;
        display: inline-block;
    }
    
    .image-preview {
        max-width: 200px;
        max-height: 150px;
        border-radius: 10px;
        margin-top: 10px;
    }
    
    .btn-action {
        padding: 12px 30px;
        border-radius: 25px;
        margin: 5px;
    }
    
    .required-field {
        color: #dc3545;
    }
    
    .help-text {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-{% if package %}edit{% else %}plus{% endif %} text-primary me-2"></i>
                        {{ title|default:"إدارة الباقة السياحية" }}
                    </h1>
                    <p class="text-muted mb-0">
                        {% if package %}تعديل بيانات الباقة السياحية{% else %}إضافة باقة سياحية جديدة{% endif %}
                    </p>
                </div>
                <div>
                    <a href="{% url 'tours:package_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <form method="post" enctype="multipart/form-data">
        {% csrf_token %}
        
        <div class="row">
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="form-container">
                    <div class="form-section">
                        <h4><i class="fas fa-info-circle text-primary me-2"></i>المعلومات الأساسية</h4>
                        
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.title_ar|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.title_fr|as_crispy_field }}
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.title_en|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.package_code|as_crispy_field }}
                                <div class="help-text">سيتم إنشاء رمز تلقائياً إذا ترك فارغاً</div>
                            </div>
                        </div>
                        
                        {{ form.short_description_ar|as_crispy_field }}
                        {{ form.short_description_fr|as_crispy_field }}
                        {{ form.short_description_en|as_crispy_field }}
                    </div>

                    <!-- Detailed Description -->
                    <div class="form-section">
                        <h4><i class="fas fa-file-alt text-primary me-2"></i>الوصف التفصيلي</h4>
                        {{ form.detailed_description_ar|as_crispy_field }}
                        {{ form.detailed_description_fr|as_crispy_field }}
                        {{ form.detailed_description_en|as_crispy_field }}
                    </div>

                    <!-- Classification -->
                    <div class="form-section">
                        <h4><i class="fas fa-tags text-primary me-2"></i>التصنيف</h4>
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.category|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.destinations|as_crispy_field }}
                            </div>
                        </div>
                    </div>

                    <!-- Duration and Capacity -->
                    <div class="form-section">
                        <h4><i class="fas fa-clock text-primary me-2"></i>المدة والسعة</h4>
                        <div class="row">
                            <div class="col-md-3">
                                {{ form.duration_days|as_crispy_field }}
                            </div>
                            <div class="col-md-3">
                                {{ form.duration_nights|as_crispy_field }}
                            </div>
                            <div class="col-md-3">
                                {{ form.min_participants|as_crispy_field }}
                            </div>
                            <div class="col-md-3">
                                {{ form.max_participants|as_crispy_field }}
                            </div>
                        </div>
                    </div>

                    <!-- Pricing -->
                    <div class="form-section">
                        <h4><i class="fas fa-money-bill text-primary me-2"></i>التسعير</h4>
                        <div class="row">
                            <div class="col-md-4">
                                {{ form.base_price|as_crispy_field }}
                            </div>
                            <div class="col-md-4">
                                {{ form.child_price|as_crispy_field }}
                            </div>
                            <div class="col-md-4">
                                {{ form.infant_price|as_crispy_field }}
                            </div>
                        </div>
                    </div>

                    <!-- Characteristics -->
                    <div class="form-section">
                        <h4><i class="fas fa-chart-line text-primary me-2"></i>الخصائص</h4>
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.difficulty_level|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.physical_rating|as_crispy_field }}
                            </div>
                        </div>
                    </div>

                    <!-- Inclusions and Exclusions -->
                    <div class="form-section">
                        <h4><i class="fas fa-list text-primary me-2"></i>المشمول وغير المشمول</h4>
                        {{ form.inclusions|as_crispy_field }}
                        {{ form.exclusions|as_crispy_field }}
                    </div>

                    <!-- Requirements -->
                    <div class="form-section">
                        <h4><i class="fas fa-exclamation-triangle text-primary me-2"></i>المتطلبات</h4>
                        {{ form.requirements|as_crispy_field }}
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.age_restrictions|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.fitness_level|as_crispy_field }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Media -->
                <div class="form-container">
                    <div class="form-section">
                        <h4><i class="fas fa-images text-primary me-2"></i>الوسائط</h4>
                        {{ form.main_image|as_crispy_field }}
                        {% if package.main_image %}
                            <img src="{{ package.main_image.url }}" class="image-preview" alt="الصورة الحالية">
                        {% endif %}
                        {{ form.video_url|as_crispy_field }}
                    </div>

                    <!-- Status -->
                    <div class="form-section">
                        <h4><i class="fas fa-toggle-on text-primary me-2"></i>الحالة</h4>
                        <div class="form-check mb-3">
                            {{ form.is_active }}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                نشط
                            </label>
                        </div>
                        <div class="form-check mb-3">
                            {{ form.is_featured }}
                            <label class="form-check-label" for="{{ form.is_featured.id_for_label }}">
                                مميز
                            </label>
                        </div>
                        <div class="form-check mb-3">
                            {{ form.is_bestseller }}
                            <label class="form-check-label" for="{{ form.is_bestseller.id_for_label }}">
                                الأكثر مبيعاً
                            </label>
                        </div>
                    </div>

                    <!-- Availability -->
                    <div class="form-section">
                        <h4><i class="fas fa-calendar text-primary me-2"></i>التوفر</h4>
                        {{ form.available_from|as_crispy_field }}
                        {{ form.available_to|as_crispy_field }}
                    </div>

                    <!-- SEO -->
                    <div class="form-section">
                        <h4><i class="fas fa-search text-primary me-2"></i>تحسين محركات البحث</h4>
                        {{ form.meta_title|as_crispy_field }}
                        {{ form.meta_description|as_crispy_field }}
                        {{ form.keywords|as_crispy_field }}
                    </div>

                    <!-- Internal Notes -->
                    <div class="form-section">
                        <h4><i class="fas fa-sticky-note text-primary me-2"></i>ملاحظات داخلية</h4>
                        {{ form.notes|as_crispy_field }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="row">
            <div class="col-12">
                <div class="form-container text-center">
                    <button type="submit" class="btn btn-primary btn-action">
                        <i class="fas fa-save me-2"></i>
                        {% if package %}تحديث الباقة{% else %}إنشاء الباقة{% endif %}
                    </button>
                    <a href="{% url 'tours:package_list' %}" class="btn btn-secondary btn-action">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </a>
                    {% if package %}
                    <a href="{% url 'tours:package_detail' package.pk %}" class="btn btn-info btn-action">
                        <i class="fas fa-eye me-2"></i>عرض الباقة
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-generate package code if empty
    $('#id_title_ar').on('blur', function() {
        if (!$('#id_package_code').val()) {
            // Simple code generation based on title
            var title = $(this).val();
            if (title) {
                var code = 'PKG' + Math.random().toString(36).substr(2, 6).toUpperCase();
                $('#id_package_code').val(code);
            }
        }
    });

    // Form validation
    $('form').on('submit', function(e) {
        var isValid = true;
        var errors = [];

        // Check required fields
        if (!$('#id_title_ar').val()) {
            errors.push('العنوان بالعربية مطلوب');
            isValid = false;
        }

        if (!$('#id_base_price').val()) {
            errors.push('السعر الأساسي مطلوب');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
            alert('يرجى تصحيح الأخطاء التالية:\n' + errors.join('\n'));
        }
    });

    console.log('Package form loaded');
});
</script>
{% endblock %}
