"""
Email handling for accounts app.
"""
from typing import Any, Dict
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.conf import settings

User = get_user_model()


def send_password_reset_email(user: User, reset_url: str) -> None:
    """Send password reset email to user."""
    context = {
        'user': user,
        'reset_url': reset_url,
        'site_name': settings.SITE_NAME,
    }

    subject = render_to_string(
        'accounts/emails/password_reset_subject.txt',
        context
    ).strip()

    message = render_to_string(
        'accounts/emails/password_reset_email.txt',
        context
    )

    html_message = render_to_string(
        'accounts/emails/password_reset_email.html',
        context
    )

    send_mail(
        subject=subject,
        message=message,
        from_email=settings.DEFAULT_FROM_EMAIL,
        recipient_list=[user.email],
        html_message=html_message
    )
