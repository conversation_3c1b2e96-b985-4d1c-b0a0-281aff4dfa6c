# Generated by Django 4.2.7 on 2025-06-12 17:35

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Destination',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('name_ar', models.CharField(max_length=100, verbose_name='الاسم بالعربية')),
                ('name_fr', models.Char<PERSON>ield(max_length=100, verbose_name='الاسم بالفرنسية')),
                ('name_en', models.CharField(max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('description_ar', models.TextField(blank=True, verbose_name='الوصف بالعربية')),
                ('description_fr', models.TextField(blank=True, verbose_name='الوصف بالفرنسية')),
                ('description_en', models.TextField(blank=True, verbose_name='الوصف بالإنجليزية')),
                ('latitude', models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True, verbose_name='خط العرض')),
                ('longitude', models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True, verbose_name='خط الطول')),
                ('main_image', models.ImageField(blank=True, upload_to='destinations/', verbose_name='الصورة الرئيسية')),
                ('gallery', models.JSONField(blank=True, default=list, verbose_name='معرض الصور')),
                ('climate', models.CharField(blank=True, max_length=50, verbose_name='المناخ')),
                ('best_time_to_visit', models.CharField(blank=True, max_length=100, verbose_name='أفضل وقت للزيارة')),
                ('activities', models.TextField(blank=True, verbose_name='الأنشطة المتاحة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_featured', models.BooleanField(default=False, verbose_name='مميز')),
                ('popularity_score', models.PositiveIntegerField(default=0, verbose_name='نقاط الشعبية')),
                ('city', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.city', verbose_name='المدينة')),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.country', verbose_name='الدولة')),
            ],
            options={
                'verbose_name': 'وجهة سياحية',
                'verbose_name_plural': 'الوجهات السياحية',
                'ordering': ['name_ar'],
            },
        ),
        migrations.CreateModel(
            name='TourCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('name_ar', models.CharField(max_length=50, verbose_name='الاسم بالعربية')),
                ('name_fr', models.CharField(max_length=50, verbose_name='الاسم بالفرنسية')),
                ('name_en', models.CharField(max_length=50, verbose_name='الاسم بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('icon', models.CharField(blank=True, max_length=50, verbose_name='الأيقونة')),
                ('color', models.CharField(default='#007bff', max_length=7, verbose_name='اللون')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='ترتيب العرض')),
            ],
            options={
                'verbose_name': 'فئة الباقة',
                'verbose_name_plural': 'فئات الباقات',
                'ordering': ['sort_order', 'name_ar'],
            },
        ),
        migrations.CreateModel(
            name='TourPackage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('package_code', models.CharField(max_length=20, unique=True, verbose_name='رمز الباقة')),
                ('title_ar', models.CharField(max_length=200, verbose_name='العنوان بالعربية')),
                ('title_fr', models.CharField(max_length=200, verbose_name='العنوان بالفرنسية')),
                ('title_en', models.CharField(max_length=200, verbose_name='العنوان بالإنجليزية')),
                ('short_description_ar', models.TextField(max_length=500, verbose_name='وصف مختصر بالعربية')),
                ('short_description_fr', models.TextField(max_length=500, verbose_name='وصف مختصر بالفرنسية')),
                ('short_description_en', models.TextField(max_length=500, verbose_name='وصف مختصر بالإنجليزية')),
                ('detailed_description_ar', models.TextField(verbose_name='وصف مفصل بالعربية')),
                ('detailed_description_fr', models.TextField(verbose_name='وصف مفصل بالفرنسية')),
                ('detailed_description_en', models.TextField(verbose_name='وصف مفصل بالإنجليزية')),
                ('duration_days', models.PositiveIntegerField(verbose_name='المدة بالأيام')),
                ('duration_nights', models.PositiveIntegerField(verbose_name='عدد الليالي')),
                ('base_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='السعر الأساسي')),
                ('child_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='سعر الطفل')),
                ('infant_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='سعر الرضيع')),
                ('min_participants', models.PositiveIntegerField(default=1, verbose_name='الحد الأدنى للمشاركين')),
                ('max_participants', models.PositiveIntegerField(verbose_name='الحد الأقصى للمشاركين')),
                ('difficulty_level', models.CharField(choices=[('easy', 'سهل'), ('moderate', 'متوسط'), ('challenging', 'صعب'), ('extreme', 'شديد الصعوبة')], default='easy', max_length=20, verbose_name='مستوى الصعوبة')),
                ('physical_rating', models.PositiveIntegerField(default=1, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='التقييم البدني')),
                ('inclusions', models.TextField(verbose_name='المشمول في الباقة')),
                ('exclusions', models.TextField(verbose_name='غير المشمول في الباقة')),
                ('requirements', models.TextField(blank=True, verbose_name='المتطلبات')),
                ('age_restrictions', models.CharField(blank=True, max_length=100, verbose_name='قيود العمر')),
                ('fitness_level', models.CharField(blank=True, max_length=100, verbose_name='مستوى اللياقة المطلوب')),
                ('main_image', models.ImageField(blank=True, upload_to='packages/', verbose_name='الصورة الرئيسية')),
                ('gallery', models.JSONField(blank=True, default=list, verbose_name='معرض الصور')),
                ('video_url', models.URLField(blank=True, verbose_name='رابط الفيديو')),
                ('meta_title', models.CharField(blank=True, max_length=60, verbose_name='عنوان SEO')),
                ('meta_description', models.CharField(blank=True, max_length=160, verbose_name='وصف SEO')),
                ('keywords', models.CharField(blank=True, max_length=200, verbose_name='الكلمات المفتاحية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_featured', models.BooleanField(default=False, verbose_name='مميز')),
                ('is_bestseller', models.BooleanField(default=False, verbose_name='الأكثر مبيعاً')),
                ('available_from', models.DateField(blank=True, null=True, verbose_name='متاح من')),
                ('available_to', models.DateField(blank=True, null=True, verbose_name='متاح حتى')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات داخلية')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='tours.tourcategory', verbose_name='الفئة')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('destinations', models.ManyToManyField(to='tours.destination', verbose_name='الوجهات')),
            ],
            options={
                'verbose_name': 'باقة سياحية',
                'verbose_name_plural': 'الباقات السياحية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PackageAvailability',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('adult_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='سعر البالغ')),
                ('child_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='سعر الطفل')),
                ('infant_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='سعر الرضيع')),
                ('available_spots', models.PositiveIntegerField(verbose_name='الأماكن المتاحة')),
                ('reserved_spots', models.PositiveIntegerField(default=0, verbose_name='الأماكن المحجوزة')),
                ('is_available', models.BooleanField(default=True, verbose_name='متاح')),
                ('is_guaranteed', models.BooleanField(default=False, verbose_name='مضمون الانطلاق')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الخصم')),
                ('early_bird_discount', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='خصم الحجز المبكر')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availabilities', to='tours.tourpackage', verbose_name='الباقة')),
            ],
            options={
                'verbose_name': 'توفر الباقة',
                'verbose_name_plural': 'توفر الباقات',
                'ordering': ['start_date'],
            },
        ),
        migrations.CreateModel(
            name='Itinerary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('day_number', models.PositiveIntegerField(verbose_name='رقم اليوم')),
                ('title_ar', models.CharField(max_length=200, verbose_name='عنوان اليوم بالعربية')),
                ('title_fr', models.CharField(max_length=200, verbose_name='عنوان اليوم بالفرنسية')),
                ('title_en', models.CharField(max_length=200, verbose_name='عنوان اليوم بالإنجليزية')),
                ('description_ar', models.TextField(verbose_name='وصف اليوم بالعربية')),
                ('description_fr', models.TextField(verbose_name='وصف اليوم بالفرنسية')),
                ('description_en', models.TextField(verbose_name='وصف اليوم بالإنجليزية')),
                ('accommodation', models.CharField(blank=True, max_length=200, verbose_name='الإقامة')),
                ('breakfast_included', models.BooleanField(default=False, verbose_name='الإفطار مشمول')),
                ('lunch_included', models.BooleanField(default=False, verbose_name='الغداء مشمول')),
                ('dinner_included', models.BooleanField(default=False, verbose_name='العشاء مشمول')),
                ('activities', models.TextField(blank=True, verbose_name='الأنشطة')),
                ('optional_activities', models.TextField(blank=True, verbose_name='الأنشطة الاختيارية')),
                ('image', models.ImageField(blank=True, upload_to='itinerary/', verbose_name='صورة اليوم')),
                ('location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='tours.destination', verbose_name='الموقع')),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='itineraries', to='tours.tourpackage', verbose_name='الباقة')),
            ],
            options={
                'verbose_name': 'برنامج يومي',
                'verbose_name_plural': 'البرامج اليومية',
                'ordering': ['package', 'day_number'],
            },
        ),
        migrations.AddIndex(
            model_name='tourpackage',
            index=models.Index(fields=['package_code'], name='tours_tourp_package_8da087_idx'),
        ),
        migrations.AddIndex(
            model_name='tourpackage',
            index=models.Index(fields=['is_active', 'is_featured'], name='tours_tourp_is_acti_6f0781_idx'),
        ),
        migrations.AddIndex(
            model_name='tourpackage',
            index=models.Index(fields=['category'], name='tours_tourp_categor_487315_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='packageavailability',
            unique_together={('package', 'start_date')},
        ),
        migrations.AlterUniqueTogether(
            name='itinerary',
            unique_together={('package', 'day_number')},
        ),
    ]
