"""
Admin configuration for CRM models.
"""
from django.contrib import admin
from django.utils.html import format_html
from .models import Client, ClientContact, ClientCommunication, ClientPreference


class ClientContactInline(admin.TabularInline):
    model = ClientContact
    extra = 1


class ClientCommunicationInline(admin.TabularInline):
    model = ClientCommunication
    extra = 0
    readonly_fields = ('created_at',)
    fields = ('communication_type', 'direction', 'subject', 'staff_member', 'communication_date', 'follow_up_required')


class ClientPreferenceInline(admin.StackedInline):
    model = ClientPreference
    can_delete = False


@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    inlines = [ClientContactInline, ClientCommunicationInline, ClientPreferenceInline]
    
    list_display = (
        'client_code', 'full_name_ar', 'email', 'phone', 'client_type', 
        'vip_status', 'loyalty_points', 'assigned_agent', 'created_at'
    )
    list_filter = (
        'client_type', 'vip_status', 'nationality', 'preferred_language', 
        'marketing_consent', 'assigned_agent', 'created_at'
    )
    search_fields = (
        'client_code', 'first_name_ar', 'last_name_ar', 'first_name_fr', 'last_name_fr',
        'email', 'phone', 'passport_number', 'national_id'
    )
    readonly_fields = ('client_code', 'created_at', 'updated_at')
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('client_code', 'client_type', 'assigned_agent')
        }),
        ('الاسم', {
            'fields': (
                ('first_name_ar', 'last_name_ar'),
                ('first_name_fr', 'last_name_fr')
            )
        }),
        ('معلومات الاتصال', {
            'fields': (
                ('email', 'phone'),
                ('whatsapp', 'secondary_phone')
            )
        }),
        ('معلومات شخصية', {
            'fields': (
                ('gender', 'date_of_birth', 'nationality'),
                ('passport_number', 'passport_expiry'),
                'national_id'
            )
        }),
        ('العنوان', {
            'fields': ('address', 'city', 'postal_code')
        }),
        ('معلومات الشركة', {
            'fields': ('company_name', 'tax_number'),
            'classes': ('collapse',)
        }),
        ('التفضيلات', {
            'fields': (
                'preferred_language', 'special_requirements', 'dietary_restrictions'
            )
        }),
        ('برنامج الولاء', {
            'fields': ('loyalty_points', 'vip_status')
        }),
        ('التسويق', {
            'fields': ('marketing_consent', 'newsletter_subscription')
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).filter(is_deleted=False)


@admin.register(ClientCommunication)
class ClientCommunicationAdmin(admin.ModelAdmin):
    list_display = (
        'client', 'communication_type', 'direction', 'subject', 
        'staff_member', 'communication_date', 'follow_up_required'
    )
    list_filter = (
        'communication_type', 'direction', 'follow_up_required', 
        'communication_date', 'staff_member'
    )
    search_fields = ('client__first_name_ar', 'client__last_name_ar', 'subject', 'content')
    date_hierarchy = 'communication_date'
    
    fieldsets = (
        ('معلومات التواصل', {
            'fields': (
                'client', 'communication_type', 'direction', 
                'staff_member', 'communication_date'
            )
        }),
        ('المحتوى', {
            'fields': ('subject', 'content', 'attachment')
        }),
        ('المتابعة', {
            'fields': ('follow_up_required', 'follow_up_date')
        })
    )


@admin.register(ClientContact)
class ClientContactAdmin(admin.ModelAdmin):
    list_display = ('client', 'first_name', 'last_name', 'contact_type', 'relationship', 'phone')
    list_filter = ('contact_type',)
    search_fields = ('client__first_name_ar', 'client__last_name_ar', 'first_name', 'last_name')


@admin.register(ClientPreference)
class ClientPreferenceAdmin(admin.ModelAdmin):
    list_display = (
        'client', 'preferred_accommodation_type', 'travel_style', 
        'budget_range_min', 'budget_range_max'
    )
    list_filter = ('preferred_accommodation_type', 'travel_style', 'travels_with_children')
    search_fields = ('client__first_name_ar', 'client__last_name_ar')
    filter_horizontal = ('preferred_destinations',)
