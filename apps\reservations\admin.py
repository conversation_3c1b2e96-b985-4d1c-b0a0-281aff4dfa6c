"""
Django admin configuration for Reservations app.
"""
from django.contrib import admin
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from .models import Reservation, ReservationParticipant, ReservationService, ReservationDocument


class ReservationParticipantInline(admin.TabularInline):
    """Inline admin for Reservation participants."""
    model = ReservationParticipant
    extra = 1
    fields = ['first_name', 'last_name', 'participant_type', 'date_of_birth', 'passport_number']


class ReservationServiceInline(admin.TabularInline):
    """Inline admin for Reservation services."""
    model = ReservationService
    extra = 0
    fields = ['service_type', 'name', 'unit_price', 'quantity', 'total_price', 'is_confirmed']
    readonly_fields = ['total_price']


class ReservationDocumentInline(admin.TabularInline):
    """Inline admin for Reservation documents."""
    model = ReservationDocument
    extra = 0
    fields = ['document_type', 'title', 'file', 'is_public']


@admin.register(Reservation)
class ReservationAdmin(admin.ModelAdmin):
    """Admin interface for Reservation model."""
    list_display = ['reservation_number', 'client', 'package', 'departure_date', 'total_participants', 'total_amount', 'status', 'payment_status']
    list_filter = ['status', 'payment_status', 'departure_date', 'booking_date', 'package__category']
    search_fields = ['reservation_number', 'client__first_name_ar', 'client__last_name_ar', 'client__email', 'package__title_ar']
    list_editable = ['status', 'payment_status']
    readonly_fields = ['reservation_number', 'total_participants', 'paid_amount', 'remaining_amount', 'booking_date', 'created_at', 'updated_at']
    inlines = [ReservationParticipantInline, ReservationServiceInline, ReservationDocumentInline]
    date_hierarchy = 'departure_date'

    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('reservation_number', 'client', 'package', 'availability')
        }),
        (_('التواريخ'), {
            'fields': ('departure_date', 'return_date', 'booking_date')
        }),
        (_('المشاركون'), {
            'fields': ('adults', 'children', 'infants', 'total_participants')
        }),
        (_('الأسعار'), {
            'fields': ('adult_price', 'child_price', 'infant_price')
        }),
        (_('المبالغ'), {
            'fields': ('subtotal', 'discount_amount', 'tax_amount', 'total_amount')
        }),
        (_('الدفع'), {
            'fields': ('paid_amount', 'remaining_amount'),
            'classes': ('collapse',)
        }),
        (_('الحالة'), {
            'fields': ('status', 'payment_status')
        }),
        (_('المتطلبات الخاصة'), {
            'fields': ('special_requests', 'dietary_requirements', 'accessibility_needs'),
            'classes': ('collapse',)
        }),
        (_('معلومات داخلية'), {
            'fields': ('sales_agent', 'internal_notes'),
            'classes': ('collapse',)
        }),
        (_('التأكيد'), {
            'fields': ('confirmation_sent', 'confirmation_sent_at'),
            'classes': ('collapse',)
        }),
        (_('معلومات النظام'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        """Set sales_agent field when creating new reservation."""
        if not change:
            obj.sales_agent = request.user
        super().save_model(request, obj, form, change)


@admin.register(ReservationParticipant)
class ReservationParticipantAdmin(admin.ModelAdmin):
    """Admin interface for ReservationParticipant model."""
    list_display = ['full_name', 'reservation', 'participant_type', 'age', 'nationality', 'passport_number']
    list_filter = ['participant_type', 'gender', 'nationality', 'reservation__departure_date']
    search_fields = ['first_name', 'last_name', 'passport_number', 'reservation__reservation_number']
    readonly_fields = ['age', 'created_at', 'updated_at']

    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('reservation', 'participant_type')
        }),
        (_('المعلومات الشخصية'), {
            'fields': ('first_name', 'last_name', 'date_of_birth', 'age', 'gender', 'nationality')
        }),
        (_('الوثائق'), {
            'fields': ('passport_number', 'passport_expiry')
        }),
        (_('المتطلبات الخاصة'), {
            'fields': ('dietary_restrictions', 'medical_conditions', 'special_assistance'),
            'classes': ('collapse',)
        }),
        (_('معلومات النظام'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ReservationService)
class ReservationServiceAdmin(admin.ModelAdmin):
    """Admin interface for ReservationService model."""
    list_display = ['name', 'reservation', 'service_type', 'unit_price', 'quantity', 'total_price', 'is_confirmed']
    list_filter = ['service_type', 'is_confirmed', 'is_optional', 'service_date']
    search_fields = ['name', 'description', 'reservation__reservation_number']
    list_editable = ['is_confirmed']
    readonly_fields = ['total_price', 'created_at', 'updated_at']

    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('reservation', 'service_type', 'name', 'description')
        }),
        (_('الأسعار'), {
            'fields': ('unit_price', 'quantity', 'total_price')
        }),
        (_('التاريخ'), {
            'fields': ('service_date',)
        }),
        (_('الحالة'), {
            'fields': ('is_confirmed', 'is_optional')
        }),
        (_('معلومات النظام'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ReservationDocument)
class ReservationDocumentAdmin(admin.ModelAdmin):
    """Admin interface for ReservationDocument model."""
    list_display = ['title', 'reservation', 'document_type', 'file_size_display', 'uploaded_by', 'is_public']
    list_filter = ['document_type', 'is_public', 'created_at']
    search_fields = ['title', 'description', 'reservation__reservation_number']
    list_editable = ['is_public']
    readonly_fields = ['file_size', 'created_at', 'updated_at']

    def file_size_display(self, obj):
        """Display file size in human readable format."""
        if obj.file_size:
            if obj.file_size < 1024:
                return f"{obj.file_size} B"
            elif obj.file_size < 1024 * 1024:
                return f"{obj.file_size / 1024:.1f} KB"
            else:
                return f"{obj.file_size / (1024 * 1024):.1f} MB"
        return "-"
    file_size_display.short_description = _('حجم الملف')

    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('reservation', 'document_type', 'title', 'description')
        }),
        (_('الملف'), {
            'fields': ('file', 'file_size')
        }),
        (_('الإعدادات'), {
            'fields': ('is_public',)
        }),
        (_('معلومات النظام'), {
            'fields': ('uploaded_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        """Set uploaded_by field when creating new document."""
        if not change:
            obj.uploaded_by = request.user
        super().save_model(request, obj, form, change)
