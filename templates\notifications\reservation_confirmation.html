<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تأكيد الحجز {{ reservation.reservation_number }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }

        .email-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header p {
            margin: 5px 0 0 0;
            opacity: 0.9;
        }

        .reservation-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
            border-right: 4px solid #667eea;
        }

        .package-showcase {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 25px;
            text-align: center;
        }

        .package-showcase h2 {
            color: #667eea;
            margin: 0 0 10px 0;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 25px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-label {
            font-weight: 600;
            color: #6c757d;
        }

        .detail-value {
            color: #495057;
        }

        .amount-highlight {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 25px 0;
        }

        .amount-highlight h2 {
            color: #667eea;
            margin: 0;
            font-size: 28px;
        }

        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            background: #d1edff;
            color: #0c63e4;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            margin: 20px 0;
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }

        .footer {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-top: 30px;
            color: #6c757d;
            font-size: 14px;
        }

        .footer a {
            color: #667eea;
            text-decoration: none;
        }

        .company-info {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }

        .checklist {
            background: #fff3cd;
            padding: 20px;
            border-radius: 10px;
            margin: 25px 0;
            border-right: 4px solid #ffc107;
        }

        .checklist h4 {
            color: #856404;
            margin-top: 0;
        }

        .checklist ul {
            color: #856404;
            margin: 0;
            padding-right: 20px;
        }

        @media (max-width: 600px) {
            .detail-grid {
                grid-template-columns: 1fr;
            }

            .email-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>🎉 تم تأكيد حجزكم!</h1>
            <p>وكالة السفر المغربية</p>
        </div>

        <!-- Greeting -->
        <div style="margin-bottom: 25px;">
            <h2 style="color: #495057; margin-bottom: 10px;">مرحباً {{ client_name }}،</h2>
            <p style="color: #6c757d; font-size: 16px; line-height: 1.6;">
                يسعدنا إبلاغكم بأنه تم تأكيد حجزكم بنجاح! نحن متحمسون لتقديم تجربة سفر لا تُنسى لكم.
            </p>
        </div>

        <!-- Package Showcase -->
        <div class="package-showcase">
            <h2>{{ reservation.package.title_ar }}</h2>
            <p style="color: #6c757d; margin: 10px 0;">{{ reservation.package.category.name_ar }}</p>
            <div style="font-size: 18px; color: #667eea; font-weight: 500;">
                {{ reservation.package.duration }} أيام من المتعة والاستكشاف
            </div>
        </div>

        <!-- Reservation Details -->
        <div class="reservation-info">
            <h3 style="margin-top: 0; color: #667eea;">تفاصيل الحجز</h3>
            <div class="detail-grid">
                <div>
                    <div class="detail-item">
                        <span class="detail-label">رقم الحجز:</span>
                        <span class="detail-value">{{ reservation.reservation_number }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">تاريخ المغادرة:</span>
                        <span class="detail-value">{{ reservation.departure_date }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">تاريخ العودة:</span>
                        <span class="detail-value">{{ reservation.return_date|default:"مفتوح" }}</span>
                    </div>
                </div>
                <div>
                    <div class="detail-item">
                        <span class="detail-label">عدد البالغين:</span>
                        <span class="detail-value">{{ reservation.adults }} شخص</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">عدد الأطفال:</span>
                        <span class="detail-value">{{ reservation.children }} طفل</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">الحالة:</span>
                        <span class="status-badge">{{ reservation.get_status_display }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Amount -->
        <div class="amount-highlight">
            <p style="margin: 0; color: #6c757d; font-size: 16px;">المبلغ الإجمالي</p>
            <h2>{{ reservation.total_amount }} درهم</h2>
            <p style="margin: 0; color: #6c757d; font-size: 14px;">
                {% if reservation.paid_amount > 0 %}
                تم دفع {{ reservation.paid_amount }} درهم - المتبقي {{ reservation.remaining_amount }} درهم
                {% else %}
                في انتظار الدفع
                {% endif %}
            </p>
        </div>

        <!-- Pre-Travel Checklist -->
        <div class="checklist">
            <h4>📋 قائمة التحضيرات قبل السفر</h4>
            <ul>
                <li>تأكد من صلاحية جواز السفر (6 أشهر على الأقل)</li>
                <li>احصل على التأشيرة المطلوبة إن وجدت</li>
                <li>احجز تأمين السفر</li>
                <li>تحقق من التطعيمات المطلوبة</li>
                <li>احزم الأمتعة وفقاً لقوانين الطيران</li>
                <li>احتفظ بنسخ من الوثائق المهمة</li>
            </ul>
        </div>

        <!-- Call to Action -->
        <div style="text-align: center; margin: 30px 0;">
            <a href="#" class="cta-button">
                📱 تحميل تطبيق الوكالة
            </a>
        </div>

        <!-- Important Information -->
        <div style="background: #d1ecf1; padding: 20px; border-radius: 10px; margin: 25px 0; border-right: 4px solid #17a2b8;">
            <h4 style="margin-top: 0; color: #0c5460;">معلومات مهمة</h4>
            <div style="color: #0c5460;">
                <p><strong>سياسة الإلغاء:</strong> يمكن إلغاء الحجز حتى 48 ساعة قبل موعد المغادرة</p>
                <p><strong>التواصل الطارئ:</strong> +212 6 XX XX XX XX (متاح 24/7)</p>
                <p><strong>نقطة التجمع:</strong> سيتم إرسال التفاصيل قبل يوم من المغادرة</p>
            </div>
        </div>

        <!-- Contact Information -->
        <div style="background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%); padding: 20px; border-radius: 10px; margin: 25px 0;">
            <h4 style="margin-top: 0; color: #667eea;">تواصل معنا</h4>
            <p style="color: #6c757d; margin-bottom: 15px;">
                فريقنا متاح لمساعدتكم في أي استفسارات أو طلبات خاصة.
            </p>
            <div style="color: #6c757d;">
                <p style="margin: 5px 0;">📞 الهاتف: +212 5 22 XX XX XX</p>
                <p style="margin: 5px 0;">📧 البريد: <EMAIL></p>
                <p style="margin: 5px 0;">💬 واتساب: +212 6 XX XX XX XX</p>
                <p style="margin: 5px 0;">🕒 ساعات العمل: 9:00 - 18:00 (الاثنين - الجمعة)</p>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p style="margin: 0; font-weight: 600;">شكراً لاختياركم وكالة السفر المغربية</p>
            <p style="margin: 5px 0 0 0;">نتطلع لتقديم تجربة سفر استثنائية لكم</p>

            <div class="company-info">
                <p style="margin: 0;"><strong>وكالة السفر المغربية</strong></p>
                <p style="margin: 5px 0;">العنوان: الدار البيضاء، المغرب</p>
                <p style="margin: 5px 0;">
                    الموقع: <a href="https://moroccantravel.ma">www.moroccantravel.ma</a>
                </p>
                <p style="margin: 5px 0; font-size: 12px; color: #adb5bd;">
                    هذا البريد الإلكتروني تم إرساله تلقائياً، يرجى عدم الرد عليه مباشرة.
                </p>
            </div>
        </div>
    </div>
</body>
</html>
