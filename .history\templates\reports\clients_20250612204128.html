{% load i18n %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% trans "تقارير العملاء" %} - نظام إدارة وكالة السفر المغربية</title>

    <!-- Bootstrap CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background-color: #f8f9fa;
        }
        .report-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            text-align: center;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/reports/">
                <i class="fas fa-users me-2"></i>
                {% trans "تقارير العملاء" %}
            </a>

            <div class="navbar-nav me-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-home me-1"></i>
                    {% trans "الرئيسية" %}
                </a>
                <a class="nav-link" href="/reports/">
                    <i class="fas fa-chart-bar me-1"></i>
                    {% trans "التقارير" %}
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-4">
                    <i class="fas fa-users me-2"></i>
                    {% trans "تقارير العملاء المفصلة" %}
                </h1>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="report-card">
                    <div class="h2">{{ total_clients }}</div>
                    <div>{% trans "إجمالي العملاء" %}</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="report-card">
                    <div class="h2">{{ vip_distribution.vip }}</div>
                    <div>{% trans "عملاء VIP" %}</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="report-card">
                    <div class="h2">{{ gender_distribution.male }}</div>
                    <div>{% trans "ذكور" %}</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="report-card">
                    <div class="h2">{{ gender_distribution.female }}</div>
                    <div>{% trans "إناث" %}</div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <!-- Gender Distribution -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-venus-mars me-2"></i>
                            {% trans "توزيع العملاء حسب الجنس" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="genderChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- VIP Status -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-star me-2"></i>
                            {% trans "توزيع العملاء حسب الحالة" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="vipChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Age Groups -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-birthday-cake me-2"></i>
                            {% trans "توزيع العملاء حسب الفئة العمرية" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="ageChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loyalty Stats -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">{% trans "إحصائيات نقاط الولاء" %}</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <h4 class="text-primary">{{ loyalty_stats.avg_points|floatformat:0|default:"0" }}</h4>
                            <small>{% trans "متوسط النقاط" %}</small>
                        </div>
                        <div class="text-center mb-3">
                            <h4 class="text-success">{{ loyalty_stats.total_points|default:"0" }}</h4>
                            <small>{% trans "إجمالي النقاط" %}</small>
                        </div>
                        <div class="text-center">
                            <h4 class="text-warning">{{ loyalty_stats.max_points|default:"0" }}</h4>
                            <small>{% trans "أعلى نقاط" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Clients -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-trophy me-2"></i>
                            {% trans "أفضل العملاء حسب نقاط الولاء" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>{% trans "الترتيب" %}</th>
                                        <th>{% trans "الاسم" %}</th>
                                        <th>{% trans "النوع" %}</th>
                                        <th>{% trans "نقاط الولاء" %}</th>
                                        <th>{% trans "الحالة" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for client in top_clients_by_points %}
                                    <tr>
                                        <td>
                                            {% if forloop.counter == 1 %}
                                                <i class="fas fa-trophy text-warning"></i>
                                            {% elif forloop.counter == 2 %}
                                                <i class="fas fa-medal text-secondary"></i>
                                            {% elif forloop.counter == 3 %}
                                                <i class="fas fa-award text-warning"></i>
                                            {% else %}
                                                {{ forloop.counter }}
                                            {% endif %}
                                        </td>
                                        <td>{{ client.full_name_ar }}</td>
                                        <td>
                                            {% if client.client_type == 'individual' %}
                                                <span class="badge bg-primary">{% trans "فردي" %}</span>
                                            {% else %}
                                                <span class="badge bg-success">{% trans "شركة" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <strong>{{ client.loyalty_points }}</strong>
                                        </td>
                                        <td>
                                            {% if client.vip_status %}
                                                <span class="badge bg-warning">VIP</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{% trans "عادي" %}</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="5" class="text-center">{% trans "لا توجد بيانات" %}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Charts -->
    <script>
        // Gender Chart
        const genderCtx = document.getElementById('genderChart').getContext('2d');
        new Chart(genderCtx, {
            type: 'doughnut',
            data: {
                labels: ['ذكور', 'إناث', 'أخرى'],
                datasets: [{
                    data: [{{ gender_distribution.male }}, {{ gender_distribution.female }}, {{ gender_distribution.other }}],
                    backgroundColor: ['#36A2EB', '#FF6384', '#FFCE56']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // VIP Chart
        const vipCtx = document.getElementById('vipChart').getContext('2d');
        new Chart(vipCtx, {
            type: 'pie',
            data: {
                labels: ['VIP', 'عادي'],
                datasets: [{
                    data: [{{ vip_distribution.vip }}, {{ vip_distribution.regular }}],
                    backgroundColor: ['#FFD700', '#C0C0C0']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // Age Chart
        const ageCtx = document.getElementById('ageChart').getContext('2d');
        new Chart(ageCtx, {
            type: 'bar',
            data: {
                labels: ['18-25', '26-35', '36-50', '50+'],
                datasets: [{
                    label: 'عدد العملاء',
                    data: [{{ age_groups.18-25 }}, {{ age_groups.26-35 }}, {{ age_groups.36-50 }}, {{ age_groups.50+ }}],
                    backgroundColor: '#4BC0C0'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>
