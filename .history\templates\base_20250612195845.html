{% load i18n %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة وكالة السفر المغربية{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% load static %}{% static 'css/custom.css' %}">

    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Noto Sans Arabic', sans-serif; }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    {% load i18n %}

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'core:dashboard' %}">
                <i class="fas fa-plane me-2"></i>
                {% trans "نظام إدارة وكالة السفر" %}
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'core:dashboard' %}">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            {% trans "لوحة التحكم" %}
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-users me-1"></i>
                            {% trans "العملاء" %}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'crm:client_list' %}">{% trans "قائمة العملاء" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'crm:client_add' %}">{% trans "إضافة عميل جديد" %}</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-map-marked-alt me-1"></i>
                            {% trans "الباقات السياحية" %}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'tours:package_list' %}">{% trans "قائمة الباقات" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'tours:package_add' %}">{% trans "إضافة باقة جديدة" %}</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'tours:destination_list' %}">{% trans "الوجهات السياحية" %}</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-calendar-check me-1"></i>
                            {% trans "الحجوزات" %}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'reservations:reservation_list' %}">{% trans "قائمة الحجوزات" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'reservations:reservation_add' %}">{% trans "حجز جديد" %}</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-bar me-1"></i>
                            {% trans "التقارير" %}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'reports:sales_report' %}">{% trans "تقرير المبيعات" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'reports:client_report' %}">{% trans "تقرير العملاء" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'reports:financial_report' %}">{% trans "التقرير المالي" %}</a></li>
                        </ul>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <!-- Language Switcher -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-globe me-1"></i>
                            العربية
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">العربية</a></li>
                            <li><a class="dropdown-item" href="#">Français</a></li>
                            <li><a class="dropdown-item" href="#">English</a></li>
                        </ul>
                    </li>

                    <!-- User Menu -->
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            {{ user.full_name_ar|default:user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">{% trans "الملف الشخصي" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'core:settings' %}">{% trans "الإعدادات" %}</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'accounts:logout' %}">{% trans "تسجيل الخروج" %}</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'accounts:login' %}">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            {% trans "تسجيل الدخول" %}
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p>&copy; {% now "Y" %} {% trans "نظام إدارة وكالة السفر المغربية" %}. {% trans "جميع الحقوق محفوظة" %}.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <!-- Custom JS -->
    <script src="{% load static %}{% static 'js/custom.js' %}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
