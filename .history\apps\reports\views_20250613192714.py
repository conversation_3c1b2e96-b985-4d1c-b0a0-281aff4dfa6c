"""
Reports views for Moroccan Travel Agency ERP.
"""
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin


class ReportsView(LoginRequiredMixin, TemplateView):
    """Main reports dashboard view."""
    template_name = 'reports/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'التقارير'
        return context


class ClientReportsView(LoginRequiredMixin, TemplateView):
    """Client reports view."""
    template_name = 'reports/clients.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'تقارير العملاء'
        return context


class PackageReportsView(LoginRequiredMixin, TemplateView):
    """Package reports view."""
    template_name = 'reports/packages.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'تقارير الباقات'
        return context
