{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "لوحة تحكم العملاء" %}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.css" rel="stylesheet">
<style>
    .dashboard-card {
        background: white;
        border-radius: 20px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: none;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 25px;
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .stats-card:hover::before {
        transform: scale(1.2);
    }

    .stats-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 25px 50px rgba(102, 126, 234, 0.4);
    }

    .stats-card.success {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        box-shadow: 0 15px 35px rgba(17, 153, 142, 0.3);
    }

    .stats-card.warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        box-shadow: 0 15px 35px rgba(240, 147, 251, 0.3);
    }

    .stats-card.info {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #333;
        box-shadow: 0 15px 35px rgba(255, 236, 210, 0.3);
    }

    .stats-icon {
        font-size: 3.5rem;
        opacity: 0.9;
        margin-bottom: 20px;
        position: relative;
        z-index: 2;
    }

    .stats-number {
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 10px;
        position: relative;
        z-index: 2;
    }

    .stats-label {
        font-size: 1.1rem;
        opacity: 0.9;
        position: relative;
        z-index: 2;
    }

    .stats-change {
        font-size: 0.9rem;
        margin-top: 10px;
        position: relative;
        z-index: 2;
    }

    .chart-container {
        position: relative;
        height: 300px;
        margin: 20px 0;
    }

    .recent-clients-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .client-item {
        display: flex;
        align-items: center;
        padding: 15px;
        border-radius: 12px;
        margin-bottom: 10px;
        background: #f8f9fa;
        transition: all 0.3s ease;
        border: 1px solid transparent;
    }

    .client-item:hover {
        background: #e9ecef;
        border-color: #667eea;
        transform: translateX(5px);
    }

    .client-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.3rem;
        margin-left: 15px;
    }

    .client-info h6 {
        margin: 0;
        font-weight: 600;
        color: #2c3e50;
    }

    .client-info small {
        color: #6c757d;
    }

    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .quick-action-btn {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 15px;
        padding: 25px;
        text-align: center;
        text-decoration: none;
        color: #495057;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .quick-action-btn:hover {
        border-color: #667eea;
        color: #667eea;
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
    }

    .quick-action-btn i {
        font-size: 2.5rem;
        margin-bottom: 15px;
    }

    .activity-timeline {
        position: relative;
        padding-right: 30px;
    }

    .activity-timeline::before {
        content: '';
        position: absolute;
        right: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #e9ecef;
    }

    .activity-item {
        position: relative;
        padding: 15px 0;
        margin-bottom: 20px;
    }

    .activity-item::before {
        content: '';
        position: absolute;
        right: 9px;
        top: 20px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #667eea;
        border: 3px solid white;
        box-shadow: 0 0 0 2px #667eea;
    }

    .activity-content {
        background: white;
        padding: 15px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-left: 40px;
    }

    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px 0;
        margin: -20px -15px 30px -15px;
        border-radius: 0 0 30px 30px;
    }

    .welcome-message {
        font-size: 1.8rem;
        font-weight: 300;
        margin-bottom: 10px;
    }

    .current-time {
        opacity: 0.9;
        font-size: 1rem;
    }

    @media (max-width: 768px) {
        .stats-card {
            text-align: center;
            padding: 20px;
        }

        .dashboard-card {
            padding: 20px;
            margin-bottom: 20px;
        }

        .quick-actions {
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .quick-action-btn {
            padding: 20px 15px;
        }

        .quick-action-btn i {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .page-header {
            padding: 30px 0;
            text-align: center;
        }

        .welcome-message {
            font-size: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Client Types Pie Chart
    const clientTypesCtx = document.getElementById('clientTypesChart').getContext('2d');
    const clientTypesChart = new Chart(clientTypesCtx, {
        type: 'doughnut',
        data: {
            labels: [
                '{% trans "أفراد" %}',
                '{% trans "عائلات" %}',
                '{% trans "مجموعات" %}',
                '{% trans "شركات" %}'
            ],
            datasets: [{
                data: [
                    {% for type in client_types %}{{ type.count }}{% if not forloop.last %},{% endif %}{% endfor %}
                ],
                backgroundColor: [
                    '#667eea',
                    '#764ba2',
                    '#11998e',
                    '#f093fb'
                ],
                borderWidth: 0,
                hoverOffset: 10
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            family: 'Noto Sans Arabic',
                            size: 12
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleFont: {
                        family: 'Noto Sans Arabic'
                    },
                    bodyFont: {
                        family: 'Noto Sans Arabic'
                    },
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                duration: 1000
            }
        }
    });

    // Monthly Growth Line Chart
    const monthlyGrowthCtx = document.getElementById('monthlyGrowthChart').getContext('2d');
    const monthlyGrowthChart = new Chart(monthlyGrowthCtx, {
        type: 'line',
        data: {
            labels: [
                '{% trans "يناير" %}', '{% trans "فبراير" %}', '{% trans "مارس" %}',
                '{% trans "أبريل" %}', '{% trans "مايو" %}', '{% trans "يونيو" %}',
                '{% trans "يوليو" %}', '{% trans "أغسطس" %}', '{% trans "سبتمبر" %}',
                '{% trans "أكتوبر" %}', '{% trans "نوفمبر" %}', '{% trans "ديسمبر" %}'
            ],
            datasets: [{
                label: '{% trans "عملاء جدد" %}',
                data: [12, 19, 15, 25, 22, 30, 28, 35, 32, 40, 38, 45],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#667eea',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleFont: {
                        family: 'Noto Sans Arabic'
                    },
                    bodyFont: {
                        family: 'Noto Sans Arabic'
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        font: {
                            family: 'Noto Sans Arabic'
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            family: 'Noto Sans Arabic'
                        }
                    }
                }
            },
            animation: {
                duration: 1500,
                easing: 'easeInOutQuart'
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });

    // Animate statistics numbers
    function animateNumbers() {
        const statsNumbers = document.querySelectorAll('.stats-number');

        statsNumbers.forEach(element => {
            const finalValue = parseInt(element.textContent);
            const duration = 2000;
            const increment = finalValue / (duration / 16);
            let currentValue = 0;

            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= finalValue) {
                    element.textContent = finalValue;
                    clearInterval(timer);
                } else {
                    element.textContent = Math.floor(currentValue);
                }
            }, 16);
        });
    }

    // Start animations
    setTimeout(animateNumbers, 500);

    // Auto-refresh dashboard data every 5 minutes
    setInterval(() => {
        // You can implement auto-refresh logic here
        console.log('Dashboard auto-refresh');
    }, 300000);

    // Add click handlers for quick actions
    document.querySelectorAll('.quick-action-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            // Add loading state
            const icon = this.querySelector('i');
            const originalClass = icon.className;
            icon.className = 'fas fa-spinner fa-spin';

            // Restore icon after navigation
            setTimeout(() => {
                icon.className = originalClass;
            }, 1000);
        });
    });

    // Add hover effects to client items
    document.querySelectorAll('.client-item').forEach(item => {
        item.addEventListener('click', function() {
            // You can add navigation to client detail here
            console.log('Navigate to client detail');
        });
    });

    // Real-time clock update
    function updateClock() {
        const now = new Date();
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };

        const timeElement = document.querySelector('.current-time');
        if (timeElement) {
            const timeString = now.toLocaleDateString('ar-SA', options);
            timeElement.innerHTML = '<i class="fas fa-calendar-alt me-2"></i>' + timeString;
        }
    }

    // Update clock every minute
    setInterval(updateClock, 60000);
});
</script>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="welcome-message">
                    {% trans "مرحباً" %} {{ user.first_name|default:user.username }}
                </h1>
                <p class="current-time mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>
                    {% now "l، j F Y - H:i" %}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'crm:client_add' %}" class="btn btn-light btn-lg">
                    <i class="fas fa-plus me-2"></i>
                    {% trans "عميل جديد" %}
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Quick Actions -->
    <div class="quick-actions">
        <a href="{% url 'crm:client_add' %}" class="quick-action-btn">
            <i class="fas fa-user-plus"></i>
            <span>{% trans "إضافة عميل" %}</span>
        </a>
        <a href="{% url 'crm:client_list' %}" class="quick-action-btn">
            <i class="fas fa-users"></i>
            <span>{% trans "قائمة العملاء" %}</span>
        </a>
        <a href="{% url 'crm:client_export' %}" class="quick-action-btn">
            <i class="fas fa-download"></i>
            <span>{% trans "تصدير البيانات" %}</span>
        </a>
        <a href="#" class="quick-action-btn">
            <i class="fas fa-chart-bar"></i>
            <span>{% trans "التقارير" %}</span>
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stats-number">{{ total_clients|default:0 }}</div>
                <div class="stats-label">{% trans "إجمالي العملاء" %}</div>
                <div class="stats-change">
                    <i class="fas fa-arrow-up"></i>
                    +{{ new_clients_this_month|default:0 }} {% trans "هذا الشهر" %}
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card warning">
                <div class="stats-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="stats-number">{{ vip_clients|default:0 }}</div>
                <div class="stats-label">{% trans "عملاء مميزون" %}</div>
                <div class="stats-change">
                    {% widthratio vip_clients total_clients 100 %}% {% trans "من الإجمالي" %}
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card success">
                <div class="stats-icon">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="stats-number">{{ new_clients_this_month|default:0 }}</div>
                <div class="stats-label">{% trans "عملاء جدد" %}</div>
                <div class="stats-change">{% trans "هذا الشهر" %}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card info">
                <div class="stats-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stats-number">85%</div>
                <div class="stats-label">{% trans "معدل الرضا" %}</div>
                <div class="stats-change">
                    <i class="fas fa-arrow-up"></i>
                    +5% {% trans "من الشهر الماضي" %}
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Recent Activity -->
    <div class="row">
        <!-- Client Types Chart -->
        <div class="col-lg-6">
            <div class="dashboard-card">
                <h5 class="mb-4">
                    <i class="fas fa-chart-pie me-2"></i>
                    {% trans "توزيع أنواع العملاء" %}
                </h5>
                <div class="chart-container">
                    <canvas id="clientTypesChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Monthly Growth Chart -->
        <div class="col-lg-6">
            <div class="dashboard-card">
                <h5 class="mb-4">
                    <i class="fas fa-chart-line me-2"></i>
                    {% trans "نمو العملاء الشهري" %}
                </h5>
                <div class="chart-container">
                    <canvas id="monthlyGrowthChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Clients and Activity -->
    <div class="row">
        <!-- Recent Clients -->
        <div class="col-lg-8">
            <div class="dashboard-card">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        {% trans "العملاء الجدد" %}
                    </h5>
                    <a href="{% url 'crm:client_list' %}" class="btn btn-outline-primary btn-sm">
                        {% trans "عرض الكل" %}
                    </a>
                </div>
                <div class="recent-clients-list">
                    {% for client in recent_clients %}
                    <div class="client-item">
                        <div class="client-avatar">
                            {{ client.first_name_ar|first }}{{ client.last_name_ar|first }}
                        </div>
                        <div class="client-info flex-grow-1">
                            <h6>{{ client.full_name_ar }}</h6>
                            <small>
                                <i class="fas fa-envelope me-1"></i> {{ client.email|default:"لا يوجد بريد" }}
                                <span class="mx-2">|</span>
                                <i class="fas fa-phone me-1"></i> {{ client.phone|default:"لا يوجد هاتف" }}
                            </small>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">{{ client.created_at|timesince }}</small>
                            {% if client.vip_status %}
                                <br><span class="badge bg-warning">{% trans "مميز" %}</span>
                            {% endif %}
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <p class="text-muted">{% trans "لا توجد عملاء جدد" %}</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Activity Timeline -->
        <div class="col-lg-4">
            <div class="dashboard-card">
                <h5 class="mb-4">
                    <i class="fas fa-history me-2"></i>
                    {% trans "النشاط الأخير" %}
                </h5>
                <div class="activity-timeline">
                    <div class="activity-item">
                        <div class="activity-content">
                            <strong>{% trans "عميل جديد" %}</strong>
                            <p class="mb-1">تم إضافة عميل جديد إلى النظام</p>
                            <small class="text-muted">منذ 5 دقائق</small>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-content">
                            <strong>{% trans "تحديث بيانات" %}</strong>
                            <p class="mb-1">تم تحديث بيانات العميل أحمد محمد</p>
                            <small class="text-muted">منذ 15 دقيقة</small>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-content">
                            <strong>{% trans "تصدير بيانات" %}</strong>
                            <p class="mb-1">تم تصدير قائمة العملاء</p>
                            <small class="text-muted">منذ ساعة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
