{% extends 'base.html' %}
{% load static %}

{% block title %}{{ package.title_ar }}{% endblock %}

{% block extra_css %}
<style>
    .package-hero {
        height: 400px;
        background-size: cover;
        background-position: center;
        position: relative;
        border-radius: 15px;
        overflow: hidden;
    }
    
    .package-hero-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(0,0,0,0.7), rgba(0,0,0,0.3));
        display: flex;
        align-items: end;
        padding: 30px;
    }
    
    .package-hero-content h1 {
        color: white;
        font-size: 2.5rem;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    }
    
    .package-hero-content p {
        color: rgba(255,255,255,0.9);
        font-size: 1.1rem;
        margin-bottom: 0;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        margin-bottom: 20px;
    }
    
    .stats-card h3 {
        font-size: 2rem;
        margin-bottom: 5px;
    }
    
    .stats-card p {
        margin-bottom: 0;
        opacity: 0.9;
    }
    
    .info-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .info-card h4 {
        color: #333;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid #f8f9fa;
    }
    
    .badge-custom {
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
        margin: 5px;
    }
    
    .price-display {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        padding: 20px;
        border-radius: 15px;
        text-align: center;
        margin-bottom: 20px;
    }
    
    .price-display h2 {
        font-size: 2.5rem;
        margin-bottom: 5px;
    }
    
    .action-buttons .btn {
        margin: 5px;
        padding: 12px 25px;
        border-radius: 25px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Package Hero Section -->
    <div class="package-hero mb-4" 
         {% if package.main_image %}
         style="background-image: url('{{ package.main_image.url }}');"
         {% else %}
         style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);"
         {% endif %}>
        <div class="package-hero-overlay">
            <div class="package-hero-content">
                <h1>{{ package.title_ar }}</h1>
                <p>{{ package.short_description_ar }}</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Package Information -->
            <div class="info-card">
                <h4><i class="fas fa-info-circle text-primary me-2"></i>معلومات الباقة</h4>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>رمز الباقة:</strong> {{ package.package_code }}
                    </div>
                    <div class="col-md-6">
                        <strong>الفئة:</strong> 
                        {% if package.category %}
                            <span class="badge bg-primary">{{ package.category.name_ar }}</span>
                        {% else %}
                            <span class="text-muted">غير محدد</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>المدة:</strong> {{ package.duration_days }} أيام / {{ package.duration_nights }} ليالي
                    </div>
                    <div class="col-md-4">
                        <strong>عدد المشاركين:</strong> {{ package.min_participants }} - {{ package.max_participants }}
                    </div>
                    <div class="col-md-4">
                        <strong>مستوى الصعوبة:</strong>
                        <span class="badge badge-custom 
                            {% if package.difficulty_level == 'easy' %}bg-success
                            {% elif package.difficulty_level == 'moderate' %}bg-warning
                            {% elif package.difficulty_level == 'challenging' %}bg-orange
                            {% else %}bg-danger{% endif %}">
                            {{ package.get_difficulty_level_display }}
                        </span>
                    </div>
                </div>
                
                <!-- Status Badges -->
                <div class="mb-3">
                    {% if package.is_active %}
                        <span class="badge bg-success badge-custom">نشط</span>
                    {% else %}
                        <span class="badge bg-secondary badge-custom">غير نشط</span>
                    {% endif %}
                    
                    {% if package.is_featured %}
                        <span class="badge bg-warning badge-custom">مميز</span>
                    {% endif %}
                    
                    {% if package.is_bestseller %}
                        <span class="badge bg-danger badge-custom">الأكثر مبيعاً</span>
                    {% endif %}
                </div>
            </div>

            <!-- Description -->
            <div class="info-card">
                <h4><i class="fas fa-file-alt text-primary me-2"></i>الوصف التفصيلي</h4>
                <p>{{ package.detailed_description_ar|linebreaks }}</p>
            </div>

            <!-- Inclusions & Exclusions -->
            <div class="row">
                <div class="col-md-6">
                    <div class="info-card">
                        <h4><i class="fas fa-check-circle text-success me-2"></i>المشمول في الباقة</h4>
                        <p>{{ package.inclusions|linebreaks }}</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-card">
                        <h4><i class="fas fa-times-circle text-danger me-2"></i>غير المشمول</h4>
                        <p>{{ package.exclusions|linebreaks }}</p>
                    </div>
                </div>
            </div>

            <!-- Requirements -->
            {% if package.requirements %}
            <div class="info-card">
                <h4><i class="fas fa-exclamation-triangle text-warning me-2"></i>المتطلبات</h4>
                <p>{{ package.requirements|linebreaks }}</p>
            </div>
            {% endif %}

            <!-- Destinations -->
            {% if package.destinations.all %}
            <div class="info-card">
                <h4><i class="fas fa-map-marker-alt text-primary me-2"></i>الوجهات</h4>
                <div class="row">
                    {% for destination in package.destinations.all %}
                    <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-location-dot text-primary me-2"></i>
                            <span>{{ destination.name_ar }}, {{ destination.country.name_ar }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Price Display -->
            <div class="price-display">
                <h2>{{ package.base_price }} درهم</h2>
                <p class="mb-0">للشخص الواحد</p>
                {% if package.child_price %}
                    <small>سعر الطفل: {{ package.child_price }} درهم</small>
                {% endif %}
            </div>

            <!-- Statistics -->
            <div class="row">
                <div class="col-6">
                    <div class="stats-card">
                        <h3>{{ stats.total_reservations }}</h3>
                        <p>إجمالي الحجوزات</p>
                    </div>
                </div>
                <div class="col-6">
                    <div class="stats-card">
                        <h3>{{ stats.confirmed_reservations }}</h3>
                        <p>الحجوزات المؤكدة</p>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons text-center">
                <a href="{% url 'tours:package_edit' package.pk %}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>تعديل الباقة
                </a>
                <a href="{% url 'tours:package_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                </a>
                <button class="btn btn-success" onclick="createReservation()">
                    <i class="fas fa-plus me-2"></i>إنشاء حجز
                </button>
            </div>

            <!-- Package Info -->
            <div class="info-card">
                <h5><i class="fas fa-calendar text-primary me-2"></i>معلومات إضافية</h5>
                <div class="mb-2">
                    <small class="text-muted">تاريخ الإنشاء:</small><br>
                    <span>{{ package.created_at|date:"d/m/Y" }}</span>
                </div>
                {% if package.created_by %}
                <div class="mb-2">
                    <small class="text-muted">أنشئ بواسطة:</small><br>
                    <span>{{ package.created_by.get_full_name|default:package.created_by.username }}</span>
                </div>
                {% endif %}
                {% if package.available_from and package.available_to %}
                <div class="mb-2">
                    <small class="text-muted">متاح من:</small><br>
                    <span>{{ package.available_from|date:"d/m/Y" }} إلى {{ package.available_to|date:"d/m/Y" }}</span>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function createReservation() {
    // Redirect to reservation creation page
    window.location.href = '/reservations/add/?package={{ package.pk }}';
}

$(document).ready(function() {
    console.log('Package detail page loaded');
});
</script>
{% endblock %}
