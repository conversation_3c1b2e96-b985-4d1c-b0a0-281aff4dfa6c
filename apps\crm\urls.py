"""
URL configuration for CRM app.
"""
from django.urls import path
from . import views, views_enhanced

app_name = 'crm'

urlpatterns = [
    # Dashboard
    path('', views_enhanced.client_dashboard, name='dashboard'),

    # Enhanced Client URLs
    path('clients/', views_enhanced.client_list, name='client_list'),
    path('clients/add/', views_enhanced.client_create, name='client_add'),
    path('clients/add/modern/', views_enhanced.client_create, name='client_add_modern'),
    path('clients/<int:pk>/', views_enhanced.client_detail, name='client_detail'),
    path('clients/<int:pk>/edit/', views_enhanced.client_edit, name='client_edit'),
    path('clients/<int:pk>/delete/', views_enhanced.client_delete, name='client_delete'),

    # Export
    path('clients/export/', views_enhanced.client_export, name='client_export'),

    # AJAX URLs
    path('ajax/client-search/', views_enhanced.client_search_ajax, name='client_search_ajax'),

    # Legacy URLs (keeping for backward compatibility)
    path('legacy/clients/', views.ClientListView.as_view(), name='client_list_legacy'),
    path('legacy/clients/add/', views.ClientCreateView.as_view(), name='client_add_legacy'),
    path('legacy/clients/<int:pk>/', views.ClientDetailView.as_view(), name='client_detail_legacy'),
    path('legacy/clients/<int:pk>/edit/', views.ClientUpdateView.as_view(), name='client_edit_legacy'),
    path('legacy/clients/<int:pk>/delete/', views.ClientDeleteView.as_view(), name='client_delete_legacy'),

    # Communication URLs (if needed)
    # path('clients/<int:client_id>/communications/', views.CommunicationListView.as_view(), name='communication_list'),
    # path('clients/<int:client_id>/communications/add/', views.CommunicationCreateView.as_view(), name='communication_add'),
    # path('communications/<int:pk>/edit/', views.CommunicationUpdateView.as_view(), name='communication_edit'),

    # Contact URLs (if needed)
    # path('clients/<int:client_id>/contacts/add/', views.ContactCreateView.as_view(), name='contact_add'),
    # path('contacts/<int:pk>/edit/', views.ContactUpdateView.as_view(), name='contact_edit'),
    # path('contacts/<int:pk>/delete/', views.ContactDeleteView.as_view(), name='contact_delete'),
]
