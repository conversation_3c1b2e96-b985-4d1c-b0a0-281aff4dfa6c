"""
Simplified URL configuration for Moroccan Travel Agency ERP project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView
from apps.core.views import DashboardView, QuickStatsView, SystemSettingsView

urlpatterns = [
    # Admin
    path('admin/', admin.site.urls),

    # Main dashboard
    path('', DashboardView.as_view(), name='dashboard'),

    # Quick stats
    path('stats/', QuickStatsView.as_view(), name='quick_stats'),

    # Reports
    path('reports/', include('apps.reports.urls')),

    # API (simplified)
    path('api/', include('rest_framework.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
