"""
Admin configuration for accounts models.
"""
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _
from .models import User, UserProfile, UserSession, Permission, Role, UserRole


class UserProfileInline(admin.StackedInline):
    model = UserProfile
    can_delete = False
    verbose_name_plural = 'ملف المستخدم'


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    inlines = (UserProfileInline,)
    
    list_display = ('username', 'full_name_ar', 'email', 'role', 'is_active_employee', 'last_login')
    list_filter = ('role', 'is_active', 'is_active_employee', 'is_staff', 'hire_date')
    search_fields = ('username', 'first_name_ar', 'last_name_ar', 'email', 'employee_id')
    ordering = ('username',)
    
    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        (_('معلومات شخصية'), {
            'fields': ('first_name', 'last_name', 'first_name_ar', 'last_name_ar', 'email')
        }),
        (_('معلومات الاتصال'), {
            'fields': ('phone', 'whatsapp', 'national_id')
        }),
        (_('معلومات العمل'), {
            'fields': ('role', 'employee_id', 'department', 'hire_date', 'salary')
        }),
        (_('الملف الشخصي'), {
            'fields': ('avatar', 'bio', 'preferred_language')
        }),
        (_('الصلاحيات'), {
            'fields': ('is_active', 'is_active_employee', 'is_staff', 'is_superuser', 'groups', 'user_permissions'),
        }),
        (_('تواريخ مهمة'), {'fields': ('last_login', 'date_joined')}),
    )
    
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'first_name_ar', 'last_name_ar', 'email', 'password1', 'password2', 'role'),
        }),
    )


@admin.register(UserSession)
class UserSessionAdmin(admin.ModelAdmin):
    list_display = ('user', 'ip_address', 'login_time', 'logout_time', 'is_active')
    list_filter = ('is_active', 'login_time')
    search_fields = ('user__username', 'ip_address')
    readonly_fields = ('session_key', 'user_agent', 'login_time')
    ordering = ('-login_time',)


@admin.register(Permission)
class PermissionAdmin(admin.ModelAdmin):
    list_display = ('name', 'codename', 'module')
    list_filter = ('module',)
    search_fields = ('name', 'codename', 'description')
    ordering = ('module', 'name')


@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')
    filter_horizontal = ('permissions',)
    ordering = ('name',)


@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    list_display = ('user', 'role', 'assigned_by', 'assigned_at', 'is_active')
    list_filter = ('role', 'is_active', 'assigned_at')
    search_fields = ('user__username', 'role__name')
    ordering = ('-assigned_at',)
