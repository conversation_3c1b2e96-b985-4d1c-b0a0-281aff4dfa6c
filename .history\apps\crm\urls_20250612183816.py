"""
URL configuration for CRM app.
"""
from django.urls import path
from . import views

app_name = 'crm'

urlpatterns = [
    # Client URLs
    path('', views.ClientListView.as_view(), name='client_list'),
    path('clients/', views.ClientListView.as_view(), name='client_list'),
    path('clients/add/', views.ClientCreateView.as_view(), name='client_add'),
    path('clients/<int:pk>/', views.ClientDetailView.as_view(), name='client_detail'),
    path('clients/<int:pk>/edit/', views.ClientUpdateView.as_view(), name='client_edit'),
    path('clients/<int:pk>/delete/', views.ClientDeleteView.as_view(), name='client_delete'),

    # Communication URLs
    path('clients/<int:client_id>/communications/', views.CommunicationListView.as_view(), name='communication_list'),
    path('clients/<int:client_id>/communications/add/', views.CommunicationCreateView.as_view(), name='communication_add'),
    path('communications/<int:pk>/edit/', views.CommunicationUpdateView.as_view(), name='communication_edit'),

    # Contact URLs
    path('clients/<int:client_id>/contacts/add/', views.ContactCreateView.as_view(), name='contact_add'),
    path('contacts/<int:pk>/edit/', views.ContactUpdateView.as_view(), name='contact_edit'),
    path('contacts/<int:pk>/delete/', views.ContactDeleteView.as_view(), name='contact_delete'),

    # AJAX URLs
    path('ajax/client-search/', views.ClientSearchAjaxView.as_view(), name='client_search_ajax'),
    path('ajax/client-info/<int:pk>/', views.ClientInfoAjaxView.as_view(), name='client_info_ajax'),
]
