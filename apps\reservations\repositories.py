from typing import List, Optional
from django.db.models import QuerySet
from apps.reservations.models import Reservation

class ReservationRepository:
    def get_by_id(self, id: int) -> Optional[Reservation]:
        return Reservation.objects.filter(id=id).first()

    def get_all(self) -> QuerySet[Reservation]:
        return Reservation.objects.all()

    def get_active(self) -> QuerySet[Reservation]:
        return Reservation.objects.filter(status='confirmed')

    def create(self, data: dict) -> Reservation:
        return Reservation.objects.create(**data)

    def update(self, reservation: Reservation, data: dict) -> Reservation:
        for key, value in data.items():
            setattr(reservation, key, value)
        reservation.save()
        return reservation

    def delete(self, reservation: Reservation) -> None:
        reservation.delete()
