"""
Visa management models for handling visa applications and processing.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import FileExtensionValidator
from apps.core.models import TimeStampedModel


class VisaType(TimeStampedModel):
    """Different types of visas available."""

    name_ar = models.CharField(_('الاسم بالعربية'), max_length=100)
    name_fr = models.Char<PERSON><PERSON>(_('الاسم بالفرنسية'), max_length=100)
    name_en = models.CharField(_('الاسم بالإنجليزية'), max_length=100)

    description = models.TextField(_('الوصف'), blank=True)
    country = models.ForeignKey('core.Country', on_delete=models.CASCADE, verbose_name=_('الدولة'))

    # Processing Information
    processing_time_days = models.PositiveIntegerField(_('مدة المعالجة بالأيام'))
    validity_days = models.PositiveIntegerField(_('صالحة لمدة (أيام)'))

    # Requirements
    required_documents = models.JSONField(_('الوثائق المطلوبة'), default=list)
    fees = models.DecimalField(_('الرسوم'), max_digits=10, decimal_places=2)

    # Status
    is_active = models.BooleanField(_('نشط'), default=True)

    class Meta:
        verbose_name = _('نوع التأشيرة')
        verbose_name_plural = _('أنواع التأشيرات')
        ordering = ['country', 'name_ar']
        unique_together = ['country', 'name_ar']

    def __str__(self):
        return f"{self.name_ar} - {self.country.name_ar}"


class VisaApplication(TimeStampedModel):
    """Visa application tracking."""

    STATUS_CHOICES = [
        ('draft', _('مسودة')),
        ('submitted', _('مقدم')),
        ('under_review', _('قيد المراجعة')),
        ('additional_docs_required', _('وثائق إضافية مطلوبة')),
        ('approved', _('موافق عليه')),
        ('rejected', _('مرفوض')),
        ('issued', _('صادر')),
        ('collected', _('تم الاستلام')),
        ('cancelled', _('ملغي')),
    ]

    PRIORITY_CHOICES = [
        ('normal', _('عادي')),
        ('urgent', _('عاجل')),
        ('express', _('سريع')),
    ]

    # Basic Information
    application_number = models.CharField(_('رقم الطلب'), max_length=20, unique=True)
    client = models.ForeignKey('crm.Client', on_delete=models.CASCADE, verbose_name=_('العميل'))
    visa_type = models.ForeignKey(VisaType, on_delete=models.CASCADE, verbose_name=_('نوع التأشيرة'))

    # Application Details
    purpose_of_travel = models.CharField(_('الغرض من السفر'), max_length=200)
    intended_arrival_date = models.DateField(_('تاريخ الوصول المقصود'))
    intended_departure_date = models.DateField(_('تاريخ المغادرة المقصود'))
    duration_of_stay = models.PositiveIntegerField(_('مدة الإقامة (أيام)'))

    # Processing Information
    status = models.CharField(_('الحالة'), max_length=30, choices=STATUS_CHOICES, default='draft')
    priority = models.CharField(_('الأولوية'), max_length=20, choices=PRIORITY_CHOICES, default='normal')

    # Dates
    submission_date = models.DateField(_('تاريخ التقديم'), null=True, blank=True)
    expected_completion_date = models.DateField(_('تاريخ الإنجاز المتوقع'), null=True, blank=True)
    actual_completion_date = models.DateField(_('تاريخ الإنجاز الفعلي'), null=True, blank=True)

    # Financial
    application_fee = models.DecimalField(_('رسوم الطلب'), max_digits=10, decimal_places=2, default=0)
    service_fee = models.DecimalField(_('رسوم الخدمة'), max_digits=10, decimal_places=2, default=0)
    total_amount = models.DecimalField(_('المبلغ الإجمالي'), max_digits=10, decimal_places=2, default=0)
    payment_status = models.CharField(
        _('حالة الدفع'),
        max_length=20,
        choices=[
            ('pending', _('معلق')),
            ('partial', _('جزئي')),
            ('paid', _('مدفوع')),
            ('refunded', _('مسترد')),
        ],
        default='pending'
    )

    # Staff Assignment
    assigned_to = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('مسند إلى')
    )

    # Notes and Comments
    notes = models.TextField(_('ملاحظات'), blank=True)
    rejection_reason = models.TextField(_('سبب الرفض'), blank=True)

    class Meta:
        verbose_name = _('طلب تأشيرة')
        verbose_name_plural = _('طلبات التأشيرات')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['application_number']),
            models.Index(fields=['client', 'status']),
            models.Index(fields=['status', 'priority']),
        ]

    def __str__(self):
        return f"{self.application_number} - {self.client.full_name_ar}"

    def save(self, *args, **kwargs):
        if not self.application_number:
            # Generate application number
            last_app = VisaApplication.objects.filter(
                application_number__startswith='VISA'
            ).order_by('-id').first()
            if last_app:
                last_number = int(last_app.application_number[4:])
                self.application_number = f"VISA{last_number + 1:06d}"
            else:
                self.application_number = "VISA000001"

        # Calculate total amount
        self.total_amount = self.application_fee + self.service_fee

        super().save(*args, **kwargs)


class VisaDocument(TimeStampedModel):
    """Documents associated with visa applications."""

    DOCUMENT_TYPES = [
        ('passport', _('جواز السفر')),
        ('photo', _('صورة شخصية')),
        ('invitation', _('خطاب دعوة')),
        ('hotel_booking', _('حجز فندق')),
        ('flight_booking', _('حجز طيران')),
        ('bank_statement', _('كشف حساب بنكي')),
        ('employment_letter', _('خطاب عمل')),
        ('insurance', _('تأمين سفر')),
        ('other', _('أخرى')),
    ]

    application = models.ForeignKey(
        VisaApplication,
        on_delete=models.CASCADE,
        related_name='documents',
        verbose_name=_('طلب التأشيرة')
    )

    document_type = models.CharField(_('نوع الوثيقة'), max_length=20, choices=DOCUMENT_TYPES)
    title = models.CharField(_('العنوان'), max_length=200)
    file = models.FileField(
        _('الملف'),
        upload_to='visa_documents/',
        validators=[FileExtensionValidator(allowed_extensions=['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx'])]
    )

    # Verification
    is_verified = models.BooleanField(_('موثق'), default=False)
    verified_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('موثق بواسطة')
    )
    verification_date = models.DateTimeField(_('تاريخ التوثيق'), null=True, blank=True)

    notes = models.TextField(_('ملاحظات'), blank=True)

    class Meta:
        verbose_name = _('وثيقة تأشيرة')
        verbose_name_plural = _('وثائق التأشيرات')
        ordering = ['document_type', 'title']

    def __str__(self):
        return f"{self.application.application_number} - {self.get_document_type_display()}"


class VisaAppointment(TimeStampedModel):
    """Visa appointment scheduling."""

    application = models.ForeignKey(
        VisaApplication,
        on_delete=models.CASCADE,
        related_name='appointments',
        verbose_name=_('طلب التأشيرة')
    )

    appointment_date = models.DateTimeField(_('تاريخ الموعد'))
    location = models.CharField(_('المكان'), max_length=200)
    address = models.TextField(_('العنوان'))

    # Status
    status = models.CharField(
        _('الحالة'),
        max_length=20,
        choices=[
            ('scheduled', _('مجدول')),
            ('confirmed', _('مؤكد')),
            ('completed', _('مكتمل')),
            ('cancelled', _('ملغي')),
            ('rescheduled', _('معاد جدولته')),
        ],
        default='scheduled'
    )

    # Reminders
    reminder_sent = models.BooleanField(_('تم إرسال التذكير'), default=False)
    reminder_date = models.DateTimeField(_('تاريخ التذكير'), null=True, blank=True)

    notes = models.TextField(_('ملاحظات'), blank=True)

    class Meta:
        verbose_name = _('موعد تأشيرة')
        verbose_name_plural = _('مواعيد التأشيرات')
        ordering = ['appointment_date']

    def __str__(self):
        return f"{self.application.application_number} - {self.appointment_date}"
