"""
Management command to perform system health checks.
"""
from django.core.management.base import BaseCommand
from django.db import connection
from django.core.cache import cache
from django.conf import settings
import os
import sys


class Command(BaseCommand):
    help = 'Perform comprehensive system health checks'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔍 فحص صحة النظام...'))
        self.stdout.write('='*60)

        # Check database connection
        self.check_database()

        # Check file permissions
        self.check_file_permissions()

        # Check required directories
        self.check_directories()

        # Check models and data integrity
        self.check_data_integrity()

        # Check security settings
        self.check_security_settings()

        # Check performance
        self.check_performance()

        self.stdout.write('='*60)
        self.stdout.write(self.style.SUCCESS('✅ فحص صحة النظام مكتمل!'))

    def check_database(self):
        """Check database connectivity and basic operations."""
        self.stdout.write('\n📊 فحص قاعدة البيانات:')

        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                if result[0] == 1:
                    self.stdout.write(self.style.SUCCESS('  ✅ الاتصال بقاعدة البيانات: نجح'))
                else:
                    self.stdout.write(self.style.ERROR('  ❌ الاتصال بقاعدة البيانات: فشل'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ خطأ في قاعدة البيانات: {e}'))

        # Check table counts
        try:
            from apps.crm.models import Client
            from apps.tours.models import TourPackage
            from apps.accounts.models import User

            client_count = Client.objects.count()
            package_count = TourPackage.objects.count()
            user_count = User.objects.count()

            self.stdout.write(f'  📈 العملاء: {client_count}')
            self.stdout.write(f'  📈 الباقات: {package_count}')
            self.stdout.write(f'  📈 المستخدمين: {user_count}')

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ خطأ في عد البيانات: {e}'))

    def check_file_permissions(self):
        """Check file and directory permissions."""
        self.stdout.write('\n📁 فحص صلاحيات الملفات:')

        # Check media directory
        media_root = getattr(settings, 'MEDIA_ROOT', 'media')
        if os.path.exists(media_root):
            if os.access(media_root, os.W_OK):
                self.stdout.write(self.style.SUCCESS('  ✅ مجلد الوسائط: قابل للكتابة'))
            else:
                self.stdout.write(self.style.ERROR('  ❌ مجلد الوسائط: غير قابل للكتابة'))
        else:
            self.stdout.write(self.style.WARNING('  ⚠️ مجلد الوسائط: غير موجود'))

        # Check static directory
        static_root = getattr(settings, 'STATIC_ROOT', 'static')
        if static_root and os.path.exists(static_root):
            if os.access(static_root, os.R_OK):
                self.stdout.write(self.style.SUCCESS('  ✅ مجلد الملفات الثابتة: قابل للقراءة'))
            else:
                self.stdout.write(self.style.ERROR('  ❌ مجلد الملفات الثابتة: غير قابل للقراءة'))

    def check_directories(self):
        """Check required directories exist."""
        self.stdout.write('\n📂 فحص المجلدات المطلوبة:')

        required_dirs = ['logs', 'media', 'static']

        for dir_name in required_dirs:
            if os.path.exists(dir_name):
                self.stdout.write(self.style.SUCCESS(f'  ✅ {dir_name}: موجود'))
            else:
                self.stdout.write(self.style.WARNING(f'  ⚠️ {dir_name}: غير موجود'))
                try:
                    os.makedirs(dir_name, exist_ok=True)
                    self.stdout.write(self.style.SUCCESS(f'  ✅ تم إنشاء {dir_name}'))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'  ❌ فشل إنشاء {dir_name}: {e}'))

    def check_data_integrity(self):
        """Check data integrity and relationships."""
        self.stdout.write('\n🔗 فحص سلامة البيانات:')

        try:
            from apps.crm.models import Client
            from apps.tours.models import TourPackage

            # Check for clients without nationality
            clients_without_nationality = Client.objects.filter(nationality__isnull=True).count()
            if clients_without_nationality > 0:
                self.stdout.write(self.style.WARNING(f'  ⚠️ عملاء بدون جنسية: {clients_without_nationality}'))
            else:
                self.stdout.write(self.style.SUCCESS('  ✅ جميع العملاء لديهم جنسية'))

            # Check for packages without category
            packages_without_category = TourPackage.objects.filter(category__isnull=True).count()
            if packages_without_category > 0:
                self.stdout.write(self.style.WARNING(f'  ⚠️ باقات بدون فئة: {packages_without_category}'))
            else:
                self.stdout.write(self.style.SUCCESS('  ✅ جميع الباقات لديها فئة'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ خطأ في فحص البيانات: {e}'))

    def check_security_settings(self):
        """Check security configuration."""
        self.stdout.write('\n🔒 فحص الإعدادات الأمنية:')

        # Check DEBUG setting
        if settings.DEBUG:
            self.stdout.write(self.style.WARNING('  ⚠️ وضع التطوير مفعل (DEBUG=True)'))
        else:
            self.stdout.write(self.style.SUCCESS('  ✅ وضع الإنتاج مفعل (DEBUG=False)'))

        # Check SECRET_KEY
        if hasattr(settings, 'SECRET_KEY') and len(settings.SECRET_KEY) > 20:
            self.stdout.write(self.style.SUCCESS('  ✅ المفتاح السري: محدد'))
        else:
            self.stdout.write(self.style.ERROR('  ❌ المفتاح السري: غير محدد أو قصير'))

        # Check ALLOWED_HOSTS
        if settings.ALLOWED_HOSTS:
            self.stdout.write(self.style.SUCCESS(f'  ✅ المضيفين المسموحين: {len(settings.ALLOWED_HOSTS)}'))
        else:
            self.stdout.write(self.style.WARNING('  ⚠️ المضيفين المسموحين: غير محدد'))

    def check_performance(self):
        """Check performance-related settings."""
        self.stdout.write('\n⚡ فحص الأداء:')

        # Check Python version
        python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        self.stdout.write(f'  🐍 إصدار Python: {python_version}')

        # Check database queries (simple test)
        try:
            from django.db import connection
            from django.test.utils import override_settings

            with connection.cursor() as cursor:
                cursor.execute("SELECT COUNT(*) FROM django_session")
                session_count = cursor.fetchone()[0]
                self.stdout.write(f'  📊 جلسات نشطة: {session_count}')

        except Exception as e:
            self.stdout.write(self.style.WARNING(f'  ⚠️ لا يمكن فحص الجلسات: {e}'))

        # Check installed apps
        installed_apps_count = len(settings.INSTALLED_APPS)
        self.stdout.write(f'  📦 التطبيقات المثبتة: {installed_apps_count}')

        # Memory usage (basic check)
        try:
            import psutil
            memory_percent = psutil.virtual_memory().percent
            self.stdout.write(f'  💾 استخدام الذاكرة: {memory_percent:.1f}%')
        except ImportError:
            self.stdout.write('  💾 استخدام الذاكرة: غير متاح (psutil غير مثبت)')
        except Exception as e:
            self.stdout.write(f'  💾 استخدام الذاكرة: خطأ ({e})')
