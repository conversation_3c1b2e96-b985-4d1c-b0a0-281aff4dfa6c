"""
Forms for CRM app.
"""
from django import forms
from django.core.exceptions import ValidationError
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Row, Column, Submit, HTML, Div
from crispy_forms.bootstrap import Field
from .models import Client


class ClientForm(forms.ModelForm):
    """Form for creating and updating clients."""

    class Meta:
        model = Client
        fields = [
            'first_name_ar', 'last_name_ar', 'first_name_en', 'last_name_en',
            'email', 'phone', 'date_of_birth', 'gender', 'nationality',
            'passport_number', 'passport_expiry', 'client_type', 'vip_status',
            'address', 'city', 'country', 'notes', 'emergency_contact_name',
            'emergency_contact_phone', 'dietary_restrictions', 'medical_conditions',
            'preferred_language', 'marketing_consent'
        ]
        widgets = {
            'date_of_birth': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'passport_expiry': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'address': forms.Textarea(attrs={'rows': 2, 'class': 'form-control'}),
            'dietary_restrictions': forms.Textarea(attrs={'rows': 2, 'class': 'form-control'}),
            'medical_conditions': forms.Textarea(attrs={'rows': 2, 'class': 'form-control'}),
            'first_name_ar': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الاسم الأول بالعربية'}),
            'last_name_ar': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم العائلة بالعربية'}),
            'first_name_en': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'First Name in English'}),
            'last_name_en': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Last Name in English'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': '<EMAIL>'}),
            'phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': '+212 6XX XXX XXX'}),
            'passport_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم جواز السفر'}),
            'emergency_contact_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم جهة الاتصال للطوارئ'}),
            'emergency_contact_phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم هاتف الطوارئ'}),
            'city': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'المدينة'}),
            'country': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'البلد'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Make required fields
        self.fields['first_name_ar'].required = True
        self.fields['last_name_ar'].required = True
        self.fields['email'].required = True
        self.fields['phone'].required = True
        self.fields['nationality'].required = True

        # Set up crispy forms
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'form-horizontal'
        self.helper.label_class = 'col-lg-3'
        self.helper.field_class = 'col-lg-9'

        self.helper.layout = Layout(
            HTML('<h4 class="mb-4"><i class="fas fa-user me-2"></i>المعلومات الأساسية</h4>'),
            Row(
                Column('first_name_ar', css_class='form-group col-md-6 mb-3'),
                Column('last_name_ar', css_class='form-group col-md-6 mb-3'),
                css_class='form-row'
            ),
            Row(
                Column('first_name_en', css_class='form-group col-md-6 mb-3'),
                Column('last_name_en', css_class='form-group col-md-6 mb-3'),
                css_class='form-row'
            ),
            Row(
                Column('email', css_class='form-group col-md-6 mb-3'),
                Column('phone', css_class='form-group col-md-6 mb-3'),
                css_class='form-row'
            ),
            Row(
                Column('date_of_birth', css_class='form-group col-md-4 mb-3'),
                Column('gender', css_class='form-group col-md-4 mb-3'),
                Column('nationality', css_class='form-group col-md-4 mb-3'),
                css_class='form-row'
            ),

            HTML('<hr><h4 class="mb-4"><i class="fas fa-passport me-2"></i>معلومات جواز السفر</h4>'),
            Row(
                Column('passport_number', css_class='form-group col-md-6 mb-3'),
                Column('passport_expiry', css_class='form-group col-md-6 mb-3'),
                css_class='form-row'
            ),

            HTML('<hr><h4 class="mb-4"><i class="fas fa-cog me-2"></i>إعدادات العميل</h4>'),
            Row(
                Column('client_type', css_class='form-group col-md-6 mb-3'),
                Column('preferred_language', css_class='form-group col-md-6 mb-3'),
                css_class='form-row'
            ),
            Row(
                Column('vip_status', css_class='form-group col-md-6 mb-3'),
                Column('marketing_consent', css_class='form-group col-md-6 mb-3'),
                css_class='form-row'
            ),

            HTML('<hr><h4 class="mb-4"><i class="fas fa-map-marker-alt me-2"></i>معلومات العنوان</h4>'),
            'address',
            Row(
                Column('city', css_class='form-group col-md-6 mb-3'),
                Column('country', css_class='form-group col-md-6 mb-3'),
                css_class='form-row'
            ),

            HTML('<hr><h4 class="mb-4"><i class="fas fa-phone me-2"></i>جهة الاتصال للطوارئ</h4>'),
            Row(
                Column('emergency_contact_name', css_class='form-group col-md-6 mb-3'),
                Column('emergency_contact_phone', css_class='form-group col-md-6 mb-3'),
                css_class='form-row'
            ),

            HTML('<hr><h4 class="mb-4"><i class="fas fa-notes-medical me-2"></i>معلومات صحية وغذائية</h4>'),
            'dietary_restrictions',
            'medical_conditions',

            HTML('<hr><h4 class="mb-4"><i class="fas fa-sticky-note me-2"></i>ملاحظات</h4>'),
            'notes',

            HTML('<hr>'),
            Div(
                Submit('submit', 'حفظ البيانات', css_class='btn btn-primary btn-lg me-2'),
                HTML('<a href="{% url "crm:client_list" %}" class="btn btn-secondary btn-lg">إلغاء</a>'),
                css_class='text-center mt-4'
            )
        )

    def clean_email(self):
        """Validate email uniqueness."""
        email = self.cleaned_data.get('email')
        if email:
            # Check if email exists for other clients
            existing_client = Client.objects.filter(email=email)
            if self.instance.pk:
                existing_client = existing_client.exclude(pk=self.instance.pk)

            if existing_client.exists():
                raise ValidationError('عميل آخر مسجل بهذا البريد الإلكتروني بالفعل')

        return email

    def clean_passport_number(self):
        """Validate passport number uniqueness."""
        passport_number = self.cleaned_data.get('passport_number')
        if passport_number:
            # Check if passport number exists for other clients
            existing_client = Client.objects.filter(passport_number=passport_number)
            if self.instance.pk:
                existing_client = existing_client.exclude(pk=self.instance.pk)

            if existing_client.exists():
                raise ValidationError('عميل آخر مسجل بهذا رقم جواز السفر بالفعل')

        return passport_number

    def clean(self):
        """Additional form validation."""
        cleaned_data = super().clean()

        # Validate passport expiry date
        passport_expiry = cleaned_data.get('passport_expiry')
        if passport_expiry:
            from django.utils import timezone
            if passport_expiry <= timezone.now().date():
                self.add_error('passport_expiry', 'تاريخ انتهاء جواز السفر يجب أن يكون في المستقبل')

        return cleaned_data


class ClientSearchForm(forms.Form):
    """Form for searching clients."""

    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث بالاسم، البريد الإلكتروني، أو رقم الهاتف...',
        })
    )

    client_type = forms.ChoiceField(
        choices=[('', 'جميع الأنواع')] + Client.CLIENT_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    nationality = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'الجنسية'
        })
    )

    vip_status = forms.ChoiceField(
        choices=[('', 'الكل'), (True, 'VIP'), (False, 'عادي')],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.helper = FormHelper()
        self.helper.form_method = 'get'
        self.helper.form_class = 'form-inline'
        self.helper.layout = Layout(
            Row(
                Column('search', css_class='form-group col-md-4 mb-2'),
                Column('client_type', css_class='form-group col-md-2 mb-2'),
                Column('nationality', css_class='form-group col-md-2 mb-2'),
                Column('vip_status', css_class='form-group col-md-2 mb-2'),
                Column(
                    Submit('submit', 'بحث', css_class='btn btn-primary'),
                    css_class='form-group col-md-2 mb-2'
                ),
                css_class='form-row'
            )
        )


class ClientQuickAddForm(forms.ModelForm):
    """Quick form for adding basic client information."""

    class Meta:
        model = Client
        fields = ['first_name_ar', 'last_name_ar', 'email', 'phone', 'nationality']
        widgets = {
            'first_name_ar': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الاسم الأول'}),
            'last_name_ar': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم العائلة'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'البريد الإلكتروني'}),
            'phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الهاتف'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Make all fields required
        for field in self.fields:
            self.fields[field].required = True

        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            'first_name_ar',
            'last_name_ar',
            'email',
            'phone',
            'nationality',
            Submit('submit', 'إضافة عميل', css_class='btn btn-success btn-block mt-3')
        )
