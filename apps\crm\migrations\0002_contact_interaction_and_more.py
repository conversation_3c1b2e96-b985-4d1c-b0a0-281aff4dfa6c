# Generated by Django 4.2 on 2025-06-13 10:23

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('crm', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Contact',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('first_name_ar', models.CharField(max_length=50, verbose_name='الاسم الأول بالعربية')),
                ('last_name_ar', models.Char<PERSON>ield(max_length=50, verbose_name='اسم العائلة بالعربية')),
                ('first_name_fr', models.CharField(blank=True, max_length=50, verbose_name='الاسم الأول بالفرنسية')),
                ('last_name_fr', models.CharField(blank=True, max_length=50, verbose_name='اسم العائلة بالفرنسية')),
                ('position', models.CharField(max_length=100, verbose_name='المنصب')),
                ('department', models.CharField(blank=True, max_length=100, verbose_name='القسم')),
                ('email', models.EmailField(max_length=254, verbose_name='البريد الإلكتروني')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('mobile', models.CharField(blank=True, max_length=20, verbose_name='الجوال')),
                ('whatsapp', models.CharField(blank=True, max_length=20, verbose_name='رقم الواتساب')),
                ('is_primary', models.BooleanField(default=False, verbose_name='جهة اتصال رئيسية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contacts', to='crm.client', verbose_name='العميل')),
            ],
            options={
                'verbose_name': 'جهة اتصال',
                'verbose_name_plural': 'جهات الاتصال',
                'ordering': ['-is_primary', 'first_name_ar'],
            },
        ),
        migrations.CreateModel(
            name='Interaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('type', models.CharField(choices=[('call', 'مكالمة'), ('email', 'بريد إلكتروني'), ('meeting', 'اجتماع'), ('whatsapp', 'واتساب'), ('visit', 'زيارة'), ('other', 'آخر')], max_length=20, verbose_name='نوع التفاعل')),
                ('subject', models.CharField(max_length=200, verbose_name='الموضوع')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('status', models.CharField(choices=[('open', 'مفتوحة'), ('in_progress', 'قيد التنفيذ'), ('pending', 'معلقة'), ('resolved', 'تم الحل'), ('closed', 'مغلقة')], default='open', max_length=20, verbose_name='الحالة')),
                ('date', models.DateTimeField(verbose_name='تاريخ التفاعل')),
                ('duration', models.PositiveIntegerField(blank=True, null=True, verbose_name='المدة (دقائق)')),
                ('location', models.CharField(blank=True, max_length=200, verbose_name='الموقع')),
                ('requires_followup', models.BooleanField(default=False, verbose_name='يتطلب متابعة')),
                ('followup_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ المتابعة')),
                ('followup_notes', models.TextField(blank=True, verbose_name='ملاحظات المتابعة')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإكمال')),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_interactions', to=settings.AUTH_USER_MODEL, verbose_name='مكلف إلى')),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interactions', to='crm.client', verbose_name='العميل')),
                ('contact', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='interactions', to='crm.contact', verbose_name='جهة الاتصال')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_interactions', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'تفاعل',
                'verbose_name_plural': 'التفاعلات',
                'ordering': ['-date'],
            },
        ),
        migrations.AddIndex(
            model_name='interaction',
            index=models.Index(fields=['client', '-date'], name='crm_interac_client__5432af_idx'),
        ),
        migrations.AddIndex(
            model_name='interaction',
            index=models.Index(fields=['type'], name='crm_interac_type_740e9d_idx'),
        ),
        migrations.AddIndex(
            model_name='interaction',
            index=models.Index(fields=['status'], name='crm_interac_status_364108_idx'),
        ),
        migrations.AddIndex(
            model_name='interaction',
            index=models.Index(fields=['priority'], name='crm_interac_priorit_1eddcb_idx'),
        ),
        migrations.AddIndex(
            model_name='interaction',
            index=models.Index(fields=['requires_followup'], name='crm_interac_require_6f98b8_idx'),
        ),
        migrations.AddIndex(
            model_name='contact',
            index=models.Index(fields=['client', 'is_primary'], name='crm_contact_client__7cf9fe_idx'),
        ),
        migrations.AddIndex(
            model_name='contact',
            index=models.Index(fields=['email'], name='crm_contact_email_240305_idx'),
        ),
        migrations.AddIndex(
            model_name='contact',
            index=models.Index(fields=['phone'], name='crm_contact_phone_189db8_idx'),
        ),
    ]
