# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# Database Settings
DB_NAME=moroccan_travel_erp
DB_USER=postgres
DB_PASSWORD=postgres
DB_HOST=localhost
DB_PORT=5432

# Redis Settings
REDIS_URL=redis://localhost:6379/0

# Email Settings
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# File Upload Settings
MAX_UPLOAD_SIZE=10485760  # 10MB

# Security Settings
SECURE_SSL_REDIRECT=False
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False

# API Keys (Optional)
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
WHATSAPP_API_TOKEN=your-whatsapp-api-token

# AWS S3 Configuration (Optional)
USE_S3=False
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-bucket-name
AWS_S3_REGION_NAME=us-east-1

# Sentry Error Monitoring (Optional)
SENTRY_DSN=your-sentry-dsn
ENVIRONMENT=development

# Payment Gateway (Optional)
STRIPE_PUBLIC_KEY=your-stripe-public-key
STRIPE_SECRET_KEY=your-stripe-secret-key

# Backup Settings
BACKUP_ENABLED=True
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# Two-Factor Authentication
ENABLE_2FA=True

# API Rate Limiting
API_RATE_LIMIT=1000/hour

# Offline Mode Settings
ENABLE_OFFLINE_MODE=True
SYNC_INTERVAL_MINUTES=15

# Default From Email
DEFAULT_FROM_EMAIL=<EMAIL>
