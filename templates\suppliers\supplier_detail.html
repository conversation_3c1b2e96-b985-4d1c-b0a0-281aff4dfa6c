{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}تفاصيل المورد - {{ supplier.name_ar }}{% endblock %}

{% block extra_css %}
<style>
.supplier-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
}

.supplier-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.supplier-logo {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: white;
    margin: 0 auto 20px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-label {
    font-weight: 500;
    color: #6c757d;
}

.info-value {
    color: #495057;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.status-active {
    background: #d1edff;
    color: #0c63e4;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

.rating-stars {
    color: #ffc107;
    font-size: 1.2rem;
}

.contract-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid #667eea;
}

.contract-item.active {
    border-left-color: #38ef7d;
}

.contract-item.expired {
    border-left-color: #ff6b6b;
}

.evaluation-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid #fdbb2d;
}

.btn-action {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.metric-card {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.metric-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.performance-chart {
    height: 200px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Supplier Header -->
    <div class="supplier-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <div class="supplier-logo me-4">
                        {{ supplier.name_ar|first }}
                    </div>
                    <div>
                        <h1 class="h2 mb-2">{{ supplier.name_ar }}</h1>
                        <p class="mb-1 opacity-75">
                            <i class="fas fa-industry me-2"></i>{{ supplier.get_supplier_type_display }}
                        </p>
                        <p class="mb-0 opacity-75">
                            <i class="fas fa-map-marker-alt me-2"></i>{{ supplier.address|default:"غير محدد" }}
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <span class="status-badge status-{{ supplier.is_active|yesno:'active,inactive' }}">
                    {{ supplier.is_active|yesno:'نشط,غير نشط' }}
                </span>
                {% if supplier.is_preferred %}
                <br><span class="badge bg-warning mt-2">
                    <i class="fas fa-star me-1"></i>مورد مفضل
                </span>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Supplier Information -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="supplier-card">
                <h5 class="mb-4"><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-item">
                            <span class="info-label">الاسم بالعربية</span>
                            <span class="info-value">{{ supplier.name_ar }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الاسم بالإنجليزية</span>
                            <span class="info-value">{{ supplier.name_en|default:"غير محدد" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">نوع المورد</span>
                            <span class="info-value">{{ supplier.get_supplier_type_display }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">البريد الإلكتروني</span>
                            <span class="info-value">{{ supplier.email|default:"غير محدد" }}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item">
                            <span class="info-label">رقم الهاتف</span>
                            <span class="info-value">{{ supplier.phone|default:"غير محدد" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الموقع الإلكتروني</span>
                            <span class="info-value">
                                {% if supplier.website %}
                                <a href="{{ supplier.website }}" target="_blank">{{ supplier.website }}</a>
                                {% else %}
                                غير محدد
                                {% endif %}
                            </span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">العنوان</span>
                            <span class="info-value">{{ supplier.address|default:"غير محدد" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">تاريخ التسجيل</span>
                            <span class="info-value">{{ supplier.created_at|date:"d/m/Y" }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Person -->
            <div class="supplier-card">
                <h5 class="mb-4"><i class="fas fa-user-tie me-2"></i>الشخص المسؤول</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-item">
                            <span class="info-label">الاسم</span>
                            <span class="info-value">{{ supplier.contact_person|default:"غير محدد" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">المنصب</span>
                            <span class="info-value">{{ supplier.contact_position|default:"غير محدد" }}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item">
                            <span class="info-label">الهاتف المباشر</span>
                            <span class="info-value">{{ supplier.contact_phone|default:"غير محدد" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">البريد المباشر</span>
                            <span class="info-value">{{ supplier.contact_email|default:"غير محدد" }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Contracts -->
            <div class="supplier-card">
                <h5 class="mb-4"><i class="fas fa-file-contract me-2"></i>العقود الحديثة</h5>
                {% if recent_contracts %}
                <div class="row">
                    {% for contract in recent_contracts %}
                    <div class="col-md-6">
                        <div class="contract-item {{ contract.status }}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ contract.title }}</h6>
                                    <p class="mb-1 text-muted">{{ contract.start_date }} - {{ contract.end_date }}</p>
                                    <small class="text-muted">{{ contract.get_contract_type_display }}</small>
                                </div>
                                <span class="badge bg-{{ contract.status|yesno:'success,warning,danger' }}">
                                    {{ contract.get_status_display }}
                                </span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-file-contract fa-3x mb-3"></i>
                    <p>لا توجد عقود مسجلة</p>
                </div>
                {% endif %}
            </div>

            <!-- Recent Evaluations -->
            <div class="supplier-card">
                <h5 class="mb-4"><i class="fas fa-star me-2"></i>التقييمات الأخيرة</h5>
                {% if recent_evaluations %}
                <div class="row">
                    {% for evaluation in recent_evaluations %}
                    <div class="col-md-6">
                        <div class="evaluation-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <div class="rating-stars mb-1">
                                        {% for i in "12345"|make_list %}
                                            {% if forloop.counter <= evaluation.overall_score %}
                                                <i class="fas fa-star"></i>
                                            {% else %}
                                                <i class="far fa-star"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                    <p class="mb-1 text-muted">{{ evaluation.evaluation_date }}</p>
                                    <small class="text-muted">بواسطة: {{ evaluation.evaluated_by.get_full_name }}</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-primary">{{ evaluation.overall_score }}/5</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-star fa-3x mb-3"></i>
                    <p>لا توجد تقييمات بعد</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Performance Metrics -->
            <div class="metric-card">
                <div class="metric-value">{{ supplier.rating|floatformat:1|default:"0.0" }}</div>
                <div class="metric-label">التقييم العام</div>
                <div class="rating-stars mt-2">
                    {% for i in "12345"|make_list %}
                        {% if forloop.counter <= supplier.rating %}
                            <i class="fas fa-star"></i>
                        {% else %}
                            <i class="far fa-star"></i>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-value">{{ stats.total_contracts|default:0 }}</div>
                <div class="metric-label">إجمالي العقود</div>
            </div>

            <div class="metric-card">
                <div class="metric-value">{{ stats.active_contracts|default:0 }}</div>
                <div class="metric-label">العقود النشطة</div>
            </div>

            <!-- Actions -->
            <div class="supplier-card">
                <h6 class="mb-3"><i class="fas fa-cogs me-2"></i>الإجراءات</h6>
                <div class="d-grid gap-2">
                    <button class="btn btn-action">
                        <i class="fas fa-edit me-2"></i>تعديل البيانات
                    </button>
                    <button class="btn btn-outline-primary">
                        <i class="fas fa-file-contract me-2"></i>عقد جديد
                    </button>
                    <button class="btn btn-outline-warning">
                        <i class="fas fa-star me-2"></i>تقييم الأداء
                    </button>
                    <button class="btn btn-outline-info">
                        <i class="fas fa-chart-line me-2"></i>تقرير الأداء
                    </button>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="supplier-card">
                <h6 class="mb-3"><i class="fas fa-chart-pie me-2"></i>إحصائيات سريعة</h6>
                <div class="info-item">
                    <span class="info-label">مدة التعاون</span>
                    <span class="info-value">{{ supplier.created_at|timesince }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">آخر تعامل</span>
                    <span class="info-value">{{ supplier.last_interaction|default:"لا يوجد" }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">حالة الدفع</span>
                    <span class="info-value">{{ supplier.payment_status|default:"جيدة" }}</span>
                </div>
            </div>

            <!-- Performance Chart -->
            <div class="supplier-card">
                <h6 class="mb-3"><i class="fas fa-chart-line me-2"></i>أداء المورد</h6>
                <div class="text-center">
                    <canvas id="performanceChart" width="200" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Performance Chart
const performanceCtx = document.getElementById('performanceChart').getContext('2d');
const performanceChart = new Chart(performanceCtx, {
    type: 'radar',
    data: {
        labels: ['الجودة', 'التسليم', 'السعر', 'الخدمة', 'الموثوقية'],
        datasets: [{
            label: 'الأداء',
            data: [{{ supplier.quality_score|default:0 }}, {{ supplier.delivery_score|default:0 }}, {{ supplier.price_score|default:0 }}, {{ supplier.service_score|default:0 }}, {{ supplier.reliability_score|default:0 }}],
            backgroundColor: 'rgba(102, 126, 234, 0.2)',
            borderColor: 'rgb(102, 126, 234)',
            pointBackgroundColor: 'rgb(102, 126, 234)',
            pointBorderColor: '#fff',
            pointHoverBackgroundColor: '#fff',
            pointHoverBorderColor: 'rgb(102, 126, 234)'
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            r: {
                beginAtZero: true,
                max: 5,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});
</script>
{% endblock %}
