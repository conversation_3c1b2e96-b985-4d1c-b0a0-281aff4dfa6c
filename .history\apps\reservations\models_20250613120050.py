"""
Reservation models for managing bookings and reservations.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from djmoney.models.fields import MoneyField
from apps.core.models import AuditModel


class Reservation(AuditModel):
    """Main reservation model."""

    STATUS_CHOICES = [
        ('draft', _('مسودة')),
        ('pending', _('في الانتظار')),
        ('confirmed', _('مؤكد')),
        ('cancelled', _('ملغي')),
        ('completed', _('مكتمل')),
        ('no_show', _('عدم حضور')),
    ]

    PAYMENT_STATUS_CHOICES = [
        ('unpaid', _('غير مدفوع')),
        ('partial', _('مدفوع جزئياً')),
        ('paid', _('مدفوع بالكامل')),
        ('refunded', _('مسترد')),
    ]

    # Basic Information
    reservation_number = models.CharField(_('رقم الحجز'), max_length=20, unique=True)
    client = models.ForeignKey('crm.Client', on_delete=models.CASCADE, verbose_name=_('العميل'))
    package = models.ForeignKey('tours.TourPackage', on_delete=models.CASCADE, verbose_name=_('الباقة السياحية'))
    availability = models.ForeignKey('tours.PackageAvailability', on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_('التوفر'))

    # Dates
    departure_date = models.DateField(_('تاريخ المغادرة'))
    return_date = models.DateField(_('تاريخ العودة'))
    booking_date = models.DateTimeField(_('تاريخ الحجز'), auto_now_add=True)

    # Participants
    adults = models.PositiveIntegerField(_('عدد البالغين'), default=1, validators=[MinValueValidator(1)])
    children = models.PositiveIntegerField(_('عدد الأطفال'), default=0)
    infants = models.PositiveIntegerField(_('عدد الرضع'), default=0)

    # Pricing
    adult_price = MoneyField(_('سعر البالغ'), max_digits=10, decimal_places=2, default_currency='MAD')
    child_price = MoneyField(_('سعر الطفل'), max_digits=10, decimal_places=2, default_currency='MAD', null=True, blank=True)
    infant_price = MoneyField(_('سعر الرضيع'), max_digits=10, decimal_places=2, default_currency='MAD', null=True, blank=True)

    subtotal = MoneyField(_('المجموع الفرعي'), max_digits=10, decimal_places=2, default_currency='MAD')
    discount_amount = MoneyField(_('مبلغ الخصم'), max_digits=10, decimal_places=2, default_currency='MAD', default=0)
    tax_amount = MoneyField(_('مبلغ الضريبة'), max_digits=10, decimal_places=2, default_currency='MAD', default=0)
    total_amount = MoneyField(_('المبلغ الإجمالي'), max_digits=10, decimal_places=2, default_currency='MAD')

    # Status
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='draft')
    payment_status = models.CharField(_('حالة الدفع'), max_length=20, choices=PAYMENT_STATUS_CHOICES, default='unpaid')

    # Special Requirements
    special_requests = models.TextField(_('طلبات خاصة'), blank=True)
    dietary_requirements = models.TextField(_('متطلبات غذائية'), blank=True)
    accessibility_needs = models.TextField(_('احتياجات إمكانية الوصول'), blank=True)

    # Internal Information
    sales_agent = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, verbose_name=_('وكيل المبيعات'))
    internal_notes = models.TextField(_('ملاحظات داخلية'), blank=True)

    # Confirmation
    confirmation_sent = models.BooleanField(_('تم إرسال التأكيد'), default=False)
    confirmation_sent_at = models.DateTimeField(_('تاريخ إرسال التأكيد'), null=True, blank=True)

    class Meta:
        verbose_name = _('حجز')
        verbose_name_plural = _('الحجوزات')
        ordering = ['-booking_date']
        indexes = [
            models.Index(fields=['reservation_number']),
            models.Index(fields=['client', 'status']),
            models.Index(fields=['departure_date']),
            models.Index(fields=['status', 'payment_status']),
            models.Index(fields=['booking_date', 'status']),
            models.Index(fields=['client', 'status']),
        ]

    def __str__(self):
        return f"{self.reservation_number} - {self.client.full_name_ar}"

    @property
    def total_participants(self):
        """Calculate total number of participants."""
        return self.adults + self.children + self.infants

    @property
    def paid_amount(self):
        """Calculate total paid amount from payments."""
        return sum(payment.amount.amount for payment in self.payments.filter(status='completed'))

    @property
    def remaining_amount(self):
        """Calculate remaining amount to be paid."""
        return self.total_amount.amount - self.paid_amount

    def save(self, *args, **kwargs):
        if not self.reservation_number:
            # Generate reservation number
            last_reservation = Reservation.objects.filter(reservation_number__startswith='RES').order_by('-id').first()
            if last_reservation:
                last_number = int(last_reservation.reservation_number[3:])
                self.reservation_number = f"RES{last_number + 1:06d}"
            else:
                self.reservation_number = "RES000001"

        # Calculate totals
        self.subtotal = (self.adult_price * self.adults) + \
                       (self.child_price * self.children if self.child_price else 0) + \
                       (self.infant_price * self.infants if self.infant_price else 0)

        self.total_amount = self.subtotal - self.discount_amount + self.tax_amount

        super().save(*args, **kwargs)


class ReservationParticipant(AuditModel):
    """Individual participants in a reservation."""
    reservation = models.ForeignKey(Reservation, on_delete=models.CASCADE, related_name='participants', verbose_name=_('الحجز'))

    PARTICIPANT_TYPE_CHOICES = [
        ('adult', _('بالغ')),
        ('child', _('طفل')),
        ('infant', _('رضيع')),
    ]

    GENDER_CHOICES = [
        ('M', _('ذكر')),
        ('F', _('أنثى')),
    ]

    # Personal Information
    first_name = models.CharField(_('الاسم الأول'), max_length=50)
    last_name = models.CharField(_('اسم العائلة'), max_length=50)
    date_of_birth = models.DateField(_('تاريخ الميلاد'), null=True, blank=True)
    gender = models.CharField(_('الجنس'), max_length=1, choices=GENDER_CHOICES, blank=True)
    nationality = models.CharField(_('الجنسية'), max_length=50, blank=True)

    # Documents
    passport_number = models.CharField(_('رقم جواز السفر'), max_length=20, blank=True)
    passport_expiry = models.DateField(_('تاريخ انتهاء جواز السفر'), null=True, blank=True)

    # Classification
    participant_type = models.CharField(_('نوع المشارك'), max_length=10, choices=PARTICIPANT_TYPE_CHOICES)

    # Special Requirements
    dietary_restrictions = models.TextField(_('قيود غذائية'), blank=True)
    medical_conditions = models.TextField(_('حالات طبية'), blank=True)
    special_assistance = models.TextField(_('مساعدة خاصة'), blank=True)

    class Meta:
        verbose_name = _('مشارك في الحجز')
        verbose_name_plural = _('المشاركون في الحجز')
        ordering = ['participant_type', 'last_name', 'first_name']

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.get_participant_type_display()})"

    @property
    def full_name(self):
        """Return full name."""
        return f"{self.first_name} {self.last_name}".strip()

    @property
    def age(self):
        """Calculate age from date of birth."""
        if self.date_of_birth:
            from datetime import date
            today = date.today()
            return today.year - self.date_of_birth.year - (
                (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day)
            )
        return None


class ReservationService(AuditModel):
    """Additional services added to a reservation."""
    reservation = models.ForeignKey(Reservation, on_delete=models.CASCADE, related_name='services', verbose_name=_('الحجز'))

    SERVICE_TYPE_CHOICES = [
        ('transport', _('نقل')),
        ('accommodation', _('إقامة إضافية')),
        ('meal', _('وجبة')),
        ('activity', _('نشاط')),
        ('guide', _('مرشد')),
        ('insurance', _('تأمين')),
        ('visa', _('تأشيرة')),
        ('other', _('أخرى')),
    ]

    service_type = models.CharField(_('نوع الخدمة'), max_length=20, choices=SERVICE_TYPE_CHOICES)
    name = models.CharField(_('اسم الخدمة'), max_length=100)
    description = models.TextField(_('الوصف'), blank=True)

    # Pricing
    unit_price = MoneyField(_('سعر الوحدة'), max_digits=10, decimal_places=2, default_currency='MAD')
    quantity = models.PositiveIntegerField(_('الكمية'), default=1)
    total_price = MoneyField(_('السعر الإجمالي'), max_digits=10, decimal_places=2, default_currency='MAD')

    # Dates
    service_date = models.DateField(_('تاريخ الخدمة'), null=True, blank=True)

    # Status
    is_confirmed = models.BooleanField(_('مؤكد'), default=False)
    is_optional = models.BooleanField(_('اختياري'), default=False)

    class Meta:
        verbose_name = _('خدمة الحجز')
        verbose_name_plural = _('خدمات الحجز')
        ordering = ['service_date', 'name']

    def __str__(self):
        return f"{self.name} - {self.reservation.reservation_number}"

    def save(self, *args, **kwargs):
        self.total_price = self.unit_price * self.quantity
        super().save(*args, **kwargs)


class ReservationDocument(AuditModel):
    """Documents related to a reservation."""
    reservation = models.ForeignKey(Reservation, on_delete=models.CASCADE, related_name='documents', verbose_name=_('الحجز'))

    DOCUMENT_TYPE_CHOICES = [
        ('contract', _('عقد')),
        ('invoice', _('فاتورة')),
        ('voucher', _('قسيمة')),
        ('itinerary', _('برنامج الرحلة')),
        ('passport', _('جواز سفر')),
        ('visa', _('تأشيرة')),
        ('insurance', _('تأمين')),
        ('other', _('أخرى')),
    ]

    document_type = models.CharField(_('نوع الوثيقة'), max_length=20, choices=DOCUMENT_TYPE_CHOICES)
    title = models.CharField(_('العنوان'), max_length=100)
    description = models.TextField(_('الوصف'), blank=True)

    # File
    file = models.FileField(_('الملف'), upload_to='reservations/documents/')
    file_size = models.PositiveIntegerField(_('حجم الملف'), null=True, blank=True)

    # Metadata
    uploaded_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, verbose_name=_('رفع بواسطة'))
    is_public = models.BooleanField(_('عام'), default=False)

    class Meta:
        verbose_name = _('وثيقة الحجز')
        verbose_name_plural = _('وثائق الحجز')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.reservation.reservation_number}"
