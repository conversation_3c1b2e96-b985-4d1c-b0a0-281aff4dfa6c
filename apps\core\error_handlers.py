"""
Custom error handlers for the Moroccan Travel Agency ERP system.
"""
import logging
from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.csrf import requires_csrf_token
from django.utils.translation import gettext as _

logger = logging.getLogger(__name__)


@requires_csrf_token
def handler404(request, exception):
    """Custom 404 error handler."""
    logger.warning(f"404 error: {request.path} - User: {request.user}")

    if request.path.startswith('/api/'):
        return JsonResponse({
            'error': 'Not Found',
            'message': _('الصفحة المطلوبة غير موجودة'),
            'status_code': 404
        }, status=404)

    return render(request, 'errors/404.html', {
        'title': _('الصفحة غير موجودة'),
        'message': _('عذراً، الصفحة التي تبحث عنها غير موجودة.'),
    }, status=404)


@requires_csrf_token
def handler500(request):
    """Custom 500 error handler."""
    logger.error(f"500 error: {request.path} - User: {request.user}")

    if request.path.startswith('/api/'):
        return JsonResponse({
            'error': 'Internal Server Error',
            'message': _('حدث خطأ في الخادم'),
            'status_code': 500
        }, status=500)

    return render(request, 'errors/500.html', {
        'title': _('خطأ في الخادم'),
        'message': _('عذراً، حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.'),
    }, status=500)


@requires_csrf_token
def handler403(request, exception):
    """Custom 403 error handler."""
    logger.warning(f"403 error: {request.path} - User: {request.user}")

    if request.path.startswith('/api/'):
        return JsonResponse({
            'error': 'Forbidden',
            'message': _('ليس لديك صلاحية للوصول إلى هذه الصفحة'),
            'status_code': 403
        }, status=403)

    return render(request, 'errors/403.html', {
        'title': _('ممنوع الوصول'),
        'message': _('عذراً، ليس لديك صلاحية للوصول إلى هذه الصفحة.'),
    }, status=403)


@requires_csrf_token
def handler400(request, exception):
    """Custom 400 error handler."""
    logger.warning(f"400 error: {request.path} - User: {request.user}")

    if request.path.startswith('/api/'):
        return JsonResponse({
            'error': 'Bad Request',
            'message': _('طلب غير صحيح'),
            'status_code': 400
        }, status=400)

    return render(request, 'errors/400.html', {
        'title': _('طلب غير صحيح'),
        'message': _('عذراً، الطلب الذي أرسلته غير صحيح.'),
    }, status=400)


class ErrorHandlingMixin:
    """Mixin to add error handling to views."""

    def dispatch(self, request, *args, **kwargs):
        try:
            return super().dispatch(request, *args, **kwargs)
        except Exception as e:
            logger.error(f"Error in {self.__class__.__name__}: {str(e)}", exc_info=True)

            if request.headers.get('Accept') == 'application/json':
                return JsonResponse({
                    'error': 'Internal Error',
                    'message': _('حدث خطأ أثناء معالجة الطلب'),
                }, status=500)

            return render(request, 'errors/500.html', {
                'title': _('خطأ في النظام'),
                'message': _('حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.'),
            }, status=500)


def log_error(error_type, message, user=None, extra_data=None):
    """Utility function to log errors consistently."""
    log_data = {
        'error_type': error_type,
        'message': message,
        'user': str(user) if user else 'Anonymous',
    }

    if extra_data:
        log_data.update(extra_data)

    logger.error(f"[{error_type}] {message}", extra=log_data)


def handle_database_error(operation, model_name, error):
    """Handle database operation errors."""
    error_message = f"Database error in {operation} for {model_name}: {str(error)}"
    log_error('DATABASE_ERROR', error_message, extra_data={
        'operation': operation,
        'model': model_name,
        'error': str(error)
    })
    return _('حدث خطأ في قاعدة البيانات. يرجى المحاولة مرة أخرى.')


def handle_validation_error(field_errors):
    """Handle form validation errors."""
    error_messages = []
    for field, errors in field_errors.items():
        for error in errors:
            error_messages.append(f"{field}: {error}")

    log_error('VALIDATION_ERROR', f"Validation failed: {'; '.join(error_messages)}")
    return error_messages


def handle_permission_error(user, action, resource):
    """Handle permission denied errors."""
    error_message = f"Permission denied: {user} tried to {action} {resource}"
    log_error('PERMISSION_ERROR', error_message, user=user, extra_data={
        'action': action,
        'resource': resource
    })
    return _('ليس لديك صلاحية لتنفيذ هذا الإجراء.')


class SafeQueryMixin:
    """Mixin to add safe database queries."""

    def safe_get_object(self, model, **kwargs):
        """Safely get an object from database."""
        try:
            return model.objects.get(**kwargs)
        except model.DoesNotExist:
            log_error('OBJECT_NOT_FOUND', f"{model.__name__} not found with {kwargs}")
            return None
        except Exception as e:
            log_error('DATABASE_ERROR', f"Error getting {model.__name__}: {str(e)}")
            return None

    def safe_get_queryset(self, model, **kwargs):
        """Safely get a queryset from database."""
        try:
            return model.objects.filter(**kwargs)
        except Exception as e:
            log_error('DATABASE_ERROR', f"Error querying {model.__name__}: {str(e)}")
            return model.objects.none()

    def safe_create_object(self, model, **kwargs):
        """Safely create an object in database."""
        try:
            return model.objects.create(**kwargs)
        except Exception as e:
            error_msg = handle_database_error('create', model.__name__, e)
            return None, error_msg

    def safe_update_object(self, obj, **kwargs):
        """Safely update an object in database."""
        try:
            for key, value in kwargs.items():
                setattr(obj, key, value)
            obj.save()
            return True, None
        except Exception as e:
            error_msg = handle_database_error('update', obj.__class__.__name__, e)
            return False, error_msg
