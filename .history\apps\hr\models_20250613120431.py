"""
HR models for managing employees, schedules, and payroll.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from djmoney.models.fields import MoneyField
from apps.core.models import AuditModel


class Department(AuditModel):
    """Department model for organizing employees."""
    name_ar = models.CharField(_('الاسم بالعربية'), max_length=100)
    name_fr = models.CharField(_('الاسم بالفرنسية'), max_length=100, blank=True)
    description = models.TextField(_('الوصف'), blank=True)

    # Manager
    manager = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='managed_departments', verbose_name=_('المدير'))

    # Status
    is_active = models.BooleanField(_('نشط'), default=True)

    class Meta:
        verbose_name = _('قسم')
        verbose_name_plural = _('الأقسام')
        ordering = ['name_ar']

    def __str__(self):
        return self.name_ar


class Position(AuditModel):
    """Position/Job title model."""
    title_ar = models.CharField(_('المسمى بالعربية'), max_length=100)
    title_fr = models.CharField(_('المسمى بالفرنسية'), max_length=100, blank=True)
    description = models.TextField(_('الوصف'), blank=True)

    # Department
    department = models.ForeignKey(Department, on_delete=models.CASCADE, verbose_name=_('القسم'))

    # Salary Range
    min_salary = MoneyField(_('الحد الأدنى للراتب'), max_digits=10, decimal_places=2, default_currency='MAD', null=True, blank=True)
    max_salary = MoneyField(_('الحد الأقصى للراتب'), max_digits=10, decimal_places=2, default_currency='MAD', null=True, blank=True)

    # Requirements
    requirements = models.TextField(_('المتطلبات'), blank=True)
    responsibilities = models.TextField(_('المسؤوليات'), blank=True)

    # Status
    is_active = models.BooleanField(_('نشط'), default=True)

    class Meta:
        verbose_name = _('منصب')
        verbose_name_plural = _('المناصب')
        ordering = ['department', 'title_ar']

    def __str__(self):
        return f"{self.title_ar} - {self.department.name_ar}"


class Schedule(AuditModel):
    """Work schedule model."""
    name = models.CharField(_('اسم الجدول'), max_length=100)
    description = models.TextField(_('الوصف'), blank=True)

    # Working Hours
    monday_start = models.TimeField(_('بداية الاثنين'), null=True, blank=True)
    monday_end = models.TimeField(_('نهاية الاثنين'), null=True, blank=True)
    tuesday_start = models.TimeField(_('بداية الثلاثاء'), null=True, blank=True)
    tuesday_end = models.TimeField(_('نهاية الثلاثاء'), null=True, blank=True)
    wednesday_start = models.TimeField(_('بداية الأربعاء'), null=True, blank=True)
    wednesday_end = models.TimeField(_('نهاية الأربعاء'), null=True, blank=True)
    thursday_start = models.TimeField(_('بداية الخميس'), null=True, blank=True)
    thursday_end = models.TimeField(_('نهاية الخميس'), null=True, blank=True)
    friday_start = models.TimeField(_('بداية الجمعة'), null=True, blank=True)
    friday_end = models.TimeField(_('نهاية الجمعة'), null=True, blank=True)
    saturday_start = models.TimeField(_('بداية السبت'), null=True, blank=True)
    saturday_end = models.TimeField(_('نهاية السبت'), null=True, blank=True)
    sunday_start = models.TimeField(_('بداية الأحد'), null=True, blank=True)
    sunday_end = models.TimeField(_('نهاية الأحد'), null=True, blank=True)

    # Break Times
    break_duration = models.PositiveIntegerField(_('مدة الاستراحة (دقائق)'), default=60)

    # Status
    is_active = models.BooleanField(_('نشط'), default=True)
    is_default = models.BooleanField(_('افتراضي'), default=False)

    class Meta:
        verbose_name = _('جدول العمل')
        verbose_name_plural = _('جداول العمل')
        ordering = ['name']

    def __str__(self):
        return self.name


class Leave(AuditModel):
    """Employee leave/vacation model."""

    LEAVE_TYPE_CHOICES = [
        ('annual', _('إجازة سنوية')),
        ('sick', _('إجازة مرضية')),
        ('maternity', _('إجازة أمومة')),
        ('paternity', _('إجازة أبوة')),
        ('emergency', _('إجازة طارئة')),
        ('unpaid', _('إجازة بدون راتب')),
        ('compensatory', _('إجازة تعويضية')),
        ('other', _('أخرى')),
    ]

    STATUS_CHOICES = [
        ('pending', _('في الانتظار')),
        ('approved', _('موافق عليها')),
        ('rejected', _('مرفوضة')),
        ('cancelled', _('ملغية')),
    ]

    # Employee
    employee = models.ForeignKey('accounts.User', on_delete=models.CASCADE, related_name='leaves', verbose_name=_('الموظف'))

    # Leave Details
    leave_type = models.CharField(_('نوع الإجازة'), max_length=20, choices=LEAVE_TYPE_CHOICES)
    start_date = models.DateField(_('تاريخ البداية'))
    end_date = models.DateField(_('تاريخ النهاية'))
    days_requested = models.PositiveIntegerField(_('عدد الأيام المطلوبة'))

    # Reason
    reason = models.TextField(_('السبب'))

    # Status
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='pending')

    # Approval
    approved_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_leaves', verbose_name=_('وافق عليها'))
    approval_date = models.DateTimeField(_('تاريخ الموافقة'), null=True, blank=True)
    approval_notes = models.TextField(_('ملاحظات الموافقة'), blank=True)

    # Documents
    supporting_document = models.FileField(_('وثيقة مساندة'), upload_to='hr/leaves/', blank=True)

    class Meta:
        verbose_name = _('إجازة')
        verbose_name_plural = _('الإجازات')
        ordering = ['-start_date']
        indexes = [
            models.Index(fields=['employee', 'status']),
            models.Index(fields=['start_date', 'end_date']),
        ]

    def __str__(self):
        return f"{self.employee.full_name_ar} - {self.get_leave_type_display()} ({self.start_date})"

    def save(self, *args, **kwargs):
        # Calculate days requested
        if self.start_date and self.end_date:
            self.days_requested = (self.end_date - self.start_date).days + 1
        super().save(*args, **kwargs)


class Attendance(AuditModel):
    """Employee attendance tracking."""

    STATUS_CHOICES = [
        ('present', _('حاضر')),
        ('absent', _('غائب')),
        ('late', _('متأخر')),
        ('half_day', _('نصف يوم')),
        ('on_leave', _('في إجازة')),
    ]

    # Employee
    employee = models.ForeignKey('accounts.User', on_delete=models.CASCADE, related_name='attendances', verbose_name=_('الموظف'))

    # Date and Time
    date = models.DateField(_('التاريخ'))
    check_in_time = models.TimeField(_('وقت الدخول'), null=True, blank=True)
    check_out_time = models.TimeField(_('وقت الخروج'), null=True, blank=True)

    # Status
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='present')

    # Working Hours
    scheduled_hours = models.DecimalField(_('الساعات المجدولة'), max_digits=4, decimal_places=2, default=8.0)
    actual_hours = models.DecimalField(_('الساعات الفعلية'), max_digits=4, decimal_places=2, null=True, blank=True)
    overtime_hours = models.DecimalField(_('ساعات إضافية'), max_digits=4, decimal_places=2, default=0)

    # Notes
    notes = models.TextField(_('ملاحظات'), blank=True)

    # Recorded by
    recorded_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='recorded_attendances', verbose_name=_('سجل بواسطة'))

    class Meta:
        verbose_name = _('حضور')
        verbose_name_plural = _('الحضور')
        ordering = ['-date', 'employee']
        unique_together = ['employee', 'date']
        indexes = [
            models.Index(fields=['employee', 'date']),
            models.Index(fields=['date', 'status']),
        ]

    def __str__(self):
        return f"{self.employee.full_name_ar} - {self.date} ({self.get_status_display()})"

    def save(self, *args, **kwargs):
        # Calculate actual hours if check in/out times are provided
        if self.check_in_time and self.check_out_time:
            from datetime import datetime, timedelta

            # Convert times to datetime for calculation
            check_in = datetime.combine(self.date, self.check_in_time)
            check_out = datetime.combine(self.date, self.check_out_time)

            # Handle overnight shifts
            if check_out < check_in:
                check_out += timedelta(days=1)

            # Calculate hours worked
            time_diff = check_out - check_in
            self.actual_hours = time_diff.total_seconds() / 3600

            # Calculate overtime
            if self.actual_hours > self.scheduled_hours:
                self.overtime_hours = self.actual_hours - self.scheduled_hours
            else:
                self.overtime_hours = 0

        super().save(*args, **kwargs)


class Payroll(AuditModel):
    """Employee payroll model."""

    STATUS_CHOICES = [
        ('draft', _('مسودة')),
        ('calculated', _('محسوبة')),
        ('approved', _('موافق عليها')),
        ('paid', _('مدفوعة')),
    ]

    # Employee
    employee = models.ForeignKey('accounts.User', on_delete=models.CASCADE, related_name='payrolls', verbose_name=_('الموظف'))

    # Period
    pay_period_start = models.DateField(_('بداية فترة الراتب'))
    pay_period_end = models.DateField(_('نهاية فترة الراتب'))
    pay_date = models.DateField(_('تاريخ الدفع'))

    # Basic Salary
    basic_salary = MoneyField(_('الراتب الأساسي'), max_digits=10, decimal_places=2, default_currency='MAD')

    # Allowances
    housing_allowance = MoneyField(_('بدل السكن'), max_digits=10, decimal_places=2, default_currency='MAD', default=0)
    transport_allowance = MoneyField(_('بدل النقل'), max_digits=10, decimal_places=2, default_currency='MAD', default=0)
    meal_allowance = MoneyField(_('بدل الوجبات'), max_digits=10, decimal_places=2, default_currency='MAD', default=0)
    other_allowances = MoneyField(_('بدلات أخرى'), max_digits=10, decimal_places=2, default_currency='MAD', default=0)

    # Overtime
    overtime_hours = models.DecimalField(_('ساعات إضافية'), max_digits=6, decimal_places=2, default=0)
    overtime_rate = MoneyField(_('معدل الساعة الإضافية'), max_digits=8, decimal_places=2, default_currency='MAD', default=0)
    overtime_amount = MoneyField(_('مبلغ الساعات الإضافية'), max_digits=10, decimal_places=2, default_currency='MAD', default=0)

    # Deductions
    tax_deduction = MoneyField(_('خصم الضريبة'), max_digits=10, decimal_places=2, default_currency='MAD', default=0)
    social_security = MoneyField(_('الضمان الاجتماعي'), max_digits=10, decimal_places=2, default_currency='MAD', default=0)
    insurance_deduction = MoneyField(_('خصم التأمين'), max_digits=10, decimal_places=2, default_currency='MAD', default=0)
    loan_deduction = MoneyField(_('خصم القرض'), max_digits=10, decimal_places=2, default_currency='MAD', default=0)
    other_deductions = MoneyField(_('خصومات أخرى'), max_digits=10, decimal_places=2, default_currency='MAD', default=0)

    # Totals
    gross_salary = MoneyField(_('الراتب الإجمالي'), max_digits=10, decimal_places=2, default_currency='MAD')
    total_deductions = MoneyField(_('إجمالي الخصومات'), max_digits=10, decimal_places=2, default_currency='MAD')
    net_salary = MoneyField(_('الراتب الصافي'), max_digits=10, decimal_places=2, default_currency='MAD')

    # Status
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='draft')

    # Approval
    approved_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_payrolls', verbose_name=_('وافق عليه'))
    approval_date = models.DateTimeField(_('تاريخ الموافقة'), null=True, blank=True)

    # Notes
    notes = models.TextField(_('ملاحظات'), blank=True)

    class Meta:
        verbose_name = _('كشف راتب')
        verbose_name_plural = _('كشوف الرواتب')
        ordering = ['-pay_period_end', 'employee']
        unique_together = ['employee', 'pay_period_start', 'pay_period_end']
        indexes = [
            models.Index(fields=['employee', 'pay_period_end']),
            models.Index(fields=['pay_date', 'status']),
        ]

    def __str__(self):
        return f"{self.employee.full_name_ar} - {self.pay_period_start} إلى {self.pay_period_end}"

    def save(self, *args, **kwargs):
        # Calculate overtime amount
        self.overtime_amount = self.overtime_hours * self.overtime_rate

        # Calculate gross salary
        self.gross_salary = (
            self.basic_salary +
            self.housing_allowance +
            self.transport_allowance +
            self.meal_allowance +
            self.other_allowances +
            self.overtime_amount
        )

        # Calculate total deductions
        self.total_deductions = (
            self.tax_deduction +
            self.social_security +
            self.insurance_deduction +
            self.loan_deduction +
            self.other_deductions
        )

        # Calculate net salary
        self.net_salary = self.gross_salary - self.total_deductions

        super().save(*args, **kwargs)
