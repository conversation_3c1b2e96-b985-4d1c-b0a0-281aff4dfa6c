{% extends 'base.html' %}
{% load static %}

{% block title %}تسجيل الدخول - نظام إدارة وكالة السفر المغربية{% endblock %}

{% block extra_css %}
<style>
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .login-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        width: 100%;
        max-width: 400px;
    }

    .login-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .login-header h2 {
        color: #333;
        margin-bottom: 0.5rem;
    }

    .login-header p {
        color: #666;
        font-size: 0.9rem;
    }

    .form-control {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-login {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
    }

    .btn-login:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .alert {
        border-radius: 10px;
        border: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h2>مرحباً بك</h2>
            <p>سجل دخولك لنظام إدارة وكالة السفر المغربية</p>
        </div>

        {% if form.errors %}
            <div class="alert alert-danger">
                <strong>خطأ في تسجيل الدخول:</strong>
                {% for field, errors in form.errors.items %}
                    {% for error in errors %}
                        <div>{{ error }}</div>
                    {% endfor %}
                {% endfor %}
            </div>
        {% endif %}

        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}

        <form method="post">
            {% csrf_token %}

            <div class="mb-3">
                <label for="{{ form.username.id_for_label }}" class="form-label">اسم المستخدم</label>
                <input type="text"
                       class="form-control"
                       id="{{ form.username.id_for_label }}"
                       name="{{ form.username.name }}"
                       value="{{ form.username.value|default:'' }}"
                       required>
            </div>

            <div class="mb-4">
                <label for="{{ form.password.id_for_label }}" class="form-label">كلمة المرور</label>
                <input type="password"
                       class="form-control"
                       id="{{ form.password.id_for_label }}"
                       name="{{ form.password.name }}"
                       required>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                <label class="form-check-label" for="remember_me">
                    تذكرني
                </label>
            </div>

            <div class="d-grid">
                <button type="submit" class="btn btn-primary btn-login">
                    تسجيل الدخول
                </button>
            </div>

            <div class="text-center mt-3">
                <a href="{% url 'accounts:password_reset' %}" class="text-decoration-none">
                    نسيت كلمة المرور؟
                </a>
            </div>
        </form>

        <hr class="my-4">

        <div class="text-center">
            <small class="text-muted">
                بيانات تسجيل الدخول التجريبية:<br>
                <strong>المستخدم:</strong> admin<br>
                <strong>كلمة المرور:</strong> admin123
            </small>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-focus on username field
    document.addEventListener('DOMContentLoaded', function() {
        const usernameField = document.querySelector('input[name="username"]');
        if (usernameField) {
            usernameField.focus();
        }
    });
</script>
{% endblock %}
