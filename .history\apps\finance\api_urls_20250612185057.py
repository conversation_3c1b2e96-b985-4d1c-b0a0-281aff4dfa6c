"""
API URL configuration for finance app.
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import api_views

router = DefaultRouter()
router.register(r'invoices', api_views.InvoiceViewSet)
router.register(r'payments', api_views.PaymentViewSet)
router.register(r'expenses', api_views.ExpenseViewSet)
router.register(r'bank-accounts', api_views.BankAccountViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('invoices/<int:invoice_id>/payments/', api_views.InvoicePaymentListView.as_view(), name='invoice-payments'),
    path('reports/financial-summary/', api_views.FinancialSummaryView.as_view(), name='financial-summary'),
    path('reports/cash-flow/', api_views.CashFlowReportView.as_view(), name='cash-flow-report'),
]
