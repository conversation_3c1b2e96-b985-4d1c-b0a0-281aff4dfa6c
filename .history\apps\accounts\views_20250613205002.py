"""
Views for accounts app.
"""
from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.views import LoginView as DjangoLoginView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import TemplateView, ListView, DetailView, CreateView, UpdateView
from django.contrib import messages
from django.urls import reverse_lazy
from django.contrib.auth import get_user_model

User = get_user_model()


class LoginView(DjangoLoginView):
    """Custom login view."""
    template_name = 'accounts/login.html'
    redirect_authenticated_user = True

    def get_success_url(self):
        return reverse_lazy('core:dashboard')


class LogoutView(LoginRequiredMixin, TemplateView):
    """Logout view."""

    def get(self, request, *args, **kwargs):
        logout(request)
        messages.success(request, 'تم تسجيل الخروج بنجاح')
        return redirect('accounts:login')


class PasswordChangeView(LoginRequiredMixin, TemplateView):
    """Password change view."""
    template_name = 'accounts/password_change.html'


class PasswordResetView(TemplateView):
    """Password reset view."""
    template_name = 'accounts/password_reset.html'


class ProfileView(LoginRequiredMixin, TemplateView):
    """User profile view."""
    template_name = 'accounts/profile.html'


class ProfileEditView(LoginRequiredMixin, TemplateView):
    """Profile edit view."""
    template_name = 'accounts/profile_edit.html'


class UserListView(LoginRequiredMixin, ListView):
    """User list view."""
    model = User
    template_name = 'accounts/user_list.html'
    context_object_name = 'users'


class UserDetailView(LoginRequiredMixin, DetailView):
    """User detail view."""
    model = User
    template_name = 'accounts/user_detail.html'
    context_object_name = 'user'


class UserCreateView(LoginRequiredMixin, CreateView):
    """User create view."""
    model = User
    template_name = 'accounts/user_form.html'
    fields = ['username', 'email', 'first_name', 'last_name', 'role']
    success_url = reverse_lazy('accounts:user_list')


class UserUpdateView(LoginRequiredMixin, UpdateView):
    """User update view."""
    model = User
    template_name = 'accounts/user_form.html'
    fields = ['username', 'email', 'first_name', 'last_name', 'role']
    success_url = reverse_lazy('accounts:user_list')


class UserDeactivateView(LoginRequiredMixin, TemplateView):
    """User deactivate view."""
    template_name = 'accounts/user_deactivate.html'


class RoleListView(LoginRequiredMixin, TemplateView):
    """Role list view."""
    template_name = 'accounts/role_list.html'


class RoleCreateView(LoginRequiredMixin, TemplateView):
    """Role create view."""
    template_name = 'accounts/role_form.html'


class RoleUpdateView(LoginRequiredMixin, TemplateView):
    """Role update view."""
    template_name = 'accounts/role_form.html'


class PermissionListView(LoginRequiredMixin, TemplateView):
    """Permission list view."""
    template_name = 'accounts/permission_list.html'


class PermissionCreateView(LoginRequiredMixin, TemplateView):
    """Permission create view."""
    template_name = 'accounts/permission_form.html'


class SessionListView(LoginRequiredMixin, TemplateView):
    """Session list view."""
    template_name = 'accounts/session_list.html'


class SessionTerminateView(LoginRequiredMixin, TemplateView):
    """Session terminate view."""
    template_name = 'accounts/session_terminate.html'
