"""
CRM models for managing clients and customer relationships.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator
from phonenumber_field.modelfields import PhoneNumberField
from django_countries.fields import CountryField
from apps.core.models import AuditModel


class Client(AuditModel):
    """Client model for storing customer information."""

    CLIENT_TYPE_CHOICES = [
        ('individual', _('فرد')),
        ('family', _('عائلة')),
        ('group', _('مجموعة')),
        ('corporate', _('شركة')),
    ]

    GENDER_CHOICES = [
        ('M', _('ذكر')),
        ('F', _('أنثى')),
    ]

    # Basic Information
    client_code = models.CharField(_('رمز العميل'), max_length=20, unique=True)
    first_name_ar = models.CharField(_('الاسم الأول بالعربية'), max_length=50)
    last_name_ar = models.CharField(_('اسم العائلة بالعربية'), max_length=50)
    first_name_fr = models.CharField(_('الاسم الأول بالفرنسية'), max_length=50, blank=True)
    last_name_fr = models.CharField(_('اسم العائلة بالفرنسية'), max_length=50, blank=True)

    # Contact Information
    email = models.EmailField(_('البريد الإلكتروني'), blank=True)
    phone = PhoneNumberField(_('رقم الهاتف'))
    whatsapp = PhoneNumberField(_('رقم الواتساب'), blank=True)
    secondary_phone = PhoneNumberField(_('هاتف ثانوي'), blank=True)

    # Personal Details
    gender = models.CharField(_('الجنس'), max_length=1, choices=GENDER_CHOICES, blank=True)
    date_of_birth = models.DateField(_('تاريخ الميلاد'), null=True, blank=True)
    nationality = CountryField(_('الجنسية'), blank=True)

    # Documents
    passport_number = models.CharField(
        _('رقم جواز السفر'),
        max_length=20,
        blank=True,
        validators=[RegexValidator(r'^[A-Z0-9]+$', _('رقم جواز السفر يجب أن يحتوي على أحرف وأرقام فقط'))]
    )
    passport_expiry = models.DateField(_('تاريخ انتهاء جواز السفر'), null=True, blank=True)
    national_id = models.CharField(_('رقم البطاقة الوطنية'), max_length=20, blank=True)

    # Address
    address = models.TextField(_('العنوان'), blank=True)
    city = models.ForeignKey('core.City', on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_('المدينة'))
    postal_code = models.CharField(_('الرمز البريدي'), max_length=10, blank=True)

    # Business Information
    client_type = models.CharField(_('نوع العميل'), max_length=20, choices=CLIENT_TYPE_CHOICES, default='individual')
    company_name = models.CharField(_('اسم الشركة'), max_length=100, blank=True)
    tax_number = models.CharField(_('الرقم الضريبي'), max_length=20, blank=True)

    # Preferences
    preferred_language = models.CharField(
        _('اللغة المفضلة'),
        max_length=5,
        choices=[('ar', 'العربية'), ('fr', 'Français'), ('en', 'English')],
        default='ar'
    )
    special_requirements = models.TextField(_('متطلبات خاصة'), blank=True)
    dietary_restrictions = models.TextField(_('قيود غذائية'), blank=True)

    # Loyalty Program
    loyalty_points = models.PositiveIntegerField(_('نقاط الولاء'), default=0)
    vip_status = models.BooleanField(_('عميل مميز'), default=False)

    # Marketing
    marketing_consent = models.BooleanField(_('موافقة التسويق'), default=False)
    newsletter_subscription = models.BooleanField(_('اشتراك النشرة'), default=False)

    # Internal Notes
    notes = models.TextField(_('ملاحظات'), blank=True)
    assigned_agent = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('الوكيل المسؤول')
    )

    class Meta:
        verbose_name = _('عميل')
        verbose_name_plural = _('العملاء')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['client_code']),
            models.Index(fields=['email']),
            models.Index(fields=['phone']),
            models.Index(fields=['passport_number']),
        ]

    def __str__(self):
        return f"{self.client_code} - {self.full_name_ar}"

    @property
    def full_name_ar(self):
        """Return full name in Arabic."""
        return f"{self.first_name_ar} {self.last_name_ar}".strip()

    @property
    def full_name_fr(self):
        """Return full name in French."""
        if self.first_name_fr and self.last_name_fr:
            return f"{self.first_name_fr} {self.last_name_fr}".strip()
        return self.full_name_ar

    @property
    def age(self):
        """Calculate age from date of birth."""
        if self.date_of_birth:
            from datetime import date
            today = date.today()
            return today.year - self.date_of_birth.year - (
                (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day)
            )
        return None

    def save(self, *args, **kwargs):
        if not self.client_code:
            # Generate client code
            last_client = Client.objects.filter(client_code__startswith='CL').order_by('-id').first()
            if last_client:
                last_number = int(last_client.client_code[2:])
                self.client_code = f"CL{last_number + 1:06d}"
            else:
                self.client_code = "CL000001"
        super().save(*args, **kwargs)


class ClientContact(AuditModel):
    """Additional contacts for a client (family members, emergency contacts, etc.)."""
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='contacts', verbose_name=_('العميل'))

    CONTACT_TYPE_CHOICES = [
        ('family', _('فرد من العائلة')),
        ('emergency', _('جهة اتصال طوارئ')),
        ('business', _('جهة اتصال عمل')),
        ('reference', _('مرجع')),
    ]

    contact_type = models.CharField(_('نوع جهة الاتصال'), max_length=20, choices=CONTACT_TYPE_CHOICES)
    first_name = models.CharField(_('الاسم الأول'), max_length=50)
    last_name = models.CharField(_('اسم العائلة'), max_length=50)
    relationship = models.CharField(_('صلة القرابة'), max_length=50, blank=True)
    phone = PhoneNumberField(_('رقم الهاتف'))
    email = models.EmailField(_('البريد الإلكتروني'), blank=True)
    notes = models.TextField(_('ملاحظات'), blank=True)

    class Meta:
        verbose_name = _('جهة اتصال العميل')
        verbose_name_plural = _('جهات اتصال العملاء')

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.get_contact_type_display()})"


class ClientCommunication(AuditModel):
    """Track all communications with clients."""
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='communications', verbose_name=_('العميل'))

    COMMUNICATION_TYPE_CHOICES = [
        ('call', _('مكالمة هاتفية')),
        ('email', _('بريد إلكتروني')),
        ('whatsapp', _('واتساب')),
        ('sms', _('رسالة نصية')),
        ('meeting', _('اجتماع')),
        ('visit', _('زيارة')),
        ('other', _('أخرى')),
    ]

    DIRECTION_CHOICES = [
        ('inbound', _('واردة')),
        ('outbound', _('صادرة')),
    ]

    communication_type = models.CharField(_('نوع التواصل'), max_length=20, choices=COMMUNICATION_TYPE_CHOICES)
    direction = models.CharField(_('الاتجاه'), max_length=10, choices=DIRECTION_CHOICES)
    subject = models.CharField(_('الموضوع'), max_length=200)
    content = models.TextField(_('المحتوى'))
    staff_member = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, verbose_name=_('الموظف'))
    communication_date = models.DateTimeField(_('تاريخ التواصل'))
    follow_up_required = models.BooleanField(_('يتطلب متابعة'), default=False)
    follow_up_date = models.DateTimeField(_('تاريخ المتابعة'), null=True, blank=True)

    # Attachments
    attachment = models.FileField(_('مرفق'), upload_to='communications/', blank=True)

    class Meta:
        verbose_name = _('تواصل مع العميل')
        verbose_name_plural = _('تواصل مع العملاء')
        ordering = ['-communication_date']

    def __str__(self):
        return f"{self.client.full_name_ar} - {self.subject}"


class ClientPreference(AuditModel):
    """Store client travel preferences and history."""
    client = models.OneToOneField(Client, on_delete=models.CASCADE, related_name='preferences', verbose_name=_('العميل'))

    # Travel Preferences
    preferred_destinations = models.ManyToManyField('tours.Destination', blank=True, verbose_name=_('الوجهات المفضلة'))
    preferred_accommodation_type = models.CharField(
        _('نوع الإقامة المفضل'),
        max_length=20,
        choices=[
            ('hotel', _('فندق')),
            ('resort', _('منتجع')),
            ('apartment', _('شقة')),
            ('villa', _('فيلا')),
            ('hostel', _('نزل')),
        ],
        blank=True
    )
    preferred_room_type = models.CharField(
        _('نوع الغرفة المفضل'),
        max_length=20,
        choices=[
            ('single', _('فردية')),
            ('double', _('مزدوجة')),
            ('twin', _('سريرين')),
            ('suite', _('جناح')),
            ('family', _('عائلية')),
        ],
        blank=True
    )
    budget_range_min = models.DecimalField(_('الحد الأدنى للميزانية'), max_digits=10, decimal_places=2, null=True, blank=True)
    budget_range_max = models.DecimalField(_('الحد الأقصى للميزانية'), max_digits=10, decimal_places=2, null=True, blank=True)

    # Travel Style
    travel_style = models.CharField(
        _('أسلوب السفر'),
        max_length=20,
        choices=[
            ('luxury', _('فاخر')),
            ('comfort', _('مريح')),
            ('budget', _('اقتصادي')),
            ('adventure', _('مغامرة')),
            ('cultural', _('ثقافي')),
            ('relaxation', _('استرخاء')),
        ],
        blank=True
    )

    # Group Preferences
    typical_group_size = models.PositiveIntegerField(_('حجم المجموعة المعتاد'), null=True, blank=True)
    travels_with_children = models.BooleanField(_('يسافر مع أطفال'), default=False)

    # Booking Preferences
    preferred_booking_lead_time = models.PositiveIntegerField(_('فترة الحجز المفضلة (أيام)'), null=True, blank=True)
    flexible_dates = models.BooleanField(_('تواريخ مرنة'), default=False)

    class Meta:
        verbose_name = _('تفضيلات العميل')
        verbose_name_plural = _('تفضيلات العملاء')

    def __str__(self):
        return f"تفضيلات {self.client.full_name_ar}"
