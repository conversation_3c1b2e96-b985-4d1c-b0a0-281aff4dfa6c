// CRM Module Functions

// Initialize Notifications
const notify = {
    success: (message) => {
        toastr.success(message, '', {
            closeButton: true,
            progressBar: true,
            rtl: true
        });
    },
    error: (message) => {
        toastr.error(message, '', {
            closeButton: true,
            progressBar: true,
            rtl: true,
            timeOut: 0,
            extendedTimeOut: 0
        });
    },
    info: (message) => {
        toastr.info(message, '', {
            closeButton: true,
            progressBar: true,
            rtl: true
        });
    }
};

// Client Input Validation
const validateClient = (client) => {
    const errors = [];

    if (!client.full_name_ar?.trim()) {
        errors.push('الاسم بالعربية مطلوب');
    }

    if (!client.phone?.trim()) {
        errors.push('رقم الهاتف مطلوب');
    } else if (!/^(\+212|0)[567]\d{8}$/.test(client.phone)) {
        errors.push('رقم الهاتف غير صالح');
    }

    if (client.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(client.email)) {
        errors.push('البريد الإلكتروني غير صالح');
    }

    if (client.passport_number && !/^[A-Z0-9]{6,12}$/.test(client.passport_number)) {
        errors.push('رقم جواز السفر غير صالح');
    }

    return errors;
};

// Contact Validation
const validateContact = (contact) => {
    const errors = [];

    if (!contact.first_name_ar?.trim()) {
        errors.push('الاسم الأول بالعربية مطلوب');
    }

    if (!contact.last_name_ar?.trim()) {
        errors.push('اسم العائلة بالعربية مطلوب');
    }

    if (!contact.phone?.trim()) {
        errors.push('رقم الهاتف مطلوب');
    } else if (!/^(\+212|0)[567]\d{8}$/.test(contact.phone)) {
        errors.push('رقم الهاتف غير صالح');
    }

    if (!contact.email?.trim()) {
        errors.push('البريد الإلكتروني مطلوب');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contact.email)) {
        errors.push('البريد الإلكتروني غير صالح');
    }

    if (!contact.position?.trim()) {
        errors.push('المنصب مطلوب');
    }

    return errors;
};

// Interaction Validation
const validateInteraction = (interaction) => {
    const errors = [];

    if (!interaction.type) {
        errors.push('نوع التفاعل مطلوب');
    }

    if (!interaction.subject?.trim()) {
        errors.push('موضوع التفاعل مطلوب');
    }

    if (!interaction.description?.trim()) {
        errors.push('وصف التفاعل مطلوب');
    }

    if (!interaction.date) {
        errors.push('تاريخ التفاعل مطلوب');
    }

    return errors;
};

// Format Phone Numbers
const formatPhoneNumber = (phone) => {
    if (!phone) return '';

    // Format Moroccan phone numbers
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
        return cleaned.replace(/(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4 $5');
    }
    return phone;
};

// Format Dates for Interactions
const formatInteractionDate = (date) => {
    if (!date) return '';
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    };
    return new Date(date).toLocaleString('ar-MA', options);
};

// Cache Management
const cacheKey = 'crm_clients_cache';
const cacheExpiry = 5 * 60 * 1000; // 5 minutes

const getCachedClients = () => {
    const cached = localStorage.getItem(cacheKey);
    if (cached) {
        const { timestamp, data } = JSON.parse(cached);
        if (Date.now() - timestamp < cacheExpiry) {
            return data;
        }
    }
    return null;
};

const setCachedClients = (clients) => {
    localStorage.setItem(cacheKey, JSON.stringify({
        timestamp: Date.now(),
        data: clients
    }));
};

// Offline Support
let offlineQueue = [];

const processOfflineQueue = async () => {
    if (navigator.onLine && offlineQueue.length > 0) {
        notify.info(`جاري معالجة ${offlineQueue.length} عملية معلقة...`);

        for (const task of offlineQueue) {
            try {
                await task();
            } catch (error) {
                console.error('Error processing offline task:', error);
            }
        }

        offlineQueue = [];
        notify.success('تم معالجة جميع العمليات المعلقة');
    }
};

window.addEventListener('online', processOfflineQueue);

// Get Interaction Status Badge Class
const getStatusBadgeClass = (status) => {
    const classes = {
        'open': 'badge-info',
        'in_progress': 'badge-primary',
        'pending': 'badge-warning',
        'resolved': 'badge-success',
        'closed': 'badge-secondary'
    };
    return classes[status] || 'badge-light';
};

// Get Priority Badge Class
const getPriorityBadgeClass = (priority) => {
    const classes = {
        'low': 'badge-info',
        'medium': 'badge-warning',
        'high': 'badge-danger',
        'urgent': 'badge-danger blink'
    };
    return classes[priority] || 'badge-light';
};

// Get Interaction Type Icon
const getInteractionTypeIcon = (type) => {
    const icons = {
        'call': 'fas fa-phone',
        'email': 'fas fa-envelope',
        'meeting': 'fas fa-users',
        'whatsapp': 'fab fa-whatsapp',
        'visit': 'fas fa-building',
        'other': 'fas fa-comment'
    };
    return icons[type] || 'fas fa-comment';
};

// Calculate Follow-up Status
const getFollowupStatus = (interaction) => {
    if (!interaction.requires_followup) {
        return { status: 'none', className: 'text-muted' };
    }

    if (!interaction.followup_date) {
        return { status: 'pending', className: 'text-warning' };
    }

    const followupDate = new Date(interaction.followup_date);
    const now = new Date();

    if (interaction.completed_at) {
        return { status: 'completed', className: 'text-success' };
    }

    if (followupDate < now) {
        return { status: 'overdue', className: 'text-danger' };
    }

    // Check if follow-up is due within 24 hours
    const hours24 = 24 * 60 * 60 * 1000;
    if ((followupDate - now) <= hours24) {
        return { status: 'due-soon', className: 'text-warning' };
    }

    return { status: 'scheduled', className: 'text-info' };
};

// Handle offline interactions
const queueInteraction = (interaction) => {
    if (!navigator.onLine) {
        offlineQueue.push(async () => {
            await axios.post('/api/crm/interactions/', interaction);
        });

        // Store in IndexedDB for persistence
        const dbRequest = indexedDB.open('crmOfflineDB', 1);

        dbRequest.onerror = (event) => {
            console.error('IndexedDB error:', event.target.error);
        };

        dbRequest.onupgradeneeded = (event) => {
            const db = event.target.result;
            if (!db.objectStoreNames.contains('interactions')) {
                db.createObjectStore('interactions', { keyPath: 'id', autoIncrement: true });
            }
        };

        dbRequest.onsuccess = (event) => {
            const db = event.target.result;
            const transaction = db.transaction(['interactions'], 'readwrite');
            const store = transaction.objectStore('interactions');
            store.add({
                ...interaction,
                status: 'pending',
                created_at: new Date().toISOString()
            });
        };

        notify.info('تم حفظ التفاعل للمزامنة لاحقاً');
        return true;
    }
    return false;
};

// Export Client Data
const exportClientData = async (format) => {
    try {
        const response = await axios.get(`/api/clients/export/?format=${format}`, {
            responseType: 'blob'
        });

        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `clients_${new Date().toISOString().split('T')[0]}.${format}`);
        document.body.appendChild(link);
        link.click();
        link.remove();
    } catch (error) {
        notify.error('حدث خطأ أثناء تصدير البيانات');
        console.error('Export error:', error);
    }
};

// Export functions
window.crmUtils = {
    notify,
    validateClient,
    exportClientData,
    formatPhoneNumber,
    getCachedClients,
    setCachedClients,
    validateContact,
    validateInteraction,
    formatInteractionDate,
    getStatusBadgeClass,
    getPriorityBadgeClass,
    getInteractionTypeIcon,
    getFollowupStatus,
    queueInteraction
};
