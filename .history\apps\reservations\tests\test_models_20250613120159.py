import pytest
from django.test import TestCase
from django.utils import timezone
from apps.reservations.models import Reservation
from apps.accounts.models import User
from apps.crm.models import Client
from apps.tours.models import TourPackage
from djmoney.money import Money

class TestReservationModel(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test user
        cls.user = User.objects.create(username="testuser")

        # Create test client
        cls.client = Client.objects.create(
            first_name="Test",
            last_name="Client"
        )

        # Create test package
        cls.package = TourPackage.objects.create(
            name="Test Package",
            description="Test Description"
        )

    def test_create_reservation(self):
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date(),
            adults=2,
            adult_price=Money(1000, 'MAD'),
            status='pending'
        )
        self.assertEqual(reservation.status, 'pending')
        self.assertEqual(reservation.adults, 2)

    def test_reservation_total_participants(self):
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date(),
            adults=2,
            children=1,
            infants=1,
            adult_price=Money(1000, 'MAD')
        )
        self.assertEqual(reservation.total_participants, 4)

    def test_reservation_str(self):
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date(),
            adult_price=Money(1000, 'MAD')
        )
        expected_str = f"{reservation.reservation_number} - {self.client.full_name_ar}"
        self.assertEqual(str(reservation), expected_str)
