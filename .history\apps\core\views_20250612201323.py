"""
Views for the core app.
"""
from django.shortcuts import render
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Count, Sum
from django.utils.translation import gettext_lazy as _


class DashboardView(TemplateView):
    """Main dashboard view."""
    template_name = 'core/dashboard_simple.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Import here to avoid circular imports
        from apps.crm.models import Client
        from apps.tours.models import TourPackage
        from apps.accounts.models import User

        # Dashboard statistics
        context.update({
            'total_clients': Client.objects.count(),
            'total_packages': TourPackage.objects.filter(is_active=True).count(),
            'featured_packages': TourPackage.objects.filter(is_featured=True, is_active=True).count(),
            'total_users': User.objects.filter(is_active=True).count(),
            'vip_clients': Client.objects.filter(vip_status=True).count(),
            'active_bookings': 0,  # Will be implemented when reservations module is added
            'monthly_sales': 0,    # Will be implemented when finance module is added
            'recent_clients': Client.objects.order_by('-created_at')[:5],
        })

        return context


class QuickStatsView(TemplateView):
    """Quick statistics view."""
    template_name = 'core/quick_stats.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Import here to avoid circular imports
        from apps.crm.models import Client
        from apps.tours.models import TourPackage, Destination
        from apps.accounts.models import User

        # Detailed statistics
        context.update({
            'total_clients': Client.objects.count(),
            'individual_clients': Client.objects.filter(client_type='individual').count(),
            'corporate_clients': Client.objects.filter(client_type='corporate').count(),
            'vip_clients': Client.objects.filter(vip_status=True).count(),
            'total_packages': TourPackage.objects.filter(is_active=True).count(),
            'active_packages': TourPackage.objects.filter(is_active=True).count(),
            'featured_packages': TourPackage.objects.filter(is_featured=True, is_active=True).count(),
            'total_destinations': Destination.objects.filter(is_active=True).count(),
            'total_users': User.objects.filter(is_active=True).count(),
        })

        return context


class SettingsView(LoginRequiredMixin, TemplateView):
    """System settings view."""
    template_name = 'core/settings.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from .models import SystemSettings

        context['settings'] = SystemSettings.objects.filter(is_active=True)
        return context
