{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}لوحة تحكم الموارد البشرية{% endblock %}

{% block extra_css %}
<style>
.hr-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.hr-stat {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    margin-bottom: 15px;
}

.hr-stat h3 {
    font-size: 2rem;
    margin-bottom: 5px;
    color: #fff;
}

.employees-card {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.departments-card {
    background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
}

.leaves-card {
    background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
}

.attendance-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.leave-item {
    background: white;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 4px solid #667eea;
}

.leave-item.urgent {
    border-left-color: #ff6b6b;
    background: linear-gradient(135deg, rgba(255,107,107,0.1) 0%, rgba(255,107,107,0.05) 100%);
}

.department-item {
    background: white;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 4px solid #11998e;
}

.chart-container {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.btn-hr {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-hr:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.attendance-gauge {
    position: relative;
    width: 150px;
    height: 150px;
    margin: 0 auto;
}

.attendance-percentage {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.5rem;
    font-weight: bold;
    color: #667eea;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">👥 لوحة تحكم الموارد البشرية</h1>
                    <p class="text-muted">إدارة شاملة للموظفين والموارد البشرية</p>
                </div>
                <div>
                    <a href="{% url 'hr:employee_list' %}" class="btn btn-hr me-2">
                        <i class="fas fa-users"></i> الموظفين
                    </a>
                    <a href="{% url 'hr:reports' %}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-bar"></i> التقارير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="hr-card employees-card">
                <div class="hr-stat">
                    <h3>{{ stats.total_employees }}</h3>
                    <p><i class="fas fa-users"></i> إجمالي الموظفين</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="hr-card departments-card">
                <div class="hr-stat">
                    <h3>{{ stats.total_departments }}</h3>
                    <p><i class="fas fa-building"></i> الأقسام</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="hr-card leaves-card">
                <div class="hr-stat">
                    <h3>{{ stats.pending_leaves }}</h3>
                    <p><i class="fas fa-calendar-times"></i> إجازات معلقة</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="hr-card attendance-card">
                <div class="hr-stat">
                    <h3>{{ stats.monthly_attendance_rate }}%</h3>
                    <p><i class="fas fa-clock"></i> معدل الحضور</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Statistics -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="hr-card">
                <h5><i class="fas fa-calendar-month"></i> إحصائيات الشهر الحالي</h5>
                <div class="row">
                    <div class="col-6">
                        <div class="hr-stat">
                            <h4>{{ stats.employees_on_leave }}</h4>
                            <p>موظفين في إجازة</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="hr-stat">
                            <h4>{{ stats.total_payroll|floatformat:0 }} درهم</h4>
                            <p>إجمالي الرواتب</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="hr-card">
                <h5><i class="fas fa-chart-pie"></i> معدل الحضور الأسبوعي</h5>
                <div class="attendance-gauge">
                    <canvas id="attendanceGauge" width="150" height="150"></canvas>
                    <div class="attendance-percentage">{{ stats.monthly_attendance_rate }}%</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Lists -->
    <div class="row">
        <!-- Attendance Trends -->
        <div class="col-lg-8">
            <div class="chart-container">
                <h5><i class="fas fa-chart-line"></i> اتجاه الحضور الأسبوعي</h5>
                <canvas id="attendanceChart" height="100"></canvas>
            </div>
        </div>

        <!-- Recent Leaves -->
        <div class="col-lg-4">
            <div class="chart-container">
                <h5><i class="fas fa-calendar-times"></i> أحدث طلبات الإجازة</h5>
                <div class="list-group list-group-flush">
                    {% for leave in recent_leaves %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">{{ leave.employee.get_full_name }}</h6>
                            <p class="mb-1 text-muted">{{ leave.get_leave_type_display }}</p>
                            <small>{{ leave.start_date }} - {{ leave.end_date }}</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-primary">{{ leave.days_requested }} يوم</span>
                            <br>
                            <small class="badge bg-{{ leave.status|yesno:'success,warning,danger' }}">
                                {{ leave.get_status_display }}
                            </small>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p>لا توجد طلبات إجازة حديثة</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Department Distribution -->
    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="chart-container">
                <h5><i class="fas fa-building"></i> توزيع الموظفين حسب القسم</h5>
                {% if department_stats %}
                <div class="row">
                    {% for dept in department_stats %}
                    <div class="col-12">
                        <div class="department-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ dept.name_ar }}</h6>
                                    <p class="mb-0 text-muted">
                                        {% if dept.manager %}
                                        <i class="fas fa-user-tie"></i> {{ dept.manager.get_full_name }}
                                        {% else %}
                                        <i class="fas fa-user-slash"></i> لا يوجد مدير
                                        {% endif %}
                                    </p>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-primary fs-6">{{ dept.employee_count }} موظف</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-building fa-3x mb-3"></i>
                    <h5>لا توجد أقسام</h5>
                    <p>لم يتم إنشاء أي أقسام بعد</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Upcoming Leaves -->
        <div class="col-lg-6">
            <div class="chart-container">
                <h5><i class="fas fa-plane-departure"></i> الإجازات القادمة</h5>
                {% if upcoming_leaves %}
                <div class="row">
                    {% for leave in upcoming_leaves %}
                    <div class="col-12">
                        <div class="leave-item {% if leave.start_date|timeuntil|slice:':1' == '1' %}urgent{% endif %}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ leave.employee.get_full_name }}</h6>
                                    <p class="mb-1 text-muted">{{ leave.get_leave_type_display }}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar"></i> {{ leave.start_date }} - {{ leave.end_date }}
                                        <span class="ms-2">
                                            <i class="fas fa-clock"></i> {{ leave.days_requested }} يوم
                                        </span>
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-info">{{ leave.get_status_display }}</span>
                                    <br>
                                    <small class="text-muted">{{ leave.start_date|timeuntil }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-calendar-check fa-3x mb-3"></i>
                    <h5>لا توجد إجازات قادمة</h5>
                    <p>لا توجد إجازات مجدولة للأسبوعين القادمين</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Attendance Trends Chart
const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
const attendanceChart = new Chart(attendanceCtx, {
    type: 'line',
    data: {
        labels: [{% for data in attendance_data %}'{{ data.date }}'{% if not forloop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'معدل الحضور (%)',
            data: [{% for data in attendance_data %}{{ data.rate }}{% if not forloop.last %},{% endif %}{% endfor %}],
            borderColor: 'rgb(102, 126, 234)',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                max: 100,
                ticks: {
                    callback: function(value) {
                        return value + '%';
                    }
                }
            }
        }
    }
});

// Attendance Gauge
const gaugeCtx = document.getElementById('attendanceGauge').getContext('2d');
const attendanceRate = {{ stats.monthly_attendance_rate }};

// Draw gauge
gaugeCtx.beginPath();
gaugeCtx.arc(75, 75, 60, 0, 2 * Math.PI);
gaugeCtx.strokeStyle = '#e9ecef';
gaugeCtx.lineWidth = 10;
gaugeCtx.stroke();

// Draw progress
const progressAngle = (attendanceRate / 100) * 2 * Math.PI;
gaugeCtx.beginPath();
gaugeCtx.arc(75, 75, 60, -Math.PI/2, -Math.PI/2 + progressAngle);
gaugeCtx.strokeStyle = attendanceRate >= 80 ? '#38ef7d' : attendanceRate >= 60 ? '#fdbb2d' : '#fc466b';
gaugeCtx.lineWidth = 10;
gaugeCtx.lineCap = 'round';
gaugeCtx.stroke();
</script>
{% endblock %}
