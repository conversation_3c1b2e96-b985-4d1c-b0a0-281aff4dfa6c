{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .search-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
        margin-bottom: 20px;
    }
    .client-card {
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: none;
    }
    .client-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
    }
    .client-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.2rem;
    }
    .vip-badge {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border: none;
        border-radius: 20px;
    }
    .table-responsive {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .btn-action {
        border-radius: 20px;
        padding: 5px 15px;
        font-size: 0.875rem;
    }
    .pagination {
        justify-content: center;
    }
    .page-link {
        border-radius: 20px;
        margin: 0 2px;
        border: none;
        color: #667eea;
    }
    .page-link:hover {
        background-color: #667eea;
        color: white;
    }
    .page-item.active .page-link {
        background-color: #667eea;
        border-color: #667eea;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-users me-2 text-primary"></i>
                {{ page_title }}
            </h1>
            <p class="text-muted mb-0">
                {% trans "إجمالي" %} {{ total_count }} {% trans "عميل" %}
            </p>
        </div>
        <div>
            <a href="{% url 'crm:client_create' %}" class="btn btn-primary me-2">
                <i class="fas fa-plus me-2"></i>
                {% trans "إضافة عميل جديد" %}
            </a>
            <a href="{% url 'crm:client_export' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
               class="btn btn-outline-success">
                <i class="fas fa-file-export me-2"></i>
                {% trans "تصدير" %}
            </a>
        </div>
    </div>

    <!-- Search Form -->
    <div class="card search-card">
        <div class="card-body">
            <h5 class="card-title mb-3">
                <i class="fas fa-search me-2"></i>
                {% trans "البحث والتصفية" %}
            </h5>
            {% crispy form %}
        </div>
    </div>

    <!-- Results -->
    <div class="card">
        <div class="card-header bg-white border-0">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2 text-primary"></i>
                    {% trans "نتائج البحث" %}
                </h5>
                <div class="btn-group" role="group">
                    <input type="radio" class="btn-check" name="view-mode" id="table-view" checked>
                    <label class="btn btn-outline-primary" for="table-view">
                        <i class="fas fa-table"></i>
                    </label>

                    <input type="radio" class="btn-check" name="view-mode" id="card-view">
                    <label class="btn btn-outline-primary" for="card-view">
                        <i class="fas fa-th-large"></i>
                    </label>
                </div>
            </div>
        </div>

        <div class="card-body p-0">
            <!-- Table View -->
            <div id="table-view-content" class="view-content">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>{% trans "العميل" %}</th>
                                <th>{% trans "معلومات الاتصال" %}</th>
                                <th>{% trans "النوع" %}</th>
                                <th>{% trans "الحالة" %}</th>
                                <th>{% trans "تاريخ الإنشاء" %}</th>
                                <th>{% trans "الإجراءات" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for client in clients %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="client-avatar me-3">
                                            {{ client.first_name_ar|first }}{{ client.last_name_ar|first }}
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ client.full_name_ar }}</h6>
                                            <small class="text-muted">{{ client.client_code }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <i class="fas fa-envelope me-1 text-muted"></i>
                                        {{ client.email|default:"-" }}
                                    </div>
                                    <div>
                                        <i class="fas fa-phone me-1 text-muted"></i>
                                        {{ client.phone|default:"-" }}
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ client.get_client_type_display }}</span>
                                </td>
                                <td>
                                    {% if client.vip_status %}
                                    <span class="badge vip-badge">
                                        <i class="fas fa-crown me-1"></i>
                                        VIP
                                    </span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% trans "عادي" %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small>{{ client.created_at|date:"Y-m-d" }}</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'crm:client_detail' client.pk %}"
                                           class="btn btn-sm btn-outline-primary btn-action"
                                           title="{% trans 'عرض' %}">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'crm:client_edit' client.pk %}"
                                           class="btn btn-sm btn-outline-warning btn-action"
                                           title="{% trans 'تعديل' %}">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button"
                                                class="btn btn-sm btn-outline-danger btn-action delete-client"
                                                data-client-id="{{ client.pk }}"
                                                data-client-name="{{ client.full_name_ar }}"
                                                title="{% trans 'حذف' %}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">{% trans "لا توجد عملاء" %}</h5>
                                    <p class="text-muted">{% trans "لم يتم العثور على عملاء مطابقين لمعايير البحث" %}</p>
                                    <a href="{% url 'crm:client_create' %}" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>
                                        {% trans "إضافة عميل جديد" %}
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Card View -->
            <div id="card-view-content" class="view-content d-none">
                <div class="row p-3">
                    {% for client in clients %}
                    <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                        <div class="card client-card h-100">
                            <div class="card-body text-center">
                                <div class="client-avatar mx-auto mb-3">
                                    {{ client.first_name_ar|first }}{{ client.last_name_ar|first }}
                                </div>
                                <h6 class="card-title">{{ client.full_name_ar }}</h6>
                                <p class="card-text text-muted small">{{ client.client_code }}</p>

                                <div class="mb-3">
                                    {% if client.vip_status %}
                                    <span class="badge vip-badge">
                                        <i class="fas fa-crown me-1"></i>
                                        VIP
                                    </span>
                                    {% endif %}
                                    <span class="badge bg-info">{{ client.get_client_type_display }}</span>
                                </div>

                                <div class="text-muted small mb-3">
                                    <div><i class="fas fa-envelope me-1"></i> {{ client.email|truncatechars:20 }}</div>
                                    <div><i class="fas fa-phone me-1"></i> {{ client.phone }}</div>
                                </div>

                                <div class="btn-group w-100" role="group">
                                    <a href="{% url 'crm:client_detail' client.pk %}"
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'crm:client_edit' client.pk %}"
                                       class="btn btn-sm btn-outline-warning">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button"
                                            class="btn btn-sm btn-outline-danger delete-client"
                                            data-client-id="{{ client.pk }}"
                                            data-client-name="{{ client.full_name_ar }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">{% trans "لا توجد عملاء" %}</h5>
                        <p class="text-muted">{% trans "لم يتم العثور على عملاء مطابقين لمعايير البحث" %}</p>
                        <a href="{% url 'crm:client_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            {% trans "إضافة عميل جديد" %}
                        </a>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <div class="card-footer bg-white border-0">
            <nav aria-label="{% trans 'تصفح الصفحات' %}">
                <ul class="pagination">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if request.GET %}&{{ request.GET.urlencode }}{% endif %}">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET %}&{{ request.GET.urlencode }}{% endif %}">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if request.GET %}&{{ request.GET.urlencode }}{% endif %}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET %}&{{ request.GET.urlencode }}{% endif %}">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET %}&{{ request.GET.urlencode }}{% endif %}">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "تأكيد الحذف" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>{% trans "هل أنت متأكد من حذف العميل" %} <strong id="client-name"></strong>؟</p>
                <p class="text-danger">{% trans "هذا الإجراء لا يمكن التراجع عنه." %}</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-danger" id="confirm-delete">{% trans "حذف" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// View mode toggle
document.querySelectorAll('input[name="view-mode"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const tableView = document.getElementById('table-view-content');
        const cardView = document.getElementById('card-view-content');

        if (this.id === 'table-view') {
            tableView.classList.remove('d-none');
            cardView.classList.add('d-none');
        } else {
            tableView.classList.add('d-none');
            cardView.classList.remove('d-none');
        }
    });
});

// Delete client functionality
let clientToDelete = null;

document.querySelectorAll('.delete-client').forEach(button => {
    button.addEventListener('click', function() {
        clientToDelete = this.dataset.clientId;
        document.getElementById('client-name').textContent = this.dataset.clientName;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    });
});

document.getElementById('confirm-delete').addEventListener('click', function() {
    if (clientToDelete) {
        fetch(`/crm/clients/${clientToDelete}/delete/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{% trans "حدث خطأ أثناء الحذف" %}');
        });
    }
});

// Reset form function
function resetForm() {
    document.querySelector('.search-form').reset();
    window.location.href = window.location.pathname;
}
</script>
{% endblock %}
