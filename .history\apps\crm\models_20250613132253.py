"""
CRM models for managing clients and customer relationships.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator
# from phonenumber_field.modelfields import PhoneNumberField
# from django_countries.fields import CountryField
from apps.core.models import TimeStampedModel


class Client(TimeStampedModel):
    """Client model for storing customer information."""

    CLIENT_TYPE_CHOICES = [
        ('individual', _('فرد')),
        ('family', _('عائلة')),
        ('group', _('مجموعة')),
        ('corporate', _('شركة')),
    ]

    GENDER_CHOICES = [
        ('M', _('ذكر')),
        ('F', _('أنثى')),
    ]

    # Basic Information
    client_code = models.CharField(_('رمز العميل'), max_length=20, unique=True)
    first_name_ar = models.CharField(_('الاسم الأول بالعربية'), max_length=50)
    last_name_ar = models.CharField(_('اسم العائلة بالعربية'), max_length=50)
    first_name_fr = models.CharField(_('الاسم الأول بالفرنسية'), max_length=50, blank=True)
    last_name_fr = models.CharField(_('اسم العائلة بالفرنسية'), max_length=50, blank=True)

    # Contact Information
    email = models.EmailField(_('البريد الإلكتروني'), blank=True)
    phone = models.CharField(_('رقم الهاتف'), max_length=20)
    whatsapp = models.CharField(_('رقم الواتساب'), max_length=20, blank=True)
    secondary_phone = models.CharField(_('هاتف ثانوي'), max_length=20, blank=True)

    # Personal Details
    gender = models.CharField(_('الجنس'), max_length=1, choices=GENDER_CHOICES, blank=True)
    date_of_birth = models.DateField(_('تاريخ الميلاد'), null=True, blank=True)
    nationality = models.CharField(_('الجنسية'), max_length=50, blank=True)

    # Documents
    passport_number = models.CharField(
        _('رقم جواز السفر'),
        max_length=20,
        blank=True,
        validators=[RegexValidator(r'^[A-Z0-9]+$', _('رقم جواز السفر يجب أن يحتوي على أحرف وأرقام فقط'))]
    )
    passport_expiry = models.DateField(_('تاريخ انتهاء جواز السفر'), null=True, blank=True)
    national_id = models.CharField(_('رقم البطاقة الوطنية'), max_length=20, blank=True)

    # Address
    address = models.TextField(_('العنوان'), blank=True)
    city = models.ForeignKey('core.City', on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_('المدينة'))
    postal_code = models.CharField(_('الرمز البريدي'), max_length=10, blank=True)

    # Business Information
    client_type = models.CharField(_('نوع العميل'), max_length=20, choices=CLIENT_TYPE_CHOICES, default='individual')
    company_name = models.CharField(_('اسم الشركة'), max_length=100, blank=True)
    tax_number = models.CharField(_('الرقم الضريبي'), max_length=20, blank=True)

    # Preferences
    preferred_language = models.CharField(
        _('اللغة المفضلة'),
        max_length=5,
        choices=[('ar', 'العربية'), ('fr', 'Français'), ('en', 'English')],
        default='ar'
    )
    special_requirements = models.TextField(_('متطلبات خاصة'), blank=True)
    dietary_restrictions = models.TextField(_('قيود غذائية'), blank=True)

    # Loyalty Program
    loyalty_points = models.PositiveIntegerField(_('نقاط الولاء'), default=0)
    vip_status = models.BooleanField(_('عميل مميز'), default=False)

    # Marketing
    marketing_consent = models.BooleanField(_('موافقة التسويق'), default=False)
    newsletter_subscription = models.BooleanField(_('اشتراك النشرة'), default=False)

    # Internal Notes
    notes = models.TextField(_('ملاحظات'), blank=True)
    assigned_agent = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('الوكيل المسؤول')
    )

    class Meta:
        verbose_name = _('عميل')
        verbose_name_plural = _('العملاء')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['client_code']),
            models.Index(fields=['email']),
            models.Index(fields=['phone']),
            models.Index(fields=['passport_number']),
        ]

    def __str__(self):
        return f"{self.client_code} - {self.full_name_ar}"

    @property
    def full_name_ar(self):
        """Return full name in Arabic."""
        return f"{self.first_name_ar} {self.last_name_ar}".strip()

    @property
    def full_name_fr(self):
        """Return full name in French."""
        if self.first_name_fr and self.last_name_fr:
            return f"{self.first_name_fr} {self.last_name_fr}".strip()
        return self.full_name_ar

    @property
    def age(self):
        """Calculate age from date of birth."""
        if self.date_of_birth:
            from datetime import date
            today = date.today()
            return today.year - self.date_of_birth.year - (
                (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day)
            )
        return None

    def save(self, *args, **kwargs):
        if not self.client_code:
            # Generate client code
            last_client = Client.objects.filter(client_code__startswith='CL').order_by('-id').first()
            if last_client:
                last_number = int(last_client.client_code[2:])
                self.client_code = f"CL{last_number + 1:06d}"
            else:
                self.client_code = "CL000001"
        super().save(*args, **kwargs)


class Contact(TimeStampedModel):
    """Contact persons for company/group clients."""

    # Basic Information
    client = models.ForeignKey(
        Client,
        on_delete=models.CASCADE,
        related_name='contacts',
        verbose_name=_('العميل')
    )
    first_name_ar = models.CharField(
        _('الاسم الأول بالعربية'),
        max_length=50
    )
    last_name_ar = models.CharField(
        _('اسم العائلة بالعربية'),
        max_length=50
    )
    first_name_fr = models.CharField(
        _('الاسم الأول بالفرنسية'),
        max_length=50,
        blank=True
    )
    last_name_fr = models.CharField(
        _('اسم العائلة بالفرنسية'),
        max_length=50,
        blank=True
    )

    # Position
    position = models.CharField(_('المنصب'), max_length=100)
    department = models.CharField(_('القسم'), max_length=100, blank=True)

    # Contact Information
    email = models.EmailField(_('البريد الإلكتروني'))
    phone = models.CharField(_('رقم الهاتف'), max_length=20)
    mobile = models.CharField(_('الجوال'), max_length=20, blank=True)
    whatsapp = models.CharField(_('رقم الواتساب'), max_length=20, blank=True)

    # Status
    is_primary = models.BooleanField(_('جهة اتصال رئيسية'), default=False)
    is_active = models.BooleanField(_('نشط'), default=True)
    notes = models.TextField(_('ملاحظات'), blank=True)

    class Meta:
        verbose_name = _('جهة اتصال')
        verbose_name_plural = _('جهات الاتصال')
        ordering = ['-is_primary', 'first_name_ar']
        indexes = [
            models.Index(fields=['client', 'is_primary']),
            models.Index(fields=['email']),
            models.Index(fields=['phone']),
        ]

    def __str__(self):
        return f"{self.full_name_ar} - {self.client.full_name_ar}"

    @property
    def full_name_ar(self):
        """Return full name in Arabic."""
        return f"{self.first_name_ar} {self.last_name_ar}".strip()

    @property
    def full_name_fr(self):
        """Return full name in French."""
        if self.first_name_fr and self.last_name_fr:
            return f"{self.first_name_fr} {self.last_name_fr}".strip()
        return self.full_name_ar

    def save(self, *args, **kwargs):
        # If this is marked as primary, unmark other contacts
        if self.is_primary:
            Contact.objects.filter(
                client=self.client
            ).exclude(
                pk=self.pk
            ).update(
                is_primary=False
            )
        super().save(*args, **kwargs)


class Interaction(TimeStampedModel):
    """Client interaction and communication history."""

    INTERACTION_TYPE_CHOICES = [
        ('call', _('مكالمة')),
        ('email', _('بريد إلكتروني')),
        ('meeting', _('اجتماع')),
        ('whatsapp', _('واتساب')),
        ('visit', _('زيارة')),
        ('other', _('آخر')),
    ]

    PRIORITY_CHOICES = [
        ('low', _('منخفضة')),
        ('medium', _('متوسطة')),
        ('high', _('عالية')),
        ('urgent', _('عاجلة')),
    ]

    STATUS_CHOICES = [
        ('open', _('مفتوحة')),
        ('in_progress', _('قيد التنفيذ')),
        ('pending', _('معلقة')),
        ('resolved', _('تم الحل')),
        ('closed', _('مغلقة')),
    ]

    # Basic Information
    client = models.ForeignKey(
        Client,
        on_delete=models.CASCADE,
        related_name='interactions',
        verbose_name=_('العميل')
    )
    contact = models.ForeignKey(
        Contact,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='interactions',
        verbose_name=_('جهة الاتصال')
    )
    type = models.CharField(
        _('نوع التفاعل'),
        max_length=20,
        choices=INTERACTION_TYPE_CHOICES
    )
    subject = models.CharField(_('الموضوع'), max_length=200)
    description = models.TextField(_('الوصف'))

    # Metadata
    priority = models.CharField(
        _('الأولوية'),
        max_length=10,
        choices=PRIORITY_CHOICES,
        default='medium'
    )
    status = models.CharField(
        _('الحالة'),
        max_length=20,
        choices=STATUS_CHOICES,
        default='open'
    )
    date = models.DateTimeField(_('تاريخ التفاعل'))
    duration = models.PositiveIntegerField(
        _('المدة (دقائق)'),
        null=True,
        blank=True
    )
    location = models.CharField(_('الموقع'), max_length=200, blank=True)

    # Assignment
    created_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_interactions',
        verbose_name=_('أنشئ بواسطة')
    )
    assigned_to = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_interactions',
        verbose_name=_('مكلف إلى')
    )

    # Follow-up
    requires_followup = models.BooleanField(_('يتطلب متابعة'), default=False)
    followup_date = models.DateTimeField(
        _('تاريخ المتابعة'),
        null=True,
        blank=True
    )
    followup_notes = models.TextField(_('ملاحظات المتابعة'), blank=True)
    completed_at = models.DateTimeField(
        _('تاريخ الإكمال'),
        null=True,
        blank=True
    )

    class Meta:
        verbose_name = _('تفاعل')
        verbose_name_plural = _('التفاعلات')
        ordering = ['-date']
        indexes = [
            models.Index(fields=['client', '-date']),
            models.Index(fields=['type']),
            models.Index(fields=['status']),
            models.Index(fields=['priority']),
            models.Index(fields=['requires_followup']),
        ]

    def __str__(self):
        return (
            f"{self.get_type_display()} - "
            f"{self.client.full_name_ar} - "
            f"{self.date:%Y-%m-%d}"
        )
