{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}تفاصيل الفاتورة {{ invoice.invoice_number }}{% endblock %}

{% block extra_css %}
<style>
.invoice-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
}

.invoice-details {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.status-badge {
    padding: 8px 16px;
    border-radius: 25px;
    font-weight: 500;
    font-size: 0.9rem;
}

.status-draft { background: #f8f9fa; color: #6c757d; }
.status-sent { background: #fff3cd; color: #856404; }
.status-paid { background: #d1edff; color: #0c63e4; }
.status-overdue { background: #f8d7da; color: #721c24; }

.invoice-items {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.payment-history {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.btn-action {
    padding: 10px 20px;
    border-radius: 25px;
    border: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-send {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    color: white;
}

.btn-pay {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-download {
    background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
    color: white;
}

.amount-summary {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Invoice Header -->
    <div class="invoice-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="h2 mb-2">{{ invoice.invoice_number }}</h1>
                <p class="mb-0 opacity-75">
                    <i class="fas fa-user me-2"></i>{{ invoice.client.full_name_ar }}
                    {% if invoice.reservation %}
                    <span class="ms-3">
                        <i class="fas fa-bookmark me-2"></i>{{ invoice.reservation.reservation_number }}
                    </span>
                    {% endif %}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <span class="status-badge status-{{ invoice.status }}">
                    {{ invoice.get_status_display }}
                </span>
                {% if invoice.is_overdue %}
                <br><small class="text-warning mt-2 d-block">
                    <i class="fas fa-exclamation-triangle"></i> متأخرة {{ invoice.due_date|timesince }}
                </small>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Invoice Details -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="invoice-details">
                <h5 class="mb-4"><i class="fas fa-info-circle me-2"></i>معلومات الفاتورة</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">تاريخ الإصدار</label>
                            <p class="mb-0">{{ invoice.issue_date }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">العميل</label>
                            <p class="mb-0">{{ invoice.client.full_name_ar }}</p>
                            <small class="text-muted">{{ invoice.client.email }}</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">تاريخ الاستحقاق</label>
                            <p class="mb-0 {% if invoice.is_overdue %}text-danger{% endif %}">
                                {{ invoice.due_date }}
                            </p>
                        </div>
                        {% if invoice.reservation %}
                        <div class="mb-3">
                            <label class="form-label text-muted">الحجز المرتبط</label>
                            <p class="mb-0">{{ invoice.reservation.reservation_number }}</p>
                            <small class="text-muted">{{ invoice.reservation.package.title_ar }}</small>
                        </div>
                        {% endif %}
                    </div>
                </div>

                {% if invoice.notes %}
                <div class="mt-3">
                    <label class="form-label text-muted">ملاحظات</label>
                    <p class="mb-0">{{ invoice.notes }}</p>
                </div>
                {% endif %}
            </div>

            <!-- Invoice Items -->
            <div class="invoice-items">
                <h5 class="mb-4"><i class="fas fa-list me-2"></i>عناصر الفاتورة</h5>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>الوصف</th>
                                <th class="text-center">الكمية</th>
                                <th class="text-end">سعر الوحدة</th>
                                <th class="text-end">الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in items %}
                            <tr>
                                <td>{{ item.description }}</td>
                                <td class="text-center">{{ item.quantity }}</td>
                                <td class="text-end">{{ item.unit_price }} درهم</td>
                                <td class="text-end">{{ item.total_price }} درهم</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="4" class="text-center text-muted py-4">
                                    <i class="fas fa-inbox fa-2x mb-2"></i>
                                    <p>لا توجد عناصر في هذه الفاتورة</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Amount Summary -->
                <div class="amount-summary">
                    <div class="row">
                        <div class="col-md-6 offset-md-6">
                            <div class="d-flex justify-content-between mb-2">
                                <span>المجموع الفرعي:</span>
                                <span>{{ invoice.subtotal }} درهم</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>الضريبة ({{ invoice.tax_rate }}%):</span>
                                <span>{{ invoice.tax_amount }} درهم</span>
                            </div>
                            {% if invoice.discount_amount %}
                            <div class="d-flex justify-content-between mb-2 text-success">
                                <span>الخصم:</span>
                                <span>-{{ invoice.discount_amount }} درهم</span>
                            </div>
                            {% endif %}
                            <hr>
                            <div class="d-flex justify-content-between">
                                <strong>المبلغ الإجمالي:</strong>
                                <strong class="text-primary">{{ invoice.total_amount }} درهم</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Actions -->
            <div class="invoice-details">
                <h5 class="mb-4"><i class="fas fa-cogs me-2"></i>الإجراءات</h5>

                {% if invoice.status == 'draft' %}
                <button class="btn btn-send w-100 mb-2">
                    <i class="fas fa-paper-plane me-2"></i>إرسال الفاتورة
                </button>
                {% elif invoice.status == 'sent' %}
                <button class="btn btn-pay w-100 mb-2">
                    <i class="fas fa-check me-2"></i>تأكيد الدفع
                </button>
                {% endif %}

                <button class="btn btn-download w-100 mb-2">
                    <i class="fas fa-download me-2"></i>تحميل PDF
                </button>

                <a href="{% url 'finance:invoice_list' %}" class="btn btn-outline-secondary w-100">
                    <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
                </a>
            </div>

            <!-- Payment History -->
            <div class="payment-history">
                <h5 class="mb-4"><i class="fas fa-credit-card me-2"></i>سجل المدفوعات</h5>

                {% if payments %}
                <div class="list-group list-group-flush">
                    {% for payment in payments %}
                    <div class="list-group-item d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">{{ payment.payment_number }}</h6>
                            <p class="mb-1">{{ payment.amount }} درهم</p>
                            <small class="text-muted">{{ payment.payment_date }} - {{ payment.get_payment_method_display }}</small>
                        </div>
                        <span class="badge bg-{{ payment.status|yesno:'success,warning,danger' }}">
                            {{ payment.get_status_display }}
                        </span>
                    </div>
                    {% endfor %}
                </div>

                <div class="mt-3 p-3 bg-light rounded">
                    <div class="d-flex justify-content-between">
                        <span>إجمالي المدفوع:</span>
                        <strong>{{ invoice.paid_amount }} درهم</strong>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>المبلغ المتبقي:</span>
                        <strong class="text-danger">{{ invoice.remaining_amount }} درهم</strong>
                    </div>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-credit-card fa-2x mb-2"></i>
                    <p>لا توجد مدفوعات بعد</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Send Invoice
document.querySelector('.btn-send')?.addEventListener('click', function() {
    if (confirm('هل أنت متأكد من إرسال هذه الفاتورة؟')) {
        // AJAX call to send invoice
        fetch(`/api/finance/invoices/{{ invoice.id }}/send_invoice/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.message) {
                alert(data.message);
                location.reload();
            }
        });
    }
});

// Mark as Paid
document.querySelector('.btn-pay')?.addEventListener('click', function() {
    if (confirm('هل أنت متأكد من تأكيد دفع هذه الفاتورة؟')) {
        // AJAX call to mark as paid
        fetch(`/api/finance/invoices/{{ invoice.id }}/mark_paid/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.message) {
                alert(data.message);
                location.reload();
            }
        });
    }
});
</script>
{% endblock %}
