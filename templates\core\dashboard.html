{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "لوحة التحكم" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>
            {% trans "لوحة التحكم الرئيسية" %}
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-primary border-4 h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-primary text-uppercase mb-1">
                            {% trans "إجمالي العملاء" %}
                        </div>
                        <div class="h5 mb-0 fw-bold text-gray-800">{{ total_clients|default:0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-success border-4 h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-success text-uppercase mb-1">
                            {% trans "الحجوزات النشطة" %}
                        </div>
                        <div class="h5 mb-0 fw-bold text-gray-800">{{ active_reservations|default:0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-warning border-4 h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-warning text-uppercase mb-1">
                            {% trans "الفواتير المعلقة" %}
                        </div>
                        <div class="h5 mb-0 fw-bold text-gray-800">{{ pending_invoices|default:0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-file-invoice-dollar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-info border-4 h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-info text-uppercase mb-1">
                            {% trans "المبيعات هذا الشهر" %}
                        </div>
                        <div class="h5 mb-0 fw-bold text-gray-800">{{ monthly_sales|default:"0 MAD" }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <!-- Recent Clients -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="m-0 fw-bold text-primary">
                    <i class="fas fa-users me-2"></i>
                    {% trans "العملاء الجدد" %}
                </h6>
                <a href="{% url 'crm:client_list' %}" class="btn btn-sm btn-outline-primary">
                    {% trans "عرض الكل" %}
                </a>
            </div>
            <div class="card-body">
                {% if recent_clients %}
                    <div class="list-group list-group-flush">
                        {% for client in recent_clients %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ client.full_name_ar }}</h6>
                                <small class="text-muted">{{ client.email }}</small>
                            </div>
                            <small class="text-muted">{{ client.created_at|date:"d/m/Y" }}</small>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted text-center">{% trans "لا توجد عملاء جدد" %}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Reservations -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="m-0 fw-bold text-primary">
                    <i class="fas fa-calendar-check me-2"></i>
                    {% trans "الحجوزات الأخيرة" %}
                </h6>
                <a href="{% url 'reservations:reservation_list' %}" class="btn btn-sm btn-outline-primary">
                    {% trans "عرض الكل" %}
                </a>
            </div>
            <div class="card-body">
                {% if recent_reservations %}
                    <div class="list-group list-group-flush">
                        {% for reservation in recent_reservations %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ reservation.package.title_ar }}</h6>
                                <small class="text-muted">{{ reservation.client.full_name_ar }}</small>
                            </div>
                            <span class="badge bg-{{ reservation.status|default:'secondary' }} rounded-pill">
                                {{ reservation.get_status_display }}
                            </span>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted text-center">{% trans "لا توجد حجوزات حديثة" %}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 fw-bold text-primary">
                    <i class="fas fa-bolt me-2"></i>
                    {% trans "إجراءات سريعة" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'crm:client_add' %}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-user-plus fa-2x d-block mb-2"></i>
                            {% trans "إضافة عميل جديد" %}
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'reservations:reservation_add' %}" class="btn btn-outline-success w-100">
                            <i class="fas fa-calendar-plus fa-2x d-block mb-2"></i>
                            {% trans "حجز جديد" %}
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'tours:package_add' %}" class="btn btn-outline-info w-100">
                            <i class="fas fa-map-marked-alt fa-2x d-block mb-2"></i>
                            {% trans "باقة جديدة" %}
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'reports:sales_report' %}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-chart-bar fa-2x d-block mb-2"></i>
                            {% trans "تقرير المبيعات" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
