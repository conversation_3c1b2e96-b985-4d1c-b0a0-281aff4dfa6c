# 🌍 نظام إدارة وكالة السفر المغربية - Moroccan Travel Agency ERP

## 📋 نظرة عامة

نظام إدارة شامل لوكالات السفر المغربية مطور باستخدام Django مع دعم كامل للغة العربية والفرنسية والإنجليزية. يوفر النظام جميع الأدوات اللازمة لإدارة العملاء، الباقات السياحية، الحجوزات، والعمليات المالية.

## ✨ الميزات الرئيسية

### 🎯 الوحدات المتاحة حالياً
- **👥 إدارة العملاء (CRM)**: قاعدة بيانات شاملة للعملاء مع تتبع التفاعلات
- **🏖️ الباقات السياحية**: إدارة الوجهات والباقات مع برامج يومية مفصلة
- **⚙️ النظام الأساسي**: إدارة المستخدمين والصلاحيات
- **📊 لوحة التحكم**: إحصائيات فورية وتقارير تفاعلية

### 🚧 الوحدات قيد التطوير
- **📅 نظام الحجوزات**: إدارة الحجوزات والتوفر
- **💰 النظام المالي**: إدارة الفواتير والمدفوعات
- **🏢 إدارة الموردين**: إدارة الفنادق وشركات النقل
- **👤 الموارد البشرية**: إدارة الموظفين والرواتب
- **📈 التقارير المتقدمة**: تحليلات شاملة ولوحات تحكم

### 🗺️ إدارة الباقات السياحية
- إنشاء وإدارة الباقات السياحية
- إدارة الوجهات والبرامج اليومية
- نظام التسعير الديناميكي
- إدارة التوفر والمواسم

### 📅 نظام الحجوزات
- حجوزات متقدمة مع إدارة المشاركين
- إدارة الخدمات الإضافية
- تتبع حالة الحجز والدفعات
- إدارة الوثائق والمستندات

### 💰 الإدارة المالية
- إصدار الفواتير والمدفوعات
- إدارة المصروفات والموردين
- تقارير مالية شاملة
- متوافق مع النظام الضريبي المغربي

### 🤝 إدارة الموردين
- قاعدة بيانات شاملة للموردين
- إدارة العقود والتقييمات
- متابعة الأداء والجودة

### 👥 الموارد البشرية
- إدارة الموظفين والأقسام
- نظام الحضور والانصراف
- إدارة الإجازات والرواتب
- جدولة العمل والمهام

### 📊 التقارير والتحليلات
- تقارير مبيعات تفصيلية
- تحليلات العملاء والأداء
- تقارير مالية وضريبية
- إمكانية التصدير بصيغ متعددة

## المتطلبات التقنية

### البرمجيات المطلوبة
- Python 3.11+
- PostgreSQL 15+
- Redis 7+
- Node.js (للتطوير)

### المكتبات الرئيسية
- Django 4.2
- Django REST Framework
- Bootstrap 5
- PostgreSQL
- Redis
- Celery

## التثبيت والإعداد

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd moroccan-travel-agency-erp
```

### 2. إنشاء البيئة الافتراضية
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate  # Windows
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات في PostgreSQL
createdb moroccan_travel_erp

# تطبيق الهجرات
python manage.py makemigrations
python manage.py migrate
```

### 5. إنشاء المستخدم الإداري
```bash
python manage.py createsuperuser
```

### 6. تجميع الملفات الثابتة
```bash
python manage.py collectstatic
```

### 7. تشغيل الخادم
```bash
python manage.py runserver
```

## التشغيل باستخدام Docker

### 1. بناء وتشغيل الحاويات
```bash
docker-compose up -d
```

### 2. تطبيق الهجرات
```bash
docker-compose exec web python manage.py migrate
```

### 3. إنشاء المستخدم الإداري
```bash
docker-compose exec web python manage.py createsuperuser
```

## إعداد البيئة

انسخ ملف `.env.example` إلى `.env` وقم بتعديل الإعدادات:

```bash
cp .env.example .env
```

### المتغيرات المهمة:
- `SECRET_KEY`: مفتاح Django السري
- `DEBUG`: وضع التطوير (True/False)
- `DB_NAME`, `DB_USER`, `DB_PASSWORD`: إعدادات قاعدة البيانات
- `EMAIL_HOST`, `EMAIL_HOST_USER`: إعدادات البريد الإلكتروني

## الاستخدام

### الوصول للنظام
- الواجهة الرئيسية: `http://localhost:8000`
- لوحة الإدارة: `http://localhost:8000/admin`
- واجهة API: `http://localhost:8000/api/docs`

### المستخدمون الافتراضيون
بعد تشغيل النظام لأول مرة، يمكنك إنشاء المستخدمين التاليين:
- **مدير النظام**: صلاحيات كاملة
- **مدير الوكالة**: إدارة العمليات اليومية
- **موظف المبيعات**: إدارة العملاء والحجوزات
- **محاسب**: إدارة الشؤون المالية

## الميزات المتقدمة

### العمل دون اتصال
- تخزين مؤقت للبيانات المهمة
- مزامنة تلقائية عند الاتصال
- إشعارات للعمليات المعلقة

### الأمان
- مصادقة ثنائية العوامل
- تشفير البيانات الحساسة
- تسجيل العمليات والأنشطة
- صلاحيات مفصلة حسب الدور

### التكامل
- API شامل للتكامل مع أنظمة خارجية
- دعم Webhooks للإشعارات
- تصدير البيانات بصيغ متعددة

## الدعم والصيانة

### النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
python manage.py dbbackup

# نسخ احتياطي للملفات
python manage.py mediabackup
```

### التحديثات
```bash
# تحديث المتطلبات
pip install -r requirements.txt --upgrade

# تطبيق الهجرات الجديدة
python manage.py migrate
```

### المراقبة
- مراقبة الأداء عبر Django Debug Toolbar
- تسجيل الأخطاء في ملفات Log
- إحصائيات الاستخدام في لوحة التحكم

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push للفرع
5. إنشاء Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للتفاصيل.

## الدعم الفني

للحصول على الدعم الفني:
- إنشاء Issue في GitHub
- مراجعة الوثائق في مجلد `docs/`
- التواصل مع فريق التطوير

## خارطة الطريق

### الإصدار القادم (v2.0)
- [ ] تطبيق جوال للعملاء
- [ ] تكامل مع أنظمة الدفع الإلكتروني
- [ ] ذكاء اصطناعي لتوصيات الباقات
- [ ] تحليلات متقدمة ولوحات تحكم تفاعلية

### المميزات المستقبلية
- [ ] دعم العملات المتعددة
- [ ] تكامل مع منصات التواصل الاجتماعي
- [ ] نظام إدارة المحتوى
- [ ] تطبيق سطح المكتب

---

**تم تطوير هذا النظام خصيصاً لوكالات السفر المغربية مع مراعاة المتطلبات المحلية والقوانين المعمول بها.**
