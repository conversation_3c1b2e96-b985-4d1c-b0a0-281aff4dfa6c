"""
Admin configuration for CRM models.
"""
from django.contrib import admin
from .models import Client


@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    list_display = (
        'client_code', 'full_name_ar', 'email', 'phone', 'client_type',
        'vip_status', 'loyalty_points', 'created_at'
    )
    list_filter = (
        'client_type', 'vip_status', 'nationality', 'preferred_language',
        'marketing_consent', 'created_at'
    )
    search_fields = (
        'client_code', 'first_name_ar', 'last_name_ar', 'first_name_fr', 'last_name_fr',
        'email', 'phone', 'passport_number', 'national_id'
    )
    readonly_fields = ('client_code', 'created_at', 'updated_at')

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('client_code', 'client_type')
        }),
        ('الاسم', {
            'fields': (
                ('first_name_ar', 'last_name_ar'),
                ('first_name_fr', 'last_name_fr')
            )
        }),
        ('معلومات الاتصال', {
            'fields': (
                ('email', 'phone'),
                ('whatsapp', 'secondary_phone')
            )
        }),
        ('معلومات شخصية', {
            'fields': (
                ('gender', 'date_of_birth', 'nationality'),
                ('passport_number', 'passport_expiry'),
                'national_id'
            )
        }),
        ('العنوان', {
            'fields': ('address', 'postal_code')
        }),
        ('معلومات الشركة', {
            'fields': ('company_name', 'tax_number'),
            'classes': ('collapse',)
        }),
        ('التفضيلات', {
            'fields': (
                'preferred_language', 'special_requirements', 'dietary_restrictions'
            )
        }),
        ('برنامج الولاء', {
            'fields': ('loyalty_points', 'vip_status')
        }),
        ('التسويق', {
            'fields': ('marketing_consent', 'newsletter_subscription')
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

# Simplified admin - complex models removed for basic setup
