"""
Management command to load initial data for the Moroccan Travel Agency ERP.
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from apps.core.models import Country, City, SystemSettings
from apps.tours.models import Destination, TourCategory
from apps.accounts.models import User


class Command(BaseCommand):
    help = 'Load initial data for the Moroccan Travel Agency ERP'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('بدء تحميل البيانات الأولية...'))

        with transaction.atomic():
            # Load countries
            self.load_countries()

            # Load cities
            self.load_cities()

            # Load tour categories
            self.load_tour_categories()

            # Load destinations
            self.load_destinations()

            # Load system settings
            self.load_system_settings()

            # Create sample users
            self.create_sample_users()

        self.stdout.write(self.style.SUCCESS('تم تحميل البيانات الأولية بنجاح!'))

    def load_countries(self):
        """Load initial countries data."""
        countries_data = [
            {'name_ar': 'المغرب', 'name_fr': 'Maroc', 'name_en': 'Morocco', 'code': 'MA', 'phone_code': '+212'},
            {'name_ar': 'فرنسا', 'name_fr': 'France', 'name_en': 'France', 'code': 'FR', 'phone_code': '+33'},
            {'name_ar': 'إسبانيا', 'name_fr': 'Espagne', 'name_en': 'Spain', 'code': 'ES', 'phone_code': '+34'},
            {'name_ar': 'الجزائر', 'name_fr': 'Algérie', 'name_en': 'Algeria', 'code': 'DZ', 'phone_code': '+213'},
            {'name_ar': 'تونس', 'name_fr': 'Tunisie', 'name_en': 'Tunisia', 'code': 'TN', 'phone_code': '+216'},
            {'name_ar': 'مصر', 'name_fr': 'Égypte', 'name_en': 'Egypt', 'code': 'EG', 'phone_code': '+20'},
            {'name_ar': 'الإمارات العربية المتحدة', 'name_fr': 'Émirats arabes unis', 'name_en': 'UAE', 'code': 'AE', 'phone_code': '+971'},
            {'name_ar': 'السعودية', 'name_fr': 'Arabie saoudite', 'name_en': 'Saudi Arabia', 'code': 'SA', 'phone_code': '+966'},
        ]

        for country_data in countries_data:
            country, created = Country.objects.get_or_create(
                code=country_data['code'],
                defaults=country_data
            )
            if created:
                self.stdout.write(f'تم إنشاء دولة: {country.name_ar}')

    def load_cities(self):
        """Load initial cities data."""
        morocco = Country.objects.get(code='MA')

        cities_data = [
            {'name_ar': 'الرباط', 'name_fr': 'Rabat', 'name_en': 'Rabat'},
            {'name_ar': 'الدار البيضاء', 'name_fr': 'Casablanca', 'name_en': 'Casablanca'},
            {'name_ar': 'فاس', 'name_fr': 'Fès', 'name_en': 'Fez'},
            {'name_ar': 'مراكش', 'name_fr': 'Marrakech', 'name_en': 'Marrakech'},
            {'name_ar': 'أكادير', 'name_fr': 'Agadir', 'name_en': 'Agadir'},
            {'name_ar': 'طنجة', 'name_fr': 'Tanger', 'name_en': 'Tangier'},
            {'name_ar': 'مكناس', 'name_fr': 'Meknès', 'name_en': 'Meknes'},
            {'name_ar': 'ورزازات', 'name_fr': 'Ouarzazate', 'name_en': 'Ouarzazate'},
            {'name_ar': 'الصويرة', 'name_fr': 'Essaouira', 'name_en': 'Essaouira'},
            {'name_ar': 'شفشاون', 'name_fr': 'Chefchaouen', 'name_en': 'Chefchaouen'},
        ]

        for city_data in cities_data:
            city_data['country'] = morocco
            city, created = City.objects.get_or_create(
                name_ar=city_data['name_ar'],
                country=morocco,
                defaults=city_data
            )
            if created:
                self.stdout.write(f'تم إنشاء مدينة: {city.name_ar}')

    def load_tour_categories(self):
        """Load initial tour categories."""
        categories_data = [
            {'name_ar': 'سياحة ثقافية', 'name_fr': 'Tourisme culturel', 'name_en': 'Cultural Tourism'},
            {'name_ar': 'سياحة المغامرات', 'name_fr': 'Tourisme d\'aventure', 'name_en': 'Adventure Tourism'},
            {'name_ar': 'سياحة الاستجمام', 'name_fr': 'Tourisme de détente', 'name_en': 'Leisure Tourism'},
            {'name_ar': 'سياحة الصحراء', 'name_fr': 'Tourisme du désert', 'name_en': 'Desert Tourism'},
            {'name_ar': 'سياحة الشواطئ', 'name_fr': 'Tourisme balnéaire', 'name_en': 'Beach Tourism'},
            {'name_ar': 'سياحة الجبال', 'name_fr': 'Tourisme de montagne', 'name_en': 'Mountain Tourism'},
            {'name_ar': 'سياحة دينية', 'name_fr': 'Tourisme religieux', 'name_en': 'Religious Tourism'},
            {'name_ar': 'سياحة الأعمال', 'name_fr': 'Tourisme d\'affaires', 'name_en': 'Business Tourism'},
        ]

        for category_data in categories_data:
            category, created = TourCategory.objects.get_or_create(
                name_ar=category_data['name_ar'],
                defaults=category_data
            )
            if created:
                self.stdout.write(f'تم إنشاء فئة: {category.name_ar}')

    def load_destinations(self):
        """Load initial destinations."""
        morocco = Country.objects.get(code='MA')

        destinations_data = [
            {
                'name_ar': 'مراكش الحمراء',
                'name_fr': 'Marrakech la Rouge',
                'name_en': 'Marrakech the Red',
                'description_ar': 'المدينة الحمراء الساحرة بأسواقها التقليدية وقصورها التاريخية',
                'description_fr': 'La ville rouge enchanteresse avec ses souks traditionnels et ses palais historiques',
                'description_en': 'The enchanting red city with its traditional souks and historic palaces',
                'country': morocco,
                'is_featured': True
            },
            {
                'name_ar': 'صحراء مرزوقة',
                'name_fr': 'Désert de Merzouga',
                'name_en': 'Merzouga Desert',
                'description_ar': 'تجربة فريدة في قلب الصحراء الكبرى مع الكثبان الذهبية',
                'description_fr': 'Une expérience unique au cœur du grand désert avec les dunes dorées',
                'description_en': 'A unique experience in the heart of the great desert with golden dunes',
                'country': morocco,
                'is_featured': True
            },
            {
                'name_ar': 'فاس العتيقة',
                'name_fr': 'Fès l\'ancienne',
                'name_en': 'Ancient Fez',
                'description_ar': 'المدينة العتيقة بتراثها الإسلامي العريق وجامعة القرويين',
                'description_fr': 'La ville ancienne avec son riche patrimoine islamique et l\'université Al Quaraouiyine',
                'description_en': 'The ancient city with its rich Islamic heritage and Al Quaraouiyine University',
                'country': morocco,
                'is_featured': True
            },
            {
                'name_ar': 'شفشاون الزرقاء',
                'name_fr': 'Chefchaouen la Bleue',
                'name_en': 'Blue Chefchaouen',
                'description_ar': 'المدينة الزرقاء الساحرة في أحضان جبال الريف',
                'description_fr': 'La ville bleue enchanteresse dans les montagnes du Rif',
                'description_en': 'The enchanting blue city in the Rif Mountains',
                'country': morocco,
                'is_featured': False
            },
        ]

        for dest_data in destinations_data:
            destination, created = Destination.objects.get_or_create(
                name_ar=dest_data['name_ar'],
                defaults=dest_data
            )
            if created:
                self.stdout.write(f'تم إنشاء وجهة: {destination.name_ar}')

    def load_system_settings(self):
        """Load initial system settings."""
        settings_data = [
            {'key': 'company_name_ar', 'value': 'وكالة السفر المغربية', 'description': 'اسم الشركة بالعربية'},
            {'key': 'company_name_fr', 'value': 'Agence de Voyage Marocaine', 'description': 'اسم الشركة بالفرنسية'},
            {'key': 'company_name_en', 'value': 'Moroccan Travel Agency', 'description': 'اسم الشركة بالإنجليزية'},
            {'key': 'default_currency', 'value': 'MAD', 'description': 'العملة الافتراضية'},
            {'key': 'tax_rate', 'value': '20', 'description': 'معدل الضريبة (%)'},
            {'key': 'booking_confirmation_days', 'value': '3', 'description': 'أيام تأكيد الحجز'},
            {'key': 'cancellation_policy_days', 'value': '7', 'description': 'سياسة الإلغاء (أيام)'},
        ]

        for setting_data in settings_data:
            setting, created = SystemSettings.objects.get_or_create(
                key=setting_data['key'],
                defaults=setting_data
            )
            if created:
                self.stdout.write(f'تم إنشاء إعداد: {setting.key}')

    def create_sample_users(self):
        """Create sample users with different roles."""
        users_data = [
            {
                'username': 'manager',
                'email': '<EMAIL>',
                'first_name_ar': 'أحمد',
                'last_name_ar': 'المدير',
                'role': 'manager',
                'employee_id': 'MGR001',
                'is_staff': True
            },
            {
                'username': 'sales1',
                'email': '<EMAIL>',
                'first_name_ar': 'فاطمة',
                'last_name_ar': 'المبيعات',
                'role': 'sales',
                'employee_id': 'SAL001',
                'is_staff': True
            },
            {
                'username': 'accountant',
                'email': '<EMAIL>',
                'first_name_ar': 'محمد',
                'last_name_ar': 'المحاسب',
                'role': 'accountant',
                'employee_id': 'ACC001',
                'is_staff': True
            },
        ]

        for user_data in users_data:
            if not User.objects.filter(username=user_data['username']).exists():
                user = User.objects.create_user(
                    username=user_data['username'],
                    email=user_data['email'],
                    password='password123',  # Default password
                    first_name_ar=user_data['first_name_ar'],
                    last_name_ar=user_data['last_name_ar'],
                    role=user_data['role'],
                    employee_id=user_data['employee_id'],
                    is_staff=user_data['is_staff']
                )
                self.stdout.write(f'تم إنشاء مستخدم: {user.username}')
