{% load i18n %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% trans "Password Reset" %}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 30px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #007bff;
            color: #ffffff;
            text-decoration: none;
            border-radius: 4px;
            margin: 20px 0;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            color: #666666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{% trans "Password Reset" %}</h1>
        </div>
        <p>{% blocktrans with name=user.get_full_name %}Hello {{ name }},{% endblocktrans %}</p>
        <p>{% trans "We received a request to reset your password on" %} {{ site_name }}.</p>
        <p>{% trans "To reset your password, please click the button below:" %}</p>
        <p style="text-align: center;">
            <a href="{{ reset_url }}" class="btn">{% trans "Reset Password" %}</a>
        </p>
        <p>{% trans "If the button above doesn't work, you can copy and paste this link into your browser:" %}</p>
        <p style="word-break: break-all;"><small>{{ reset_url }}</small></p>
        <p><strong>{% trans "If you did not request a password reset, you can safely ignore this email." %}</strong></p>
        <div class="footer">
            <p>
                {% trans "Best regards," %}<br>
                {% trans "The" %} {{ site_name }} {% trans "Team" %}
            </p>
        </div>
    </div>
</body>
</html>
