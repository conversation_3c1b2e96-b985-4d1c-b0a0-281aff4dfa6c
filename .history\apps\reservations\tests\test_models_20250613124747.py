from django.test import TestCase
from django.utils import timezone
from django.core.exceptions import ValidationError
from apps.reservations.models import (
    Reservation, ReservationParticipant, ReservationService, ReservationDocument
)
from apps.accounts.models import User
from apps.crm.models import Client
from apps.tours.models import TourPackage
from djmoney.money import Money


class TestReservationModel(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test user
        cls.user = User.objects.create(username="testuser")

        # Create test client
        cls.client = Client.objects.create(
            client_code="CL123",
            first_name_ar="Test",
            last_name_ar="Client",
            phone="*********",
            email="<EMAIL>"
        )

        # Create test package
        cls.package = TourPackage.objects.create(
            title_ar="باقة اختبار",
            title_fr="Package Test",
            title_en="Test Package",
            description_ar="وصف الباقة",
            description_fr="Description du package",
            description_en="Package Description",
            duration_days=5,
            min_participants=2,
            max_participants=10
        )

    def test_create_reservation(self):
        # Test basic reservation creation
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD'),
            status='pending'
        )
        self.assertEqual(reservation.status, 'pending')
        self.assertEqual(reservation.adults, 2)
        self.assertIsNotNone(reservation.reservation_number)
        self.assertTrue(reservation.reservation_number.startswith('RES'))

    def test_reservation_total_participants(self):
        # Test participant count calculation
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            children=1,
            infants=1,
            adult_price=Money(1000, 'MAD'),
            child_price=Money(500, 'MAD'),
            infant_price=Money(100, 'MAD')
        )
        self.assertEqual(reservation.total_participants, 4)

    def test_reservation_str(self):
        # Test string representation
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adult_price=Money(1000, 'MAD')
        )
        expected_str = f"{reservation.reservation_number} - {self.client.full_name_ar}"
        self.assertEqual(str(reservation), expected_str)

    def test_reservation_amounts(self):
        # Test price calculations
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            children=1,
            infants=1,
            adult_price=Money(1000, 'MAD'),
            child_price=Money(500, 'MAD'),
            infant_price=Money(100, 'MAD'),
            discount_amount=Money(200, 'MAD'),
            tax_amount=Money(50, 'MAD')
        )
        # Verify subtotal calculation: (2 * 1000) + (1 * 500) + (1 * 100) = 2600
        self.assertEqual(float(reservation.subtotal.amount), 2600.0)
        # Verify total amount: 2600 - 200 + 50 = 2450
        self.assertEqual(float(reservation.total_amount.amount), 2450.0)

    def test_reservation_date_validation(self):
        # Test invalid dates
        with self.assertRaises(ValidationError):
            reservation = Reservation.objects.create(
                client=self.client,
                package=self.package,
                departure_date=timezone.now().date(),
                return_date=timezone.now().date() - timezone.timedelta(days=1),
                adult_price=Money(1000, 'MAD')
            )
            reservation.full_clean()

    def test_reservation_participants_validation(self):
        # Test invalid participant numbers
        with self.assertRaises(ValidationError):
            reservation = Reservation(
                client=self.client,
                package=self.package,
                departure_date=timezone.now().date(),
                return_date=timezone.now().date() + timezone.timedelta(days=5),
                adults=0,  # Invalid: must have at least one adult
                adult_price=Money(1000, 'MAD')
            )
            reservation.full_clean()

    def test_reservation_update(self):
        # Test reservation update operations
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD'),
            status='pending'
        )

        # Update status
        reservation.status = 'confirmed'
        reservation.save()
        self.assertEqual(Reservation.objects.get(id=reservation.id).status, 'confirmed')

        # Update prices
        reservation.adult_price = Money(1200, 'MAD')
        reservation.save()
        self.assertEqual(float(Reservation.objects.get(id=reservation.id).adult_price.amount), 1200.0)

    def test_add_reservation_participant(self):
        # Test adding participants to reservation
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD')
        )

        participant = ReservationParticipant.objects.create(
            reservation=reservation,
            first_name="John",
            last_name="Doe",
            date_of_birth=timezone.now().date() - timezone.timedelta(days=365*30),
            participant_type='adult',
            passport_number="A123456"
        )
        self.assertEqual(reservation.participants.count(), 1)
        self.assertEqual(participant.reservation, reservation)

    def test_add_reservation_service(self):
        # Test adding services to reservation
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD')
        )

        service = ReservationService.objects.create(
            reservation=reservation,
            service_type='transfer',
            name='Airport Transfer',
            unit_price=Money(200, 'MAD'),
            quantity=2
        )
        self.assertEqual(reservation.services.count(), 1)
        self.assertEqual(float(service.total_price.amount), 400.0)

    def test_add_reservation_document(self):
        # Test adding documents to reservation
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD')
        )

        document = ReservationDocument.objects.create(
            reservation=reservation,
            document_type='passport',
            title='Passport Copy',
            is_public=False
        )
        self.assertEqual(reservation.documents.count(), 1)
        self.assertEqual(document.document_type, 'passport')

    def test_reservation_status_transitions(self):
        # Test valid status transitions
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD'),
            status='pending'
        )

        # Test pending to confirmed
        reservation.status = 'confirmed'
        reservation.save()
        self.assertEqual(reservation.status, 'confirmed')

        # Test confirmed to completed
        reservation.status = 'completed'
        reservation.save()
        self.assertEqual(reservation.status, 'completed')

        # Test confirmed to cancelled
        new_reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD'),
            status='confirmed'
        )
        new_reservation.status = 'cancelled'
        new_reservation.save()
        self.assertEqual(new_reservation.status, 'cancelled')

    def test_multilingual_fields(self):
        # Test notes in different languages
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD'),
            notes_ar="ملاحظات",
            notes_fr="Notes en français",
            notes_en="Notes in English"
        )
        self.assertEqual(reservation.notes_ar, "ملاحظات")
        self.assertEqual(reservation.notes_fr, "Notes en français")
        self.assertEqual(reservation.notes_en, "Notes in English")

    def test_max_participants_validation(self):
        # Test exceeding package max participants
        with self.assertRaises(ValidationError):
            reservation = Reservation(
                client=self.client,
                package=self.package,  # max_participants is 10
                departure_date=timezone.now().date(),
                return_date=timezone.now().date() + timezone.timedelta(days=5),
                adults=9,
                children=2,  # Total 11 participants > max 10
                adult_price=Money(1000, 'MAD')
            )
            reservation.full_clean()    def test_edge_case_pricing(self):
        # Test edge cases with pricing
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=1,
            children=1,
            infants=1,
            adult_price=Money(999.99, 'MAD'),
            child_price=Money(499.99, 'MAD'),
            infant_price=Money(0, 'MAD'),  # Free infant
            discount_amount=Money(99.99, 'MAD'),
            tax_amount=Money(199.99, 'MAD')
        )
        expected_subtotal = 999.99 + 499.99 + 0  # 1499.98
        expected_total = expected_subtotal - 99.99 + 199.99  # 1599.98
        self.assertAlmostEqual(
            float(reservation.subtotal.amount),
            expected_subtotal,
            places=2
        )
        self.assertAlmostEqual(
            float(reservation.total_amount.amount),
            expected_total,
            places=2
        )

    def test_participant_type_validation(self):
        # Test participant type validation
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=1,
            adult_price=Money(1000, 'MAD')
        )

        # Valid adult participant
        adult = ReservationParticipant.objects.create(
            reservation=reservation,
            first_name="John",
            last_name="Doe",
            date_of_birth=timezone.now().date() - timezone.timedelta(days=365*30),
            participant_type='adult'
        )
        self.assertEqual(adult.participant_type, 'adult')

        # Valid child participant
        child = ReservationParticipant.objects.create(
            reservation=reservation,
            first_name="Jane",
            last_name="Doe",
            date_of_birth=timezone.now().date() - timezone.timedelta(days=365*10),
            participant_type='child'
        )
        self.assertEqual(child.participant_type, 'child')

        # Valid infant participant
        infant = ReservationParticipant.objects.create(
            reservation=reservation,
            first_name="Baby",
            last_name="Doe",
            date_of_birth=timezone.now().date() - timezone.timedelta(days=180),
            participant_type='infant'
        )
        self.assertEqual(infant.participant_type, 'infant')

    def test_service_calculations(self):
        # Test complex service calculations
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD')
        )

        # Add multiple services
        service1 = ReservationService.objects.create(
            reservation=reservation,
            service_type='transfer',
            name='Airport Transfer',
            unit_price=Money(200, 'MAD'),
            quantity=2
        )

        service2 = ReservationService.objects.create(
            reservation=reservation,
            service_type='activity',
            name='Desert Tour',
            unit_price=Money(500, 'MAD'),
            quantity=2
        )

        service3 = ReservationService.objects.create(
            reservation=reservation,
            service_type='accommodation',
            name='Extra Night',
            unit_price=Money(800, 'MAD'),
            quantity=1
        )

        # Verify individual service calculations
        self.assertEqual(float(service1.total_price.amount), 400.0)
        self.assertEqual(float(service2.total_price.amount), 1000.0)
        self.assertEqual(float(service3.total_price.amount), 800.0)

        # Verify total services amount
        total_services = sum(float(service.total_price.amount) for service in reservation.services.all())
        self.assertEqual(total_services, 2200.0)
