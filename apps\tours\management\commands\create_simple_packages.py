"""
Management command to create simple tour packages.
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from apps.tours.models import TourPackage, TourCategory, Destination, Itinerary
from decimal import Decimal


class Command(BaseCommand):
    help = 'Create simple tour packages'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('بدء إنشاء باقات سياحية مبسطة...'))

        with transaction.atomic():
            self.create_simple_packages()

        self.stdout.write(self.style.SUCCESS('تم إنشاء الباقات السياحية المبسطة بنجاح!'))

    def create_simple_packages(self):
        """Create simple tour packages."""

        # Get categories and destinations
        cultural_category = TourCategory.objects.get(name_ar='سياحة ثقافية')
        desert_category = TourCategory.objects.get(name_ar='سياحة الصحراء')

        marrakech = Destination.objects.get(name_ar='مراكش الحمراء')
        merzouga = Destination.objects.get(name_ar='صحراء مرزوقة')

        packages_data = [
            {
                'package_code': 'MAR001',
                'title_ar': 'جولة مراكش الكلاسيكية',
                'title_fr': 'Tour Classique de Marrakech',
                'title_en': 'Classic Marrakech Tour',
                'short_description_ar': 'استكشف جمال مراكش الحمراء في رحلة لا تُنسى',
                'short_description_fr': 'Découvrez la beauté de Marrakech la Rouge',
                'short_description_en': 'Explore the beauty of Red Marrakech',
                'detailed_description_ar': 'رحلة شاملة لاستكشاف مراكش الحمراء مع زيارة أهم المعالم التاريخية والثقافية',
                'detailed_description_fr': 'Voyage complet pour explorer Marrakech avec visite des principaux sites',
                'detailed_description_en': 'Complete journey to explore Marrakech with visits to major sites',
                'category': cultural_category,
                'duration_days': 3,
                'duration_nights': 2,
                'max_participants': 20,
                'min_participants': 2,
                'difficulty_level': 'easy',
                'base_price': Decimal('1200.00'),
                'child_price': Decimal('800.00'),
                'infant_price': Decimal('200.00'),
                'inclusions': 'الإقامة في فندق 4 نجوم، وجبة الإفطار، النقل، دليل سياحي',
                'exclusions': 'تذاكر الطيران، الوجبات الرئيسية، المشتريات الشخصية',
                'is_active': True,
                'is_featured': True
            },
            {
                'package_code': 'DES001',
                'title_ar': 'مغامرة الصحراء الكبرى',
                'title_fr': 'Aventure du Grand Désert',
                'title_en': 'Great Desert Adventure',
                'short_description_ar': 'تجربة فريدة في صحراء مرزوقة مع ركوب الجمال',
                'short_description_fr': 'Une expérience unique dans le désert de Merzouga',
                'short_description_en': 'A unique experience in Merzouga desert',
                'detailed_description_ar': 'مغامرة صحراوية شاملة تتضمن رحلة عبر الأطلس الكبير وتجربة الحياة البدوية',
                'detailed_description_fr': 'Aventure complète dans le désert incluant un voyage à travers le Haut Atlas',
                'detailed_description_en': 'Complete desert adventure including journey through High Atlas',
                'category': desert_category,
                'duration_days': 4,
                'duration_nights': 3,
                'max_participants': 15,
                'min_participants': 2,
                'difficulty_level': 'moderate',
                'base_price': Decimal('1800.00'),
                'child_price': Decimal('1200.00'),
                'infant_price': Decimal('300.00'),
                'inclusions': 'النقل بسيارة 4x4، المبيت في المخيم، جميع الوجبات، ركوب الجمال',
                'exclusions': 'تذاكر الطيران، المشروبات الكحولية، الإكراميات',
                'is_active': True,
                'is_featured': True
            }
        ]

        for package_data in packages_data:
            package, created = TourPackage.objects.get_or_create(
                package_code=package_data['package_code'],
                defaults=package_data
            )
            if created:
                self.stdout.write(f'تم إنشاء باقة: {package.title_ar} ({package.package_code})')

                # Add destinations to package
                if package.package_code == 'MAR001':
                    package.destinations.add(marrakech)
                elif package.package_code == 'DES001':
                    package.destinations.add(merzouga)

                # Create simple itinerary
                self.create_simple_itinerary(package)

    def create_simple_itinerary(self, package):
        """Create simple itinerary for a package."""
        if package.package_code == 'MAR001':
            itineraries = [
                {
                    'day_number': 1,
                    'title_ar': 'الوصول إلى مراكش',
                    'title_fr': 'Arrivée à Marrakech',
                    'title_en': 'Arrival in Marrakech',
                    'description_ar': 'الوصول إلى المطار والانتقال إلى الفندق، جولة استطلاعية في ساحة جامع الفنا',
                    'description_fr': 'Arrivée à l\'aéroport et transfert à l\'hôtel, visite de la place Jemaa el-Fna',
                    'description_en': 'Airport arrival and hotel transfer, visit of Jemaa el-Fna square',
                    'accommodation': 'فندق 4 نجوم',
                    'breakfast_included': False,
                    'lunch_included': False,
                    'dinner_included': True
                },
                {
                    'day_number': 2,
                    'title_ar': 'استكشاف المدينة القديمة',
                    'title_fr': 'Exploration de la médina',
                    'title_en': 'Exploring the old city',
                    'description_ar': 'زيارة قصر البديع وحدائق المنارة والأسواق التقليدية',
                    'description_fr': 'Visite du Palais El Badi, des jardins de la Ménara et des souks',
                    'description_en': 'Visit El Badi Palace, Menara gardens and traditional souks',
                    'accommodation': 'فندق 4 نجوم',
                    'breakfast_included': True,
                    'lunch_included': True,
                    'dinner_included': False
                },
                {
                    'day_number': 3,
                    'title_ar': 'المغادرة',
                    'title_fr': 'Départ',
                    'title_en': 'Departure',
                    'description_ar': 'وقت حر للتسوق والانتقال إلى المطار',
                    'description_fr': 'Temps libre pour le shopping et transfert à l\'aéroport',
                    'description_en': 'Free time for shopping and airport transfer',
                    'accommodation': '',
                    'breakfast_included': True,
                    'lunch_included': False,
                    'dinner_included': False
                }
            ]
        elif package.package_code == 'DES001':
            itineraries = [
                {
                    'day_number': 1,
                    'title_ar': 'الانطلاق نحو الصحراء',
                    'title_fr': 'Départ vers le désert',
                    'title_en': 'Departure to the desert',
                    'description_ar': 'الانطلاق من مراكش عبر الأطلس الكبير إلى ورزازات',
                    'description_fr': 'Départ de Marrakech via le Haut Atlas vers Ouarzazate',
                    'description_en': 'Departure from Marrakech via High Atlas to Ouarzazate',
                    'accommodation': 'فندق في ورزازات',
                    'breakfast_included': True,
                    'lunch_included': False,
                    'dinner_included': True
                },
                {
                    'day_number': 2,
                    'title_ar': 'وصول إلى مرزوقة',
                    'title_fr': 'Arrivée à Merzouga',
                    'title_en': 'Arrival in Merzouga',
                    'description_ar': 'الوصول إلى مرزوقة وركوب الجمال إلى المخيم الصحراوي',
                    'description_fr': 'Arrivée à Merzouga et balade à dos de chameau vers le camp',
                    'description_en': 'Arrival in Merzouga and camel ride to desert camp',
                    'accommodation': 'مخيم صحراوي',
                    'breakfast_included': True,
                    'lunch_included': True,
                    'dinner_included': True
                }
            ]
        else:
            return

        for itinerary_data in itineraries:
            itinerary_data['package'] = package
            itinerary, created = Itinerary.objects.get_or_create(
                package=package,
                day_number=itinerary_data['day_number'],
                defaults=itinerary_data
            )
            if created:
                self.stdout.write(f'  تم إنشاء برنامج اليوم {itinerary.day_number}: {itinerary.title_ar}')
