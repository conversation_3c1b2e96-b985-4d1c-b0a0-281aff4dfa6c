"""
Quality management and review system for the travel agency.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from apps.core.models import TimeStampedModel


class ReviewCategory(TimeStampedModel):
    """Categories for organizing reviews."""

    name_ar = models.CharField(_('الاسم بالعربية'), max_length=100)
    name_fr = models.Char<PERSON>ield(_('الاسم بالفرنسية'), max_length=100, blank=True)
    name_en = models.CharField(_('الاسم بالإنجليزية'), max_length=100, blank=True)

    description = models.TextField(_('الوصف'), blank=True)
    icon = models.Char<PERSON><PERSON>(_('الأيقونة'), max_length=50, blank=True)

    # Settings
    is_active = models.BooleanField(_('نشط'), default=True)
    sort_order = models.PositiveIntegerField(_('ترتيب العرض'), default=0)

    class Meta:
        verbose_name = _('فئة التقييم')
        verbose_name_plural = _('فئات التقييمات')
        ordering = ['sort_order', 'name_ar']

    def __str__(self):
        return self.name_ar


class Review(TimeStampedModel):
    """Customer reviews and ratings."""

    STATUS_CHOICES = [
        ('pending', _('في الانتظار')),
        ('approved', _('موافق عليه')),
        ('rejected', _('مرفوض')),
        ('hidden', _('مخفي')),
    ]

    # Basic Information
    title = models.CharField(_('العنوان'), max_length=200)
    content = models.TextField(_('المحتوى'))
    rating = models.PositiveIntegerField(
        _('التقييم'),
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )

    # Reviewer Information
    reviewer_name = models.CharField(_('اسم المراجع'), max_length=100)
    reviewer_email = models.EmailField(_('بريد المراجع'), blank=True)
    reviewer_phone = models.CharField(_('هاتف المراجع'), max_length=20, blank=True)
    reviewer_nationality = models.CharField(_('جنسية المراجع'), max_length=50, blank=True)

    # Client Association (if registered client)
    client = models.ForeignKey(
        'crm.Client',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('العميل')
    )

    # Category
    category = models.ForeignKey(
        ReviewCategory,
        on_delete=models.CASCADE,
        verbose_name=_('الفئة')
    )

    # Related object (what is being reviewed)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')

    # Detailed Ratings
    service_rating = models.PositiveIntegerField(
        _('تقييم الخدمة'),
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True,
        blank=True
    )
    value_rating = models.PositiveIntegerField(
        _('تقييم القيمة مقابل المال'),
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True,
        blank=True
    )
    cleanliness_rating = models.PositiveIntegerField(
        _('تقييم النظافة'),
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True,
        blank=True
    )
    location_rating = models.PositiveIntegerField(
        _('تقييم الموقع'),
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True,
        blank=True
    )

    # Status and Moderation
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='pending')
    moderated_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('راجع بواسطة')
    )
    moderation_date = models.DateTimeField(_('تاريخ المراجعة'), null=True, blank=True)
    moderation_notes = models.TextField(_('ملاحظات المراجعة'), blank=True)

    # Verification
    is_verified_purchase = models.BooleanField(_('شراء موثق'), default=False)
    verification_code = models.CharField(_('رمز التحقق'), max_length=50, blank=True)

    # Interaction
    helpful_votes = models.PositiveIntegerField(_('أصوات مفيد'), default=0)
    not_helpful_votes = models.PositiveIntegerField(_('أصوات غير مفيد'), default=0)

    # Media
    images = models.JSONField(_('الصور'), default=list, blank=True)

    # Response from business
    response = models.TextField(_('رد الإدارة'), blank=True)
    response_date = models.DateTimeField(_('تاريخ الرد'), null=True, blank=True)
    responded_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='review_responses',
        verbose_name=_('رد بواسطة')
    )

    # Metadata
    ip_address = models.GenericIPAddressField(_('عنوان IP'), null=True, blank=True)
    user_agent = models.TextField(_('معلومات المتصفح'), blank=True)

    class Meta:
        verbose_name = _('تقييم')
        verbose_name_plural = _('التقييمات')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['content_type', 'object_id']),
            models.Index(fields=['status', 'rating']),
            models.Index(fields=['client']),
        ]

    def __str__(self):
        return f"{self.title} - {self.rating}/5"

    @property
    def average_detailed_rating(self):
        """Calculate average of detailed ratings."""
        ratings = [
            self.service_rating,
            self.value_rating,
            self.cleanliness_rating,
            self.location_rating
        ]
        valid_ratings = [r for r in ratings if r is not None]
        if valid_ratings:
            return sum(valid_ratings) / len(valid_ratings)
        return self.rating


class QualityStandard(TimeStampedModel):
    """Quality standards and criteria."""

    STANDARD_TYPES = [
        ('service', _('خدمة')),
        ('accommodation', _('إقامة')),
        ('transportation', _('نقل')),
        ('food', _('طعام')),
        ('guide', _('إرشاد')),
        ('safety', _('أمان')),
        ('cleanliness', _('نظافة')),
    ]

    name = models.CharField(_('اسم المعيار'), max_length=200)
    description = models.TextField(_('الوصف'))
    standard_type = models.CharField(_('نوع المعيار'), max_length=20, choices=STANDARD_TYPES)

    # Criteria
    criteria = models.JSONField(_('المعايير'), default=list)
    minimum_score = models.PositiveIntegerField(
        _('الحد الأدنى للنقاط'),
        validators=[MinValueValidator(1), MaxValueValidator(100)]
    )

    # Applicability
    applies_to_packages = models.BooleanField(_('ينطبق على الباقات'), default=True)
    applies_to_suppliers = models.BooleanField(_('ينطبق على الموردين'), default=True)
    applies_to_staff = models.BooleanField(_('ينطبق على الموظفين'), default=False)

    is_active = models.BooleanField(_('نشط'), default=True)

    class Meta:
        verbose_name = _('معيار جودة')
        verbose_name_plural = _('معايير الجودة')
        ordering = ['standard_type', 'name']

    def __str__(self):
        return f"{self.name} ({self.get_standard_type_display()})"


class QualityAssessment(TimeStampedModel):
    """Quality assessments based on standards."""

    STATUS_CHOICES = [
        ('draft', _('مسودة')),
        ('in_progress', _('قيد التنفيذ')),
        ('completed', _('مكتمل')),
        ('approved', _('موافق عليه')),
        ('rejected', _('مرفوض')),
    ]

    # Basic Information
    title = models.CharField(_('عنوان التقييم'), max_length=200)
    assessment_number = models.CharField(_('رقم التقييم'), max_length=20, unique=True)

    # Standards
    standards = models.ManyToManyField(
        QualityStandard,
        through='AssessmentStandardScore',
        verbose_name=_('المعايير')
    )

    # Subject of assessment
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')

    # Assessment Details
    assessment_date = models.DateField(_('تاريخ التقييم'))
    assessor = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('المقيم')
    )

    # Scores
    total_score = models.DecimalField(_('النقاط الإجمالية'), max_digits=5, decimal_places=2, default=0)
    max_possible_score = models.DecimalField(_('أقصى نقاط ممكنة'), max_digits=5, decimal_places=2, default=0)
    percentage_score = models.DecimalField(_('النسبة المئوية'), max_digits=5, decimal_places=2, default=0)

    # Status
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='draft')

    # Results
    passed = models.BooleanField(_('نجح'), default=False)
    grade = models.CharField(_('الدرجة'), max_length=5, blank=True)  # A, B, C, D, F

    # Follow-up
    recommendations = models.TextField(_('التوصيات'), blank=True)
    action_items = models.JSONField(_('بنود العمل'), default=list, blank=True)
    next_assessment_date = models.DateField(_('تاريخ التقييم القادم'), null=True, blank=True)

    notes = models.TextField(_('ملاحظات'), blank=True)

    class Meta:
        verbose_name = _('تقييم جودة')
        verbose_name_plural = _('تقييمات الجودة')
        ordering = ['-assessment_date']
        indexes = [
            models.Index(fields=['assessment_number']),
            models.Index(fields=['content_type', 'object_id']),
            models.Index(fields=['assessor', 'status']),
        ]

    def __str__(self):
        return f"{self.assessment_number} - {self.title}"

    def calculate_scores(self):
        """Calculate total and percentage scores."""
        scores = self.standard_scores.all()
        self.total_score = sum(score.score for score in scores)
        self.max_possible_score = sum(score.max_score for score in scores)

        if self.max_possible_score > 0:
            self.percentage_score = (self.total_score / self.max_possible_score) * 100
        else:
            self.percentage_score = 0

        # Determine grade and pass/fail
        if self.percentage_score >= 90:
            self.grade = 'A'
            self.passed = True
        elif self.percentage_score >= 80:
            self.grade = 'B'
            self.passed = True
        elif self.percentage_score >= 70:
            self.grade = 'C'
            self.passed = True
        elif self.percentage_score >= 60:
            self.grade = 'D'
            self.passed = False
        else:
            self.grade = 'F'
            self.passed = False

    def save(self, *args, **kwargs):
        if not self.assessment_number:
            # Generate assessment number
            last_assessment = QualityAssessment.objects.filter(
                assessment_number__startswith='QA'
            ).order_by('-id').first()
            if last_assessment:
                last_number = int(last_assessment.assessment_number[2:])
                self.assessment_number = f"QA{last_number + 1:06d}"
            else:
                self.assessment_number = "QA000001"

        super().save(*args, **kwargs)


class AssessmentStandardScore(TimeStampedModel):
    """Scores for individual standards in an assessment."""

    assessment = models.ForeignKey(
        QualityAssessment,
        on_delete=models.CASCADE,
        related_name='standard_scores',
        verbose_name=_('التقييم')
    )
    standard = models.ForeignKey(
        QualityStandard,
        on_delete=models.CASCADE,
        verbose_name=_('المعيار')
    )

    score = models.DecimalField(_('النقاط'), max_digits=5, decimal_places=2)
    max_score = models.DecimalField(_('أقصى نقاط'), max_digits=5, decimal_places=2)

    # Detailed scoring per criteria
    criteria_scores = models.JSONField(_('نقاط المعايير'), default=dict, blank=True)

    comments = models.TextField(_('تعليقات'), blank=True)

    class Meta:
        verbose_name = _('نقاط معيار التقييم')
        verbose_name_plural = _('نقاط معايير التقييم')
        unique_together = ['assessment', 'standard']

    def __str__(self):
        return f"{self.assessment.title} - {self.standard.name} - {self.score}/{self.max_score}"
