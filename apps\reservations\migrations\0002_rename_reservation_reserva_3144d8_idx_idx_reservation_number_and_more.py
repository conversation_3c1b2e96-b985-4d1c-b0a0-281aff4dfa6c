# Generated by Django 4.2.7 on 2025-06-13 09:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('reservations', '0001_initial'),
    ]

    operations = [
        migrations.RenameIndex(
            model_name='reservation',
            new_name='idx_reservation_number',
            old_name='reservation_reserva_3144d8_idx',
        ),
        migrations.RenameIndex(
            model_name='reservation',
            new_name='idx_client_status',
            old_name='reservation_client__d27462_idx',
        ),
        migrations.RenameIndex(
            model_name='reservation',
            new_name='idx_departure_date',
            old_name='reservation_departu_1fda8d_idx',
        ),
        migrations.RenameIndex(
            model_name='reservation',
            new_name='idx_status_payment',
            old_name='reservation_status_f56792_idx',
        ),
        migrations.AddIndex(
            model_name='reservation',
            index=models.Index(fields=['booking_date', 'status'], name='idx_booking_status'),
        ),
        migrations.AddIndex(
            model_name='reservation',
            index=models.Index(fields=['client', 'booking_date'], name='idx_client_booking'),
        ),
    ]
