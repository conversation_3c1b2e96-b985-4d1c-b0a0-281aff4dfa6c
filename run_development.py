#!/usr/bin/env python
"""
Development server runner for Moroccan Travel Agency ERP.
This script helps set up and run the development environment quickly.
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def run_command(command, description, check=True):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            return True
        else:
            print(f"❌ {description} failed: {result.stderr}")
            return False
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        return False

def check_requirements():
    """Check if all requirements are installed."""
    print("🔍 Checking requirements...")

    # Check Python version
    if sys.version_info < (3, 11):
        print("❌ Python 3.11+ is required")
        return False

    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Virtual environment not detected. It's recommended to use a virtual environment.")

    # Check if requirements.txt exists
    if not Path('requirements.txt').exists():
        print("❌ requirements.txt not found")
        return False

    print("✅ Requirements check passed")
    return True

def install_dependencies():
    """Install Python dependencies."""
    return run_command("pip install -r requirements.txt", "Installing Python dependencies")

def setup_database():
    """Set up the database."""
    print("🗄️ Setting up database...")

    # Check if migrations exist
    if not run_command("python manage.py showmigrations", "Checking migrations", check=False):
        return False

    # Make migrations
    if not run_command("python manage.py makemigrations", "Creating migrations"):
        return False

    # Apply migrations
    if not run_command("python manage.py migrate", "Applying migrations"):
        return False

    return True

def setup_initial_data():
    """Set up initial data."""
    return run_command("python setup_initial_data.py", "Setting up initial data")

def collect_static_files():
    """Collect static files."""
    return run_command("python manage.py collectstatic --noinput", "Collecting static files")

def create_superuser():
    """Create superuser if needed."""
    print("👤 Checking for superuser...")

    # Check if admin user exists
    check_admin = subprocess.run(
        'python manage.py shell -c "from django.contrib.auth import get_user_model; User = get_user_model(); print(User.objects.filter(is_superuser=True).exists())"',
        shell=True,
        capture_output=True,
        text=True
    )

    if "True" in check_admin.stdout:
        print("✅ Superuser already exists")
        return True

    print("Creating superuser...")
    print("Please enter superuser details:")
    return run_command("python manage.py createsuperuser", "Creating superuser", check=False)

def start_services():
    """Start development services."""
    print("🚀 Starting development services...")

    # Check if Redis is running (for Celery)
    redis_check = subprocess.run("redis-cli ping", shell=True, capture_output=True, text=True)
    if "PONG" not in redis_check.stdout:
        print("⚠️  Redis is not running. Celery tasks will not work.")
        print("   Please start Redis server: redis-server")

    # Start Django development server
    print("🌐 Starting Django development server...")
    print("   Server will be available at: http://localhost:8000")
    print("   Admin panel: http://localhost:8000/admin")
    print("   API documentation: http://localhost:8000/api/docs")
    print("\n📝 Default admin credentials:")
    print("   Username: admin")
    print("   Password: admin123")
    print("\n⚠️  Please change the admin password after first login!")
    print("\n🛑 Press Ctrl+C to stop the server")

    try:
        subprocess.run("python manage.py runserver", shell=True, check=True)
    except KeyboardInterrupt:
        print("\n👋 Development server stopped")

def main():
    """Main function to set up and run the development environment."""
    print("🏢 Moroccan Travel Agency ERP - Development Setup")
    print("=" * 60)

    # Check if we're in the right directory
    if not Path('manage.py').exists():
        print("❌ manage.py not found. Please run this script from the project root directory.")
        sys.exit(1)

    # Check requirements
    if not check_requirements():
        sys.exit(1)

    # Ask user what to do
    print("\nWhat would you like to do?")
    print("1. Full setup (install dependencies, setup database, run server)")
    print("2. Quick start (just run the server)")
    print("3. Setup only (install dependencies and setup database)")
    print("4. Reset database (recreate database and initial data)")

    choice = input("\nEnter your choice (1-4): ").strip()

    if choice == "1":
        # Full setup
        print("\n🔧 Starting full setup...")

        if not install_dependencies():
            sys.exit(1)

        if not setup_database():
            sys.exit(1)

        if not setup_initial_data():
            sys.exit(1)

        if not collect_static_files():
            sys.exit(1)

        if not create_superuser():
            print("⚠️  Superuser creation skipped or failed")

        start_services()

    elif choice == "2":
        # Quick start
        print("\n🚀 Quick start...")
        start_services()

    elif choice == "3":
        # Setup only
        print("\n🔧 Setup only...")

        if not install_dependencies():
            sys.exit(1)

        if not setup_database():
            sys.exit(1)

        if not setup_initial_data():
            sys.exit(1)

        if not collect_static_files():
            sys.exit(1)

        print("✅ Setup completed! Run 'python manage.py runserver' to start the server.")

    elif choice == "4":
        # Reset database
        print("\n🔄 Resetting database...")

        confirm = input("⚠️  This will delete all data. Are you sure? (yes/no): ").strip().lower()
        if confirm != "yes":
            print("Operation cancelled.")
            sys.exit(0)

        # Remove migration files (except __init__.py)
        for app_dir in Path('apps').iterdir():
            if app_dir.is_dir():
                migrations_dir = app_dir / 'migrations'
                if migrations_dir.exists():
                    for migration_file in migrations_dir.glob('*.py'):
                        if migration_file.name != '__init__.py':
                            migration_file.unlink()
                            print(f"Removed {migration_file}")

        # Remove database file if using SQLite
        db_file = Path('db.sqlite3')
        if db_file.exists():
            db_file.unlink()
            print("Removed SQLite database")

        # Recreate database
        if not setup_database():
            sys.exit(1)

        if not setup_initial_data():
            sys.exit(1)

        print("✅ Database reset completed!")

    else:
        print("❌ Invalid choice")
        sys.exit(1)

if __name__ == '__main__':
    main()
