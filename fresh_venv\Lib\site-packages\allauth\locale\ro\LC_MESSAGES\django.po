# DJANGO-ALLAUTH.
# Copyright (C) 2016
# This file is distributed under the same license as the django-allauth package.
#
# Translators:
# <PERSON> <steve.koss<PERSON><PERSON>@yahoo.fr>, 2016.
# <PERSON><PERSON> <<EMAIL>>, 2019
#
msgid ""
msgstr ""
"Project-Id-Version: django-allauthrr\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-07 04:22-0500\n"
"PO-Revision-Date: 2021-11-08 19:23+0200\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: ro_RO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n==0 || (n!=1 && n%100>=1 && n"
"%100<=19) ? 1 : 2);\n"
"X-Generator: Poedit 3.0\n"

#: account/adapter.py:48
msgid "Username can not be used. Please use other username."
msgstr ""
"Numele de utilizator nu este disponibil. Te rugam alege alt nume de "
"utilizator."

#: account/adapter.py:54
msgid "Too many failed login attempts. Try again later."
msgstr "Prea multe incercări de conectare esuate. Incearca mai tarziu."

#: account/adapter.py:56
msgid "A user is already registered with this email address."
msgstr "Un utilizator este deja inregistrat cu aceasta adresa de e-mail."

#: account/adapter.py:57
#, fuzzy
#| msgid "Current Password"
msgid "Incorrect password."
msgstr "Parola actuala"

#: account/adapter.py:308
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "Parola trebuie să aibă minimum {0} caractere."

#: account/apps.py:9
msgid "Accounts"
msgstr "Conturi"

#: account/forms.py:58 account/forms.py:432
msgid "You must type the same password each time."
msgstr "Trebuie sa tastezi aceeași parolă de fiecare data."

#: account/forms.py:90 account/forms.py:397 account/forms.py:522
#: account/forms.py:658
msgid "Password"
msgstr "Parola"

#: account/forms.py:91
msgid "Remember Me"
msgstr "Tine-ma minte"

#: account/forms.py:95
msgid "This account is currently inactive."
msgstr "Acest cont este in prezent inactiv."

#: account/forms.py:97
msgid "The email address and/or password you specified are not correct."
msgstr "Adresa de e-mail si / sau parola sunt incorecte."

#: account/forms.py:100
msgid "The username and/or password you specified are not correct."
msgstr "Numele de utilizator si / sau parola sunt incorecte."

#: account/forms.py:111 account/forms.py:271 account/forms.py:459
#: account/forms.py:540
msgid "Email address"
msgstr "Adresa de e-mail"

#: account/forms.py:115 account/forms.py:316 account/forms.py:456
#: account/forms.py:535
msgid "Email"
msgstr "E-mail"

#: account/forms.py:118 account/forms.py:121 account/forms.py:261
#: account/forms.py:264
msgid "Username"
msgstr "Nume de utilizator"

#: account/forms.py:131
msgid "Username or email"
msgstr "Nume de utilizator sau e-mail"

#: account/forms.py:134
msgctxt "field label"
msgid "Login"
msgstr "Nume de utilizator sau e-mail"

#: account/forms.py:307
msgid "Email (again)"
msgstr "E-mail (confirma)"

#: account/forms.py:311
msgid "Email address confirmation"
msgstr "Confirmarea adresei de e-mail"

#: account/forms.py:319
msgid "Email (optional)"
msgstr "E-mail (optional)"

#: account/forms.py:368
msgid "You must type the same email each time."
msgstr "Trebuie sa tastezi aceeasi adresa de e-mail de fiecare data."

#: account/forms.py:401 account/forms.py:523
msgid "Password (again)"
msgstr "Parola (confirma)"

#: account/forms.py:470
msgid "This email address is already associated with this account."
msgstr "Aceasta adresa de e-mail este deja asociata acestui cont."

#: account/forms.py:472
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Nu poti adauga mai mult de %d adrese de e-mail."

#: account/forms.py:503
msgid "Current Password"
msgstr "Parola actuala"

#: account/forms.py:505 account/forms.py:607
msgid "New Password"
msgstr "Parola noua"

#: account/forms.py:506 account/forms.py:608
msgid "New Password (again)"
msgstr "Parola noua (confirma)"

#: account/forms.py:514
msgid "Please type your current password."
msgstr "Te rugam tasteaza parola actuala."

#: account/forms.py:552
msgid "The email address is not assigned to any user account"
msgstr "Adresa de e-mail nu este atribuita niciunui cont de utilizator"

#: account/forms.py:628
msgid "The password reset token was invalid."
msgstr "Link-ul de resetare a parolei nu era valid."

#: account/models.py:21
msgid "user"
msgstr "utilizator"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "adresa de e-mail"

#: account/models.py:28
msgid "verified"
msgstr "verificata"

#: account/models.py:29
msgid "primary"
msgstr "principala"

#: account/models.py:35
msgid "email addresses"
msgstr "adrese de e-mail"

#: account/models.py:141
msgid "created"
msgstr "creata"

#: account/models.py:142
msgid "sent"
msgstr "trimis"

#: account/models.py:143 socialaccount/models.py:62
msgid "key"
msgstr "cheie"

#: account/models.py:148
msgid "email confirmation"
msgstr "confirmare e-mail"

#: account/models.py:149
msgid "email confirmations"
msgstr "confirmari e-mail"

#: mfa/adapter.py:19
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""

#: mfa/adapter.py:22
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""

#: mfa/adapter.py:24
msgid "Incorrect code."
msgstr ""

#: mfa/apps.py:7
msgid "MFA"
msgstr ""

#: mfa/forms.py:12
msgid "Code"
msgstr ""

#: mfa/forms.py:29
msgid "Authenticator code"
msgstr ""

#: mfa/models.py:15
msgid "Recovery codes"
msgstr ""

#: mfa/models.py:16
msgid "TOTP Authenticator"
msgstr ""

#: socialaccount/adapter.py:30
#, fuzzy, python-format
#| msgid ""
#| "An account already exists with this e-mail address. Please sign in to "
#| "that account first, then connect your %s account."
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Exista deja un cont cu această adresa de e-mail. Te rugam sa vt conectați "
"mai intai la acel cont, apoi sa conecteazi contul %s."

#: socialaccount/adapter.py:136
msgid "Your account has no password set up."
msgstr "Contul tau nu are o parola setata."

#: socialaccount/adapter.py:143
msgid "Your account has no verified email address."
msgstr "Contul tau nu are o adresa de e-mail confirmata."

#: socialaccount/apps.py:7
msgid "Social Accounts"
msgstr "Retele de socializare"

#: socialaccount/models.py:36 socialaccount/models.py:97
msgid "provider"
msgstr "furnizor"

#: socialaccount/models.py:45
#, fuzzy
#| msgid "provider"
msgid "provider ID"
msgstr "furnizor"

#: socialaccount/models.py:49
msgid "name"
msgstr "nume"

#: socialaccount/models.py:51
msgid "client id"
msgstr "id client"

#: socialaccount/models.py:53
msgid "App ID, or consumer key"
msgstr "ID-ul aplicatiei sau cheia consumatorului"

#: socialaccount/models.py:56
msgid "secret key"
msgstr "cheie secreta"

#: socialaccount/models.py:59
msgid "API secret, client secret, or consumer secret"
msgstr "API secret, client secret sau consumator secret"

#: socialaccount/models.py:62
msgid "Key"
msgstr "Cheie"

#: socialaccount/models.py:81
msgid "social application"
msgstr "aplicatie sociala"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "aplicatii sociale"

#: socialaccount/models.py:117
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:119
msgid "last login"
msgstr "ultima logare"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "data inscrierii"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "date suplimentare"

#: socialaccount/models.py:125
msgid "social account"
msgstr "retea de socializare"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "retele de socializare"

#: socialaccount/models.py:160
msgid "token"
msgstr "token"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) sau token de acces (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "token secret"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) token de reimprospatare (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "expira la"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "token pentru aplicatia de socializare"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "token-uri pentru aplicatiile de socializare"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Date de profil invalide"

#: socialaccount/providers/oauth/client.py:85
#, fuzzy, python-format
#| msgid "Invalid response while obtaining request token from \"%s\"."
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "Raspuns invalid la obtinerea token-ului de solicitare de la \"% s\"."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:78
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Raspuns invalid la obtinerea token-ului de acces de la \"% s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Nu s-a salvat niciun token de solicitare pentru \"% s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Nu s-a salvat niciun token de acces pentru \"% s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Nu exista acces la resurse private la \"% s\"."

#: socialaccount/providers/pocket/client.py:37
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Raspuns invalid la obtinerea token-ului de solicitare de la \"% s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:8
msgid "Account Inactive"
msgstr "Cont inactiv"

#: templates/account/account_inactive.html:10
msgid "This account is inactive."
msgstr "Acest cont este inactiv."

#: templates/account/base.html:16
msgid "Messages:"
msgstr ""

#: templates/account/base.html:26
msgid "Menu:"
msgstr ""

#: templates/account/base.html:29 templates/account/email_change.html:31
#, fuzzy
#| msgid "Email"
msgid "Change Email"
msgstr "E-mail"

#: templates/account/base.html:30 templates/account/logout.html:5
#: templates/account/logout.html:8 templates/account/logout.html:17
msgid "Sign Out"
msgstr "Deconecteaza-te"

#: templates/account/base.html:32 templates/account/login.html:6
#: templates/account/login.html:10 templates/account/login.html:43
#: templates/mfa/authenticate.html:4 templates/mfa/authenticate.html:16
#: templates/socialaccount/login.html:4
msgid "Sign In"
msgstr "Conecteaza-te"

#: templates/account/base.html:33 templates/account/signup.html:8
#: templates/account/signup.html:18 templates/socialaccount/signup.html:8
#: templates/socialaccount/signup.html:19
msgid "Sign Up"
msgstr "Inregistreaza-te"

#: templates/account/email.html:5 templates/account/email.html:8
msgid "Email Addresses"
msgstr "Adrese de e-mail"

#: templates/account/email.html:10
msgid "The following email addresses are associated with your account:"
msgstr "Urmatoarele adrese de e-mail sunt asociate contului tau:"

#: templates/account/email.html:24
msgid "Verified"
msgstr "Verificata"

#: templates/account/email.html:26
msgid "Unverified"
msgstr "Neverificata"

#: templates/account/email.html:28
msgid "Primary"
msgstr "Principala"

#: templates/account/email.html:34
msgid "Make Primary"
msgstr "Seteaza ca principala"

#: templates/account/email.html:35 templates/account/email_change.html:21
msgid "Re-send Verification"
msgstr "Retrimite e-mail verificare"

#: templates/account/email.html:36 templates/socialaccount/connections.html:35
msgid "Remove"
msgstr "Elimina adresa"

#: templates/account/email.html:47
msgid "Add Email Address"
msgstr "Adauga adresa de e-mail"

#: templates/account/email.html:52
msgid "Add Email"
msgstr "Adauga e-mail"

#: templates/account/email.html:62
msgid "Do you really want to remove the selected email address?"
msgstr "Sigur doresti sa elimini aceasta adresa de e-mail?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr ""

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Salutari de la %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Iti multumim ca folosesti %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "You're receiving this e-mail because user %(user_display)s has given your "
#| "e-mail address to register an account on %(site_domain)s.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"Ai primit acest e-mail pentru ca utilizatorul %(user_display)s a folosit "
"aceasta adresa de e-mail pentru a inregistra un cont pe %(site_domain)s.\n"
"\n"
"Pentru a confirma, click pe link-ul urmator: %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Te rugam confirma adresa de e-mail"

#: templates/account/email/password_reset_key_message.txt:4
#, fuzzy
#| msgid ""
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Ai primit acest e-mail pentru ca tu sau altcineva a solicitat o resetare de "
"parola.\n"
"Daca nu ai fost tu cel care a solicitat resetarea parolei, te rugam sa "
"ignori acest e-mail.\n"
"Pentru resetarea parolei acceseaza linkul de mai jos."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "In cazul in care ai uitat, numele tau de utilizator este %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "E-mail pentru resetarea parolei"

#: templates/account/email/unknown_account_message.txt:4
#, fuzzy, python-format
#| msgid ""
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"Ai primit acest e-mail pentru ca tu sau altcineva a solicitat o resetare de "
"parola.\n"
"Daca nu ai fost tu cel care a solicitat resetarea parolei, te rugam sa "
"ignori acest e-mail.\n"
"Pentru resetarea parolei acceseaza linkul de mai jos."

#: templates/account/email_change.html:4 templates/account/email_change.html:7
#, fuzzy
#| msgid "Email Addresses"
msgid "Email Address"
msgstr "Adrese de e-mail"

#: templates/account/email_change.html:11
#, fuzzy
#| msgid "The following email addresses are associated with your account:"
msgid "The following email address is associated with your account:"
msgstr "Urmatoarele adrese de e-mail sunt asociate contului tau:"

#: templates/account/email_change.html:16
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Your email address is still pending verification:"
msgstr ""
"Adresa de e-mail trebuie mai intai confirmata inainte de a o seta ca adresa "
"principala. Acceseaza link-ul din e-mailul de verificare."

#: templates/account/email_change.html:27
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Change Email Address"
msgstr "Confirma adresa de e-mail"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Confirma adresa de e-mail"

#: templates/account/email_confirm.html:17
#, fuzzy, python-format
#| msgid ""
#| "Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an e-"
#| "mail address for user %(user_display)s."
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Te rugam confirma faptul ca <a href=\"mailto:%(email)s\">%(email)s</a> este "
"o adresa de e-mail a utilizatorului %(user_display)s."

#: templates/account/email_confirm.html:21
#: templates/account/reauthenticate.html:19
msgid "Confirm"
msgstr "Confirma"

#: templates/account/email_confirm.html:24
#: templates/account/messages/email_confirmation_failed.txt:2
#, fuzzy, python-format
#| msgid "The social account is already connected to a different account."
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr ""
"Acest cont de socializare este deja asociat unui alt profil pe site-ul "
"nostru."

#: templates/account/email_confirm.html:31
#, fuzzy, python-format
#| msgid ""
#| "This e-mail confirmation link expired or is invalid. Please <a href="
#| "\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgid ""
"This email confirmation link expired or is invalid. Please <a href="
"\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Link-ul de confirmare a adresei de email a expirat sau este invalid. Te "
"rugam <a href=\"%(email_url)s\"> solicita un nou link de confirmare</a>."

#: templates/account/login.html:15
#, python-format
msgid ""
"Please sign in with one\n"
"of your existing third party accounts. Or, <a href=\"%(signup_url)s\">sign "
"up</a>\n"
"for a %(site_name)s account and sign in below:"
msgstr ""
"Te rugam conecteaza-te cu unul\n"
"din conturile de mai jos. Sau <a href=\"%(signup_url)s\">creeaza un cont</"
"a>\n"
"%(site_name)s si apoi conecteaza-te folosind formularul de mai jos:"

#: templates/account/login.html:25
msgid "or"
msgstr "sau"

#: templates/account/login.html:32
#, python-format
msgid ""
"If you have not created an account yet, then please\n"
"<a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"Daca nu ai un cont inca, te rugam\n"
"<a href=\"%(signup_url)s\">inregistreaza-te</a>."

#: templates/account/login.html:42 templates/account/password_change.html:14
msgid "Forgot Password?"
msgstr "Ai uitat parola?"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "Esti sigur(a) ca vrei sa te deconectezi?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr ""
"Nu poti elimina adresa de e-mail  (%(email)s). \n"
"Aceasta este setata ca adresa principala de e-mail."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Confirma e-mailul trimis catre %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Ai confirmat %(email)s."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "Adresa de e-mail eliminata %(email)s."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Esti conectat(a) ca %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Te-ai deconectat."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Parola a fost schimbata cu success."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Parola a fost setata cu success."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Adresa principala de e-mail a fost setata."

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr ""
"Adresa de e-mail trebuie mai intai confirmata inainte de a o seta ca adresa "
"principala. Acceseaza link-ul din e-mailul de verificare."

#: templates/account/password_change.html:5
#: templates/account/password_change.html:8
#: templates/account/password_change.html:13
#: templates/account/password_reset_from_key.html:4
#: templates/account/password_reset_from_key.html:7
#: templates/account/password_reset_from_key_done.html:4
#: templates/account/password_reset_from_key_done.html:7
msgid "Change Password"
msgstr "Schimba parola"

#: templates/account/password_reset.html:6
#: templates/account/password_reset.html:10
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:9
msgid "Password Reset"
msgstr "Reseteaza parola"

#: templates/account/password_reset.html:15
#, fuzzy
#| msgid ""
#| "Forgotten your password? Enter your e-mail address below, and we'll send "
#| "you an e-mail allowing you to reset it."
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Ti-ai uitat parola? Introdu adresa de e-mail mai jos si-ti vom trimite un e-"
"mail ce-ti va permite sa resetezi parola."

#: templates/account/password_reset.html:20
msgid "Reset My Password"
msgstr "Reseteaza parola"

#: templates/account/password_reset.html:23
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Te rugam contacteaza-ne daca intampini dificultati in resetarea parolei."

#: templates/account/password_reset_done.html:15
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Ti-am trimis un e-mail pentru verificare (confirmare). \n"
"Acceseaza link-ul din e-mail pentru confirmare. \n"
"Te rugam contacteaza-ne daca nu primesti e-mailul in cateva minute."

#: templates/account/password_reset_from_key.html:7
msgid "Bad Token"
msgstr "Token invalid"

#: templates/account/password_reset_from_key.html:11
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Link-ul de resetare a parolei este invalid, posibil din cauza ca a fost deja "
"folosit. Te rugam solicita <a href=\"%(passwd_reset_url)s\">un nou link de "
"resetare a parolei</a>."

#: templates/account/password_reset_from_key.html:16
msgid "change password"
msgstr "schimba parola"

#: templates/account/password_reset_from_key_done.html:8
msgid "Your password is now changed."
msgstr "Parola ta a fost schimbata cu succes."

#: templates/account/password_set.html:5 templates/account/password_set.html:8
#: templates/account/password_set.html:13
msgid "Set Password"
msgstr "Seteaza parola"

#: templates/account/reauthenticate.html:5
#: templates/account/reauthenticate.html:9
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Confirm Access"
msgstr "Confirma adresa de e-mail"

#: templates/account/reauthenticate.html:11
msgid "To safeguard the security of your account, please enter your password:"
msgstr ""

#: templates/account/signup.html:5 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Inregistrare"

#: templates/account/signup.html:10
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr "Ai deja un cont? Te rugam <a href=\"%(login_url)s\">conecteaza-te</a>."

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:8
msgid "Sign Up Closed"
msgstr "Inregistrare blocata"

#: templates/account/signup_closed.html:10
msgid "We are sorry, but the sign up is currently closed."
msgstr "Ne pare rau, posibilitatea inregistrarii este momentan blocata."

#: templates/account/snippets/already_logged_in.html:5
msgid "Note"
msgstr "Nota"

#: templates/account/snippets/already_logged_in.html:5
#, python-format
msgid "you are already logged in as %(user_display)s."
msgstr "esti deja conectat ca %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Avertizare:"

#: templates/account/snippets/warn_no_email.html:3
#, fuzzy
#| msgid ""
#| "You currently do not have any e-mail address set up. You should really "
#| "add an e-mail address so you can receive notifications, reset your "
#| "password, etc."
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"În prezent nu ai configurata nicio adresa de e-mail. Adauga o adresa de e-"
"mail pentru a primi notificari, a putea reseta parola, etc."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:8
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:8
msgid "Verify Your Email Address"
msgstr "Verifica-ti adresa de e-mail"

#: templates/account/verification_sent.html:10
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for verification. Follow the link provided "
#| "to finalize the signup process. Please contact us if you do not receive "
#| "it within a few minutes."
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Ti-am trimis un e-mail pentru verificare (confirmare). Acceseaza link-ul din "
"e-mail pentru a finaliza procesul de inregistrare. Te rugam contacteaza-ne "
"daca nu primesti e-mailul in cateva minute."

#: templates/account/verified_email_required.html:12
#, fuzzy
#| msgid ""
#| "This part of the site requires us to verify that\n"
#| "you are who you claim to be. For this purpose, we require that you\n"
#| "verify ownership of your e-mail address. "
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Pentru a accesa aceasta sectiune a site-ului \n"
"te rugam sa confirmi ca aceasta adresa de e-mail iti apartine. "

#: templates/account/verified_email_required.html:16
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Ti-am trimis un e-mail pentru verificare (confirmare). \n"
"Acceseaza link-ul din e-mail pentru confirmare. \n"
"Te rugam contacteaza-ne daca nu primesti e-mailul in cateva minute."

#: templates/account/verified_email_required.html:20
#, fuzzy, python-format
#| msgid ""
#| "<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change "
#| "your e-mail address</a>."
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Nota:</strong> poti in continuarea <a href=\"%(email_url)s\">sa-ti "
"schimbi adresa de e-mail</a>."

#: templates/mfa/authenticate.html:7 templates/mfa/index.html:4
#: templates/mfa/index.html:7
msgid "Two-Factor Authentication"
msgstr ""

#: templates/mfa/authenticate.html:9
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""

#: templates/mfa/index.html:9 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr ""

#: templates/mfa/index.html:11
msgid "Authentication using an authenticator app is active."
msgstr ""

#: templates/mfa/index.html:14 templates/mfa/totp/deactivate_form.html:11
msgid "Deactivate"
msgstr ""

#: templates/mfa/index.html:18
msgid "An authenticator app is not active."
msgstr ""

#: templates/mfa/index.html:21 templates/mfa/totp/activate_form.html:16
msgid "Activate"
msgstr ""

#: templates/mfa/index.html:27 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:4
#: templates/mfa/recovery_codes/index.html:4
msgid "Recovery Codes"
msgstr ""

#: templates/mfa/index.html:30 templates/mfa/recovery_codes/index.html:6
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: templates/mfa/index.html:34
msgid "View codes"
msgstr ""

#: templates/mfa/index.html:37 templates/mfa/recovery_codes/index.html:16
msgid "Download codes"
msgstr ""

#: templates/mfa/index.html:40 templates/mfa/recovery_codes/index.html:20
msgid "Generate new codes"
msgstr ""

#: templates/mfa/index.html:44
msgid "No recovery codes set up."
msgstr ""

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr ""

#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr ""

#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:6
msgid ""
"You are about to generate a new set of recovery codes for your account. This "
"action will invalidate your existing codes. Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/generate.html:11
msgid "Generate"
msgstr ""

#: templates/mfa/totp/activate_form.html:4
msgid "Activate Authenticator App"
msgstr ""

#: templates/mfa/totp/activate_form.html:9
#, fuzzy
#| msgid "token secret"
msgid "Authenticator secret"
msgstr "token secret"

#: templates/mfa/totp/deactivate_form.html:4
msgid "Deactivate Authenticator App"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:6
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""

#: templates/openid/login.html:9
msgid "OpenID Sign In"
msgstr "Conectare OpenID"

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:8
msgid "Social Network Login Failure"
msgstr "Conectarea la reteaua sociala a esuat"

#: templates/socialaccount/authentication_error.html:10
msgid ""
"An error occurred while attempting to login via your social network account."
msgstr ""
"A aparut o eroare in incercarea de a te autentifica folosind aceasta cont de "
"retea sociala."

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:8
msgid "Account Connections"
msgstr "Conexiuni"

#: templates/socialaccount/connections.html:11
msgid ""
"You can sign in to your account using any of the following third party "
"accounts:"
msgstr ""
"Te poti conecta la contul tau folosind oricare dintre urmatoarele conturi de "
"socializare (conturi terte):"

#: templates/socialaccount/connections.html:43
msgid ""
"You currently have no social network accounts connected to this account."
msgstr "In present nu ai niciun cont de retea sociala asociat cu acest cont."

#: templates/socialaccount/connections.html:46
msgid "Add a 3rd Party Account"
msgstr "Adauga un cont de retea sociala"

#: templates/socialaccount/login.html:8
#, python-format
msgid "Connect %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:10
#, python-format
msgid "You are about to connect a new third party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:12
#, python-format
msgid "Sign In Via %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:14
#, python-format
msgid "You are about to sign in using a third party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:19
msgid "Continue"
msgstr ""

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Conectare anulata"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a href=\"%(login_url)s"
"\">sign in</a>."
msgstr ""
"Ai decis sa anulezi procesul de conectare la contul tau. Daca ai anulat din "
"greseala, te rugam acceseaza link-ul <a href=\"%(login_url)s\">conecteaza-te "
"la contul tau</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The social account has been connected."
msgstr "Contul a fost conectat cu success."

#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The social account is already connected to a different account."
msgstr ""
"Acest cont de socializare este deja asociat unui alt profil pe site-ul "
"nostru."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The social account has been disconnected."
msgstr "Contul de socializare a fost deconectat."

#: templates/socialaccount/signup.html:10
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Esti pe cale de a folosi contul %(provider_name)s pentru a te conecta la\n"
"%(site_name)s. Pentru finalizarea procesului, te rugam completeaza urmatorul "
"formular:"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Aceasta adresa de e-mail este deja asociata altui cont."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Ti-am trimis un e-mail. Daca nu-l gasesti in \"inbox\", te rugam verifica "
#~ "si folderul \"spam\".\n"
#~ "Te rugam contacteaza-ne daca nu-l primesti in cateva minute."

#~ msgid "Account"
#~ msgstr "Compte"

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "L'identifiant ou le mot de passe sont incorrects."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr ""
#~ "Un pseudonyme ne peut contenir que des lettres, des chiffres, ainsi que "
#~ "@/./+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "Ce pseudonyme est déjà utilisé, merci d'en choisir un autre."

#~ msgid "Shopify Sign In"
#~ msgstr "Connexion Shopify"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "Vous avez confirmé que l'adresse e-mail de l'utilsateur %(user_display)s "
#~ "est <a href=\"mailto:%(email)s\">%(email)s</a>."

#~ msgid "Thanks for using our site!"
#~ msgstr "Merci d'utiliser notre site !"
