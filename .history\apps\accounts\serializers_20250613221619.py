"""
Serializers for accounts app models.
"""
from rest_framework import serializers
from django.contrib.auth.models import Permission
from django.contrib.auth import get_user_model
from apps.accounts.models import Role

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    """
    Serializer for User model.
    """
    class Meta:
        """Meta class for UserSerializer configuration."""
        model = User
        fields = [
            'id', 'username', 'email', 'first_name',
            'last_name', 'is_active', 'is_staff'
        ]
        read_only_fields = ['id']


class RoleSerializer(serializers.ModelSerializer):
    """
    Serializer for Role model.
    """
    class Meta:
        """Meta class for RoleSerializer configuration."""
        model = Role
        fields = ['id', 'name', 'description', 'permissions']
        read_only_fields = ['id']


class PermissionSerializer(serializers.ModelSerializer):
    """
    Serializer for Permission model.
    """
    class Meta:
        """Meta class for PermissionSerializer configuration."""
        model = Permission
        fields = ['id', 'name', 'codename', 'content_type']
        read_only_fields = ['id']
