"""
Event and activity management system for the travel agency.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator
from apps.core.models import TimeStampedModel


class EventCategory(TimeStampedModel):
    """Categories for organizing events."""

    name_ar = models.CharField(_('الاسم بالعربية'), max_length=100)
    name_fr = models.Char<PERSON><PERSON>(_('الاسم بالفرنسية'), max_length=100, blank=True)
    name_en = models.CharField(_('الاسم بالإنجليزية'), max_length=100, blank=True)

    description = models.TextField(_('الوصف'), blank=True)
    icon = models.CharField(_('الأيقونة'), max_length=50, blank=True)
    color = models.CharField(_('اللون'), max_length=7, default='#007bff')

    is_active = models.BooleanField(_('نشط'), default=True)
    sort_order = models.PositiveIntegerField(_('ترتيب العرض'), default=0)

    class Meta:
        verbose_name = _('فئة الحدث')
        verbose_name_plural = _('فئات الأحداث')
        ordering = ['sort_order', 'name_ar']

    def __str__(self):
        return self.name_ar


class Event(TimeStampedModel):
    """Events and activities."""

    STATUS_CHOICES = [
        ('draft', _('مسودة')),
        ('published', _('منشور')),
        ('cancelled', _('ملغي')),
        ('postponed', _('مؤجل')),
        ('completed', _('مكتمل')),
    ]

    EVENT_TYPES = [
        ('conference', _('مؤتمر')),
        ('workshop', _('ورشة عمل')),
        ('seminar', _('ندوة')),
        ('exhibition', _('معرض')),
        ('festival', _('مهرجان')),
        ('tour', _('جولة')),
        ('meeting', _('اجتماع')),
        ('training', _('تدريب')),
        ('celebration', _('احتفال')),
        ('other', _('أخرى')),
    ]

    # Basic Information
    title_ar = models.CharField(_('العنوان بالعربية'), max_length=200)
    title_fr = models.CharField(_('العنوان بالفرنسية'), max_length=200, blank=True)
    title_en = models.CharField(_('العنوان بالإنجليزية'), max_length=200, blank=True)

    description_ar = models.TextField(_('الوصف بالعربية'))
    description_fr = models.TextField(_('الوصف بالفرنسية'), blank=True)
    description_en = models.TextField(_('الوصف بالإنجليزية'), blank=True)

    # Classification
    category = models.ForeignKey(EventCategory, on_delete=models.CASCADE, verbose_name=_('الفئة'))
    event_type = models.CharField(_('نوع الحدث'), max_length=20, choices=EVENT_TYPES)

    # Date and Time
    start_date = models.DateField(_('تاريخ البداية'))
    end_date = models.DateField(_('تاريخ النهاية'))
    start_time = models.TimeField(_('وقت البداية'), null=True, blank=True)
    end_time = models.TimeField(_('وقت النهاية'), null=True, blank=True)

    # Location
    venue_name = models.CharField(_('اسم المكان'), max_length=200)
    venue_address = models.TextField(_('عنوان المكان'))
    city = models.ForeignKey('core.City', on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_('المدينة'))

    # Geographic coordinates
    latitude = models.DecimalField(_('خط العرض'), max_digits=9, decimal_places=6, null=True, blank=True)
    longitude = models.DecimalField(_('خط الطول'), max_digits=9, decimal_places=6, null=True, blank=True)

    # Capacity and Registration
    max_participants = models.PositiveIntegerField(_('الحد الأقصى للمشاركين'), null=True, blank=True)
    current_participants = models.PositiveIntegerField(_('المشاركون الحاليون'), default=0)
    registration_required = models.BooleanField(_('التسجيل مطلوب'), default=True)
    registration_deadline = models.DateField(_('موعد انتهاء التسجيل'), null=True, blank=True)

    # Pricing
    is_free = models.BooleanField(_('مجاني'), default=True)
    price = models.DecimalField(_('السعر'), max_digits=10, decimal_places=2, default=0)
    early_bird_price = models.DecimalField(_('سعر التسجيل المبكر'), max_digits=10, decimal_places=2, null=True, blank=True)
    early_bird_deadline = models.DateField(_('موعد انتهاء التسجيل المبكر'), null=True, blank=True)

    # Organization
    organizer = models.CharField(_('المنظم'), max_length=200)
    contact_person = models.CharField(_('شخص الاتصال'), max_length=100)
    contact_email = models.EmailField(_('بريد الاتصال'))
    contact_phone = models.CharField(_('هاتف الاتصال'), max_length=20)

    # Staff Assignment
    assigned_staff = models.ManyToManyField(
        'accounts.User',
        through='EventStaffAssignment',
        related_name='assigned_events',
        verbose_name=_('الموظفون المسندون')
    )

    # Status
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='draft')
    is_featured = models.BooleanField(_('مميز'), default=False)
    is_public = models.BooleanField(_('عام'), default=True)

    # Media
    main_image = models.ImageField(_('الصورة الرئيسية'), upload_to='events/', blank=True)
    gallery = models.JSONField(_('معرض الصور'), default=list, blank=True)

    # Additional Information
    requirements = models.TextField(_('المتطلبات'), blank=True)
    agenda = models.JSONField(_('جدول الأعمال'), default=list, blank=True)
    speakers = models.JSONField(_('المتحدثون'), default=list, blank=True)
    sponsors = models.JSONField(_('الرعاة'), default=list, blank=True)

    # SEO
    meta_title = models.CharField(_('عنوان SEO'), max_length=60, blank=True)
    meta_description = models.CharField(_('وصف SEO'), max_length=160, blank=True)

    notes = models.TextField(_('ملاحظات'), blank=True)

    class Meta:
        verbose_name = _('حدث')
        verbose_name_plural = _('الأحداث')
        ordering = ['-start_date', '-start_time']
        indexes = [
            models.Index(fields=['status', 'start_date']),
            models.Index(fields=['category', 'event_type']),
            models.Index(fields=['city']),
        ]

    def __str__(self):
        return self.title_ar

    @property
    def is_full(self):
        """Check if event is at capacity."""
        if self.max_participants:
            return self.current_participants >= self.max_participants
        return False

    @property
    def available_spots(self):
        """Calculate available spots."""
        if self.max_participants:
            return max(0, self.max_participants - self.current_participants)
        return None

    @property
    def is_registration_open(self):
        """Check if registration is still open."""
        if not self.registration_required:
            return False

        from django.utils import timezone
        now = timezone.now().date()

        if self.registration_deadline and now > self.registration_deadline:
            return False

        if now > self.start_date:
            return False

        return not self.is_full

    @property
    def current_price(self):
        """Get current price based on early bird deadline."""
        if self.is_free:
            return 0

        if self.early_bird_price and self.early_bird_deadline:
            from django.utils import timezone
            if timezone.now().date() <= self.early_bird_deadline:
                return self.early_bird_price

        return self.price


class EventStaffAssignment(TimeStampedModel):
    """Staff assignments for events."""

    ROLE_CHOICES = [
        ('coordinator', _('منسق')),
        ('host', _('مضيف')),
        ('technical', _('دعم فني')),
        ('security', _('أمن')),
        ('registration', _('تسجيل')),
        ('catering', _('ضيافة')),
        ('photographer', _('مصور')),
        ('translator', _('مترجم')),
        ('guide', _('مرشد')),
        ('driver', _('سائق')),
    ]

    event = models.ForeignKey(Event, on_delete=models.CASCADE, verbose_name=_('الحدث'))
    staff = models.ForeignKey('accounts.User', on_delete=models.CASCADE, verbose_name=_('الموظف'))
    role = models.CharField(_('الدور'), max_length=20, choices=ROLE_CHOICES)

    # Assignment details
    start_time = models.TimeField(_('وقت البداية'), null=True, blank=True)
    end_time = models.TimeField(_('وقت النهاية'), null=True, blank=True)
    hourly_rate = models.DecimalField(_('الأجر بالساعة'), max_digits=8, decimal_places=2, null=True, blank=True)

    # Status
    is_confirmed = models.BooleanField(_('مؤكد'), default=False)
    notes = models.TextField(_('ملاحظات'), blank=True)

    class Meta:
        verbose_name = _('تكليف موظف للحدث')
        verbose_name_plural = _('تكليفات الموظفين للأحداث')
        unique_together = ['event', 'staff', 'role']

    def __str__(self):
        return f"{self.event.title_ar} - {self.staff.username} ({self.get_role_display()})"


class EventRegistration(TimeStampedModel):
    """Event registrations."""

    STATUS_CHOICES = [
        ('pending', _('معلق')),
        ('confirmed', _('مؤكد')),
        ('cancelled', _('ملغي')),
        ('attended', _('حضر')),
        ('no_show', _('لم يحضر')),
    ]

    event = models.ForeignKey(
        Event,
        on_delete=models.CASCADE,
        related_name='registrations',
        verbose_name=_('الحدث')
    )

    # Participant Information
    participant_name = models.CharField(_('اسم المشارك'), max_length=100)
    participant_email = models.EmailField(_('بريد المشارك'))
    participant_phone = models.CharField(_('هاتف المشارك'), max_length=20)
    participant_organization = models.CharField(_('المؤسسة'), max_length=200, blank=True)
    participant_title = models.CharField(_('المسمى الوظيفي'), max_length=100, blank=True)

    # Client Association (if registered client)
    client = models.ForeignKey(
        'crm.Client',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('العميل')
    )

    # Registration Details
    registration_number = models.CharField(_('رقم التسجيل'), max_length=20, unique=True)
    registration_date = models.DateTimeField(_('تاريخ التسجيل'), auto_now_add=True)

    # Payment
    amount_paid = models.DecimalField(_('المبلغ المدفوع'), max_digits=10, decimal_places=2, default=0)
    payment_status = models.CharField(
        _('حالة الدفع'),
        max_length=20,
        choices=[
            ('pending', _('معلق')),
            ('paid', _('مدفوع')),
            ('partial', _('جزئي')),
            ('refunded', _('مسترد')),
        ],
        default='pending'
    )
    payment_method = models.CharField(_('طريقة الدفع'), max_length=50, blank=True)

    # Status
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='pending')

    # Additional Information
    dietary_requirements = models.TextField(_('المتطلبات الغذائية'), blank=True)
    special_needs = models.TextField(_('الاحتياجات الخاصة'), blank=True)
    comments = models.TextField(_('تعليقات'), blank=True)

    # Check-in
    checked_in = models.BooleanField(_('تم تسجيل الدخول'), default=False)
    check_in_time = models.DateTimeField(_('وقت تسجيل الدخول'), null=True, blank=True)
    checked_in_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('سجل الدخول بواسطة')
    )

    class Meta:
        verbose_name = _('تسجيل حدث')
        verbose_name_plural = _('تسجيلات الأحداث')
        ordering = ['-registration_date']
        indexes = [
            models.Index(fields=['registration_number']),
            models.Index(fields=['event', 'status']),
            models.Index(fields=['participant_email']),
        ]

    def __str__(self):
        return f"{self.registration_number} - {self.participant_name}"

    def save(self, *args, **kwargs):
        if not self.registration_number:
            # Generate registration number
            event_code = self.event.title_ar[:3].upper()
            last_reg = EventRegistration.objects.filter(
                registration_number__startswith=f"REG{event_code}"
            ).order_by('-id').first()

            if last_reg:
                last_number = int(last_reg.registration_number[-4:])
                self.registration_number = f"REG{event_code}{last_number + 1:04d}"
            else:
                self.registration_number = f"REG{event_code}0001"

        super().save(*args, **kwargs)
