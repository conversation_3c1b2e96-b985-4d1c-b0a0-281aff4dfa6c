/**
 * Handle offline form submission and sync
 */
class OfflineFormManager {
    constructor() {
        this.dbName = 'crmOfflineDB';
        this.dbVersion = 1;
        this.pendingForms = new Set();
        this.init();
    }

    async init() {
        await this.setupDB();
        this.setupEventListeners();
        window.addEventListener('online', () => this.syncPendingForms());
    }

    async setupDB() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = () => {
                console.error('Failed to open offline database');
                reject(new Error('Failed to open offline database'));
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;

                // Store for offline forms
                if (!db.objectStoreNames.contains('forms')) {
                    const store = db.createObjectStore('forms', {
                        keyPath: 'id',
                        autoIncrement: true
                    });
                    store.createIndex('formType', 'formType');
                    store.createIndex('timestamp', 'timestamp');
                    store.createIndex('status', 'status');
                }
            };

            request.onsuccess = (event) => {
                this.db = event.target.result;
                resolve();
            };
        });
    }

    setupEventListeners() {
        document.addEventListener('submit', async (event) => {
            const form = event.target;

            // Check if this is an offline-enabled form
            if (!form.hasAttribute('data-offline-form')) {
                return;
            }

            // If offline, store the form data
            if (!navigator.onLine) {
                event.preventDefault();
                await this.storeForm(form);
                toastr.info('سيتم حفظ النموذج ومزامنته عندما تعود للاتصال بالإنترنت');
                return;
            }
        });
    }

    async storeForm(form) {
        const formData = new FormData(form);
        const data = {
            formType: form.getAttribute('data-form-type'),
            timestamp: new Date().toISOString(),
            status: 'pending',
            url: form.action,
            method: form.method,
            data: Object.fromEntries(formData)
        };

        const transaction = this.db.transaction(['forms'], 'readwrite');
        const store = transaction.objectStore('forms');

        return new Promise((resolve, reject) => {
            const request = store.add(data);

            request.onsuccess = () => {
                this.pendingForms.add(request.result);
                resolve(request.result);
            };

            request.onerror = () => {
                console.error('Failed to store form data offline');
                reject(new Error('Failed to store form data offline'));
            };
        });
    }

    async syncPendingForms() {
        if (!navigator.onLine || this.pendingForms.size === 0) {
            return;
        }

        const transaction = this.db.transaction(['forms'], 'readwrite');
        const store = transaction.objectStore('forms');

        for (const formId of this.pendingForms) {
            try {
                const request = await store.get(formId);
                const formData = request.result;

                if (!formData || formData.status !== 'pending') {
                    continue;
                }

                // Try to submit the form
                const response = await fetch(formData.url, {
                    method: formData.method,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.getCsrfToken()
                    },
                    body: JSON.stringify(formData.data)
                });

                if (response.ok) {
                    // Update form status to synced
                    await store.put({
                        ...formData,
                        status: 'synced'
                    });
                    this.pendingForms.delete(formId);
                    toastr.success('تمت مزامنة البيانات بنجاح');
                } else {
                    throw new Error('Sync failed');
                }
            } catch (error) {
                console.error('Failed to sync form:', error);
                toastr.error('فشلت مزامنة بعض البيانات');
            }
        }
    }

    getCsrfToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value;
    }

    async getPendingFormsCount() {
        const transaction = this.db.transaction(['forms'], 'readonly');
        const store = transaction.objectStore('forms');
        const index = store.index('status');

        return new Promise((resolve) => {
            const request = index.count('pending');
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => resolve(0);
        });
    }
}

// Initialize offline form manager
window.offlineFormManager = new OfflineFormManager();

// Add online/offline event handlers
window.addEventListener('online', () => {
    document.body.classList.remove('offline-mode');
    toastr.success('تم استعادة الاتصال بالإنترنت');
});

window.addEventListener('offline', () => {
    document.body.classList.add('offline-mode');
    toastr.warning('أنت الآن في وضع عدم الاتصال');
});
