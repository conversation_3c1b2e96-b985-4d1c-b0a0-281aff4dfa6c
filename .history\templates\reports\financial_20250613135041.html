{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- ملخص مالي -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">الملخص المالي للعام {{ current_year }}</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="metric">
                                <h6>إجمالي الإيرادات</h6>
                                <h3>{{ yearly_summary.total_revenue|floatformat:2 }} درهم</h3>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metric">
                                <h6>عدد الحجوزات</h6>
                                <h3>{{ yearly_summary.total_bookings }}</h3>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metric">
                                <h6>متوسط قيمة الحجز</h6>
                                <h3>{{ yearly_summary.average_booking_value|floatformat:2 }} درهم</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الرسم البياني للإيرادات الشهرية -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">الإيرادات الشهرية</h5>
                    <canvas id="monthlyRevenueChart"></canvas>
                </div>
            </div>
        </div>

        <!-- تحليل طرق الدفع -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">طرق الدفع</h5>
                    <canvas id="paymentMethodsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // تهيئة الرسوم البيانية
    document.addEventListener('DOMContentLoaded', function() {
        // رسم بياني للإيرادات الشهرية
        const monthlyRevenueCtx = document.getElementById('monthlyRevenueChart').getContext('2d');
        new Chart(monthlyRevenueCtx, {
            type: 'line',
            data: {
                labels: {{ monthly_revenue|safe }}.map(item => item.month),
                datasets: [{
                    label: 'الإيرادات الشهرية',
                    data: {{ monthly_revenue|safe }}.map(item => item.total_revenue),
                    borderColor: '#4e73df',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // رسم بياني لطرق الدفع
        const paymentMethodsCtx = document.getElementById('paymentMethodsChart').getContext('2d');
        new Chart(paymentMethodsCtx, {
            type: 'doughnut',
            data: {
                labels: {{ payment_methods|safe }}.map(item => item.method),
                datasets: [{
                    data: {{ payment_methods|safe }}.map(item => item.total_amount),
                    backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e']
                }]
            }
        });
    });
</script>
{% endblock %}
