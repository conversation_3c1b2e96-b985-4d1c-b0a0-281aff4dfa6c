#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to set up initial data for Moroccan Travel Agency ERP system.
Run this after initial migration to populate the database with essential data.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'moroccan_travel_erp.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.core.models import Country, City, Currency, SystemSettings
from apps.accounts.models import Permission, Role
from apps.tours.models import TourCategory
from apps.hr.models import Department, Position, Schedule

User = get_user_model()

def create_countries_and_cities():
    """Create initial countries and cities data."""
    print("Creating countries and cities...")

    # Morocco
    morocco, created = Country.objects.get_or_create(
        code='MAR',
        defaults={
            'name_ar': 'المغرب',
            'name_fr': 'Maroc',
            'name_en': 'Morocco',
            'phone_code': '+212'
        }
    )

    # Moroccan cities
    cities_data = [
        {'name_ar': 'الرباط', 'name_fr': 'Rabat', 'name_en': 'Rabat'},
        {'name_ar': 'الدار البيضاء', 'name_fr': 'Casablanca', 'name_en': 'Casablanca'},
        {'name_ar': 'فاس', 'name_fr': 'Fès', 'name_en': 'Fez'},
        {'name_ar': 'مراكش', 'name_fr': 'Marrakech', 'name_en': 'Marrakech'},
        {'name_ar': 'أكادير', 'name_fr': 'Agadir', 'name_en': 'Agadir'},
        {'name_ar': 'طنجة', 'name_fr': 'Tanger', 'name_en': 'Tangier'},
        {'name_ar': 'مكناس', 'name_fr': 'Meknès', 'name_en': 'Meknes'},
        {'name_ar': 'وجدة', 'name_fr': 'Oujda', 'name_en': 'Oujda'},
        {'name_ar': 'تطوان', 'name_fr': 'Tétouan', 'name_en': 'Tetouan'},
        {'name_ar': 'الصويرة', 'name_fr': 'Essaouira', 'name_en': 'Essaouira'},
    ]

    for city_data in cities_data:
        City.objects.get_or_create(
            country=morocco,
            name_ar=city_data['name_ar'],
            defaults=city_data
        )

    # Other popular countries
    other_countries = [
        {'code': 'FRA', 'name_ar': 'فرنسا', 'name_fr': 'France', 'name_en': 'France', 'phone_code': '+33'},
        {'code': 'ESP', 'name_ar': 'إسبانيا', 'name_fr': 'Espagne', 'name_en': 'Spain', 'phone_code': '+34'},
        {'code': 'TUR', 'name_ar': 'تركيا', 'name_fr': 'Turquie', 'name_en': 'Turkey', 'phone_code': '+90'},
        {'code': 'EGY', 'name_ar': 'مصر', 'name_fr': 'Égypte', 'name_en': 'Egypt', 'phone_code': '+20'},
        {'code': 'SAU', 'name_ar': 'السعودية', 'name_fr': 'Arabie Saoudite', 'name_en': 'Saudi Arabia', 'phone_code': '+966'},
        {'code': 'ARE', 'name_ar': 'الإمارات', 'name_fr': 'Émirats Arabes Unis', 'name_en': 'UAE', 'phone_code': '+971'},
    ]

    for country_data in other_countries:
        Country.objects.get_or_create(
            code=country_data['code'],
            defaults=country_data
        )

def create_currencies():
    """Create initial currencies."""
    print("Creating currencies...")

    currencies_data = [
        {'code': 'MAD', 'name_ar': 'درهم مغربي', 'name_fr': 'Dirham Marocain', 'symbol': 'DH', 'is_base': True, 'exchange_rate': 1.0},
        {'code': 'EUR', 'name_ar': 'يورو', 'name_fr': 'Euro', 'symbol': '€', 'is_base': False, 'exchange_rate': 10.5},
        {'code': 'USD', 'name_ar': 'دولار أمريكي', 'name_fr': 'Dollar Américain', 'symbol': '$', 'is_base': False, 'exchange_rate': 9.8},
        {'code': 'GBP', 'name_ar': 'جنيه إسترليني', 'name_fr': 'Livre Sterling', 'symbol': '£', 'is_base': False, 'exchange_rate': 12.2},
    ]

    for currency_data in currencies_data:
        Currency.objects.get_or_create(
            code=currency_data['code'],
            defaults=currency_data
        )

def create_permissions_and_roles():
    """Create initial permissions and roles."""
    print("Creating permissions and roles...")

    # Permissions
    permissions_data = [
        # CRM Permissions
        {'codename': 'view_client', 'name': 'عرض العملاء', 'module': 'crm'},
        {'codename': 'add_client', 'name': 'إضافة عميل', 'module': 'crm'},
        {'codename': 'change_client', 'name': 'تعديل عميل', 'module': 'crm'},
        {'codename': 'delete_client', 'name': 'حذف عميل', 'module': 'crm'},

        # Tours Permissions
        {'codename': 'view_package', 'name': 'عرض الباقات', 'module': 'tours'},
        {'codename': 'add_package', 'name': 'إضافة باقة', 'module': 'tours'},
        {'codename': 'change_package', 'name': 'تعديل باقة', 'module': 'tours'},
        {'codename': 'delete_package', 'name': 'حذف باقة', 'module': 'tours'},

        # Reservations Permissions
        {'codename': 'view_reservation', 'name': 'عرض الحجوزات', 'module': 'reservations'},
        {'codename': 'add_reservation', 'name': 'إضافة حجز', 'module': 'reservations'},
        {'codename': 'change_reservation', 'name': 'تعديل حجز', 'module': 'reservations'},
        {'codename': 'cancel_reservation', 'name': 'إلغاء حجز', 'module': 'reservations'},

        # Finance Permissions
        {'codename': 'view_invoice', 'name': 'عرض الفواتير', 'module': 'finance'},
        {'codename': 'add_invoice', 'name': 'إضافة فاتورة', 'module': 'finance'},
        {'codename': 'approve_payment', 'name': 'الموافقة على الدفعات', 'module': 'finance'},
        {'codename': 'view_financial_reports', 'name': 'عرض التقارير المالية', 'module': 'finance'},

        # HR Permissions
        {'codename': 'view_employee', 'name': 'عرض الموظفين', 'module': 'hr'},
        {'codename': 'manage_payroll', 'name': 'إدارة الرواتب', 'module': 'hr'},
        {'codename': 'approve_leave', 'name': 'الموافقة على الإجازات', 'module': 'hr'},

        # Reports Permissions
        {'codename': 'view_reports', 'name': 'عرض التقارير', 'module': 'reports'},
        {'codename': 'export_data', 'name': 'تصدير البيانات', 'module': 'reports'},

        # Admin Permissions
        {'codename': 'manage_users', 'name': 'إدارة المستخدمين', 'module': 'admin'},
        {'codename': 'manage_system', 'name': 'إدارة النظام', 'module': 'admin'},
    ]

    for perm_data in permissions_data:
        Permission.objects.get_or_create(
            codename=perm_data['codename'],
            defaults=perm_data
        )

    # Roles
    roles_data = [
        {
            'name': 'مدير النظام',
            'description': 'صلاحيات كاملة لإدارة النظام',
            'permissions': ['manage_users', 'manage_system', 'view_reports', 'export_data']
        },
        {
            'name': 'مدير الوكالة',
            'description': 'إدارة العمليات اليومية للوكالة',
            'permissions': ['view_client', 'add_client', 'change_client', 'view_package', 'add_package', 'change_package', 'view_reservation', 'add_reservation', 'change_reservation', 'view_invoice', 'view_reports']
        },
        {
            'name': 'موظف مبيعات',
            'description': 'إدارة العملاء والحجوزات',
            'permissions': ['view_client', 'add_client', 'change_client', 'view_package', 'view_reservation', 'add_reservation', 'change_reservation']
        },
        {
            'name': 'محاسب',
            'description': 'إدارة الشؤون المالية',
            'permissions': ['view_invoice', 'add_invoice', 'approve_payment', 'view_financial_reports', 'view_reports']
        },
        {
            'name': 'مرشد سياحي',
            'description': 'عرض الحجوزات والباقات',
            'permissions': ['view_package', 'view_reservation']
        }
    ]

    for role_data in roles_data:
        role, created = Role.objects.get_or_create(
            name=role_data['name'],
            defaults={'description': role_data['description']}
        )

        if created:
            # Add permissions to role
            permissions = Permission.objects.filter(codename__in=role_data['permissions'])
            role.permissions.set(permissions)

def create_tour_categories():
    """Create initial tour categories."""
    print("Creating tour categories...")

    categories_data = [
        {'name_ar': 'سياحة ثقافية', 'name_fr': 'Tourisme Culturel', 'name_en': 'Cultural Tourism', 'icon': 'fas fa-mosque', 'color': '#8B4513'},
        {'name_ar': 'سياحة الشواطئ', 'name_fr': 'Tourisme Balnéaire', 'name_en': 'Beach Tourism', 'icon': 'fas fa-umbrella-beach', 'color': '#1E90FF'},
        {'name_ar': 'سياحة المغامرات', 'name_fr': 'Tourisme d\'Aventure', 'name_en': 'Adventure Tourism', 'icon': 'fas fa-mountain', 'color': '#228B22'},
        {'name_ar': 'سياحة الصحراء', 'name_fr': 'Tourisme Saharien', 'name_en': 'Desert Tourism', 'icon': 'fas fa-sun', 'color': '#DAA520'},
        {'name_ar': 'سياحة دينية', 'name_fr': 'Tourisme Religieux', 'name_en': 'Religious Tourism', 'icon': 'fas fa-pray', 'color': '#4B0082'},
        {'name_ar': 'سياحة العافية', 'name_fr': 'Tourisme de Bien-être', 'name_en': 'Wellness Tourism', 'icon': 'fas fa-spa', 'color': '#FF69B4'},
        {'name_ar': 'سياحة الأعمال', 'name_fr': 'Tourisme d\'Affaires', 'name_en': 'Business Tourism', 'icon': 'fas fa-briefcase', 'color': '#2F4F4F'},
        {'name_ar': 'سياحة عائلية', 'name_fr': 'Tourisme Familial', 'name_en': 'Family Tourism', 'icon': 'fas fa-family', 'color': '#FF6347'},
    ]

    for i, category_data in enumerate(categories_data):
        TourCategory.objects.get_or_create(
            name_ar=category_data['name_ar'],
            defaults={**category_data, 'sort_order': i * 10}
        )

def create_hr_structure():
    """Create initial HR structure."""
    print("Creating HR structure...")

    # Departments
    departments_data = [
        {'name_ar': 'الإدارة العامة', 'name_fr': 'Direction Générale'},
        {'name_ar': 'المبيعات والتسويق', 'name_fr': 'Ventes et Marketing'},
        {'name_ar': 'العمليات السياحية', 'name_fr': 'Opérations Touristiques'},
        {'name_ar': 'المحاسبة والمالية', 'name_fr': 'Comptabilité et Finance'},
        {'name_ar': 'الموارد البشرية', 'name_fr': 'Ressources Humaines'},
        {'name_ar': 'تقنية المعلومات', 'name_fr': 'Technologies de l\'Information'},
    ]

    for dept_data in departments_data:
        Department.objects.get_or_create(
            name_ar=dept_data['name_ar'],
            defaults=dept_data
        )

    # Positions
    positions_data = [
        {'title_ar': 'مدير عام', 'title_fr': 'Directeur Général', 'department': 'الإدارة العامة'},
        {'title_ar': 'مدير المبيعات', 'title_fr': 'Directeur des Ventes', 'department': 'المبيعات والتسويق'},
        {'title_ar': 'موظف مبيعات', 'title_fr': 'Agent de Ventes', 'department': 'المبيعات والتسويق'},
        {'title_ar': 'مرشد سياحي', 'title_fr': 'Guide Touristique', 'department': 'العمليات السياحية'},
        {'title_ar': 'محاسب', 'title_fr': 'Comptable', 'department': 'المحاسبة والمالية'},
        {'title_ar': 'مطور نظم', 'title_fr': 'Développeur', 'department': 'تقنية المعلومات'},
    ]

    for pos_data in positions_data:
        department = Department.objects.get(name_ar=pos_data['department'])
        Position.objects.get_or_create(
            title_ar=pos_data['title_ar'],
            department=department,
            defaults={'title_fr': pos_data['title_fr']}
        )

    # Default Schedule
    Schedule.objects.get_or_create(
        name='جدول العمل الافتراضي',
        defaults={
            'description': 'جدول العمل الافتراضي من الاثنين إلى الجمعة',
            'monday_start': '09:00',
            'monday_end': '17:00',
            'tuesday_start': '09:00',
            'tuesday_end': '17:00',
            'wednesday_start': '09:00',
            'wednesday_end': '17:00',
            'thursday_start': '09:00',
            'thursday_end': '17:00',
            'friday_start': '09:00',
            'friday_end': '17:00',
            'is_default': True,
            'break_duration': 60
        }
    )

def create_system_settings():
    """Create initial system settings."""
    print("Creating system settings...")

    settings_data = [
        {'key': 'company_name_ar', 'value': 'وكالة السفر المغربية', 'description': 'اسم الشركة بالعربية'},
        {'key': 'company_name_fr', 'value': 'Agence de Voyage Marocaine', 'description': 'اسم الشركة بالفرنسية'},
        {'key': 'company_address', 'value': 'الرباط، المغرب', 'description': 'عنوان الشركة'},
        {'key': 'company_phone', 'value': '+212 5 37 XX XX XX', 'description': 'هاتف الشركة'},
        {'key': 'company_email', 'value': '<EMAIL>', 'description': 'بريد الشركة الإلكتروني'},
        {'key': 'default_currency', 'value': 'MAD', 'description': 'العملة الافتراضية'},
        {'key': 'tax_rate', 'value': '20', 'description': 'معدل الضريبة (%)'},
        {'key': 'booking_confirmation_auto', 'value': 'true', 'description': 'تأكيد الحجز التلقائي'},
        {'key': 'invoice_terms', 'value': 'الدفع خلال 30 يوم من تاريخ الفاتورة', 'description': 'شروط الدفع الافتراضية'},
        {'key': 'backup_enabled', 'value': 'true', 'description': 'تفعيل النسخ الاحتياطي'},
    ]

    for setting_data in settings_data:
        SystemSettings.objects.get_or_create(
            key=setting_data['key'],
            defaults=setting_data
        )

def create_admin_user():
    """Create default admin user if it doesn't exist."""
    print("Creating admin user...")

    if not User.objects.filter(username='admin').exists():
        admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            first_name_ar='المدير',
            last_name_ar='العام',
            role='admin'
        )
        print(f"Admin user created: {admin_user.username}")
    else:
        print("Admin user already exists")

def main():
    """Main function to run all setup tasks."""
    print("Setting up initial data for Moroccan Travel Agency ERP...")
    print("=" * 60)

    try:
        create_countries_and_cities()
        create_currencies()
        create_permissions_and_roles()
        create_tour_categories()
        create_hr_structure()
        create_system_settings()
        create_admin_user()

        print("=" * 60)
        print("✅ Initial data setup completed successfully!")
        print("\nDefault admin credentials:")
        print("Username: admin")
        print("Password: admin123")
        print("\n⚠️  Please change the admin password after first login!")

    except Exception as e:
        print(f"❌ Error during setup: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
