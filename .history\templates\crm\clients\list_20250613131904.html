{% extends 'base.html' %}
{% load i18n static %}

{% block title %}{% trans "العملاء" %}{% endblock %}

{% block content %}
<div class="container-fluid" id="client-app">
  <!-- Header -->
  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{% trans "قائمة العملاء" %}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
      <button type="button" @click="showAddModal" class="btn btn-sm btn-primary">
        <i class="fas fa-plus"></i> {% trans "إضافة عميل" %}
      </button>
    </div>
  </div>

  <!-- Search & Filters -->
  <div class="row mb-3">
    <div class="col-md-4">
      <div class="input-group">
        <span class="input-group-text"><i class="fas fa-search"></i></span>
        <input type="text" v-model="search" class="form-control" placeholder="{% trans 'بحث...' %}">
      </div>
    </div>
    <div class="col-md-8">
      <div class="btn-group float-end" role="group">
        <button type="button" class="btn btn-outline-secondary" :class="{ active: filter === 'all' }" @click="filter = 'all'">
          {% trans "الكل" %}
        </button>
        <button type="button" class="btn btn-outline-secondary" :class="{ active: filter === 'active' }" @click="filter = 'active'">
          {% trans "نشط" %}
        </button>
        <button type="button" class="btn btn-outline-secondary" :class="{ active: filter === 'vip' }" @click="filter = 'vip'">
          VIP
        </button>
      </div>
    </div>
  </div>

  <!-- Clients Table -->
  <div class="table-responsive">
    <table class="table table-striped table-hover">
      <thead>
        <tr>
          <th>{% trans "الاسم" %}</th>
          <th>{% trans "رقم الهاتف" %}</th>
          <th>{% trans "البريد الإلكتروني" %}</th>
          <th>{% trans "نقاط الولاء" %}</th>
          <th>{% trans "آخر نشاط" %}</th>
          <th>{% trans "الإجراءات" %}</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="client in filteredClients" :key="client.id">
          <td>[[ client.full_name_ar ]]</td>
          <td>[[ client.phone ]]</td>
          <td>[[ client.email ]]</td>
          <td>
            <span class="badge bg-info">[[ client.loyalty_points ]]</span>
          </td>
          <td>[[ formatDate(client.last_activity) ]]</td>
          <td>
            <div class="btn-group">
              <button @click="showEditModal(client)" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-edit"></i>
              </button>
              <button @click="showViewModal(client)" class="btn btn-sm btn-outline-info">
                <i class="fas fa-eye"></i>
              </button>
              <button @click="confirmDelete(client)" class="btn btn-sm btn-outline-danger">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Add/Edit Modal -->
  <div class="modal fade" id="clientModal" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">[[ modalTitle ]]</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="saveClient">
            <div class="mb-3">
              <label class="form-label">{% trans "الاسم بالعربية" %}</label>
              <input type="text" v-model="currentClient.full_name_ar" class="form-control" required>
            </div>
            <div class="mb-3">
              <label class="form-label">{% trans "الاسم بالفرنسية" %}</label>
              <input type="text" v-model="currentClient.full_name_fr" class="form-control">
            </div>
            <div class="mb-3">
              <label class="form-label">{% trans "رقم الهاتف" %}</label>
              <input type="tel" v-model="currentClient.phone" class="form-control" required>
            </div>
            <div class="mb-3">
              <label class="form-label">{% trans "البريد الإلكتروني" %}</label>
              <input type="email" v-model="currentClient.email" class="form-control">
            </div>
            <div class="mb-3">
              <label class="form-label">{% trans "رقم جواز السفر" %}</label>
              <input type="text" v-model="currentClient.passport_number" class="form-control">
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
              <button type="submit" class="btn btn-primary">[[ modalAction ]]</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- View Modal -->
  <div class="modal fade" id="viewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">{% trans "تفاصيل العميل" %}</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-6">
              <h6>{% trans "المعلومات الشخصية" %}</h6>
              <hr>
              <p><strong>{% trans "الاسم بالعربية" %}:</strong> [[ selectedClient.full_name_ar ]]</p>
              <p><strong>{% trans "الاسم بالفرنسية" %}:</strong> [[ selectedClient.full_name_fr ]]</p>
              <p><strong>{% trans "رقم الهاتف" %}:</strong> [[ selectedClient.phone ]]</p>
              <p><strong>{% trans "البريد الإلكتروني" %}:</strong> [[ selectedClient.email ]]</p>
            </div>
            <div class="col-md-6">
              <h6>{% trans "إحصائيات" %}</h6>
              <hr>
              <p><strong>{% trans "نقاط الولاء" %}:</strong> [[ selectedClient.loyalty_points ]]</p>
              <p><strong>{% trans "عدد الرحلات" %}:</strong> [[ selectedClient.trips_count ]]</p>
              <p><strong>{% trans "تاريخ التسجيل" %}:</strong> [[ formatDate(selectedClient.created_at) ]]</p>
            </div>
          </div>
          <div class="row mt-3">
            <div class="col-12">
              <h6>{% trans "آخر الرحلات" %}</h6>
              <hr>
              <div class="table-responsive">
                <table class="table table-sm">
                  <thead>
                    <tr>
                      <th>{% trans "الباقة" %}</th>
                      <th>{% trans "التاريخ" %}</th>
                      <th>{% trans "الحالة" %}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="trip in selectedClient.recent_trips" :key="trip.id">
                      <td>[[ trip.package_name ]]</td>
                      <td>[[ formatDate(trip.date) ]]</td>
                      <td>
                        <span :class="getStatusBadgeClass(trip.status)">[[ trip.status ]]</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
const app = Vue.createApp({
  delimiters: ['[[', ']]'],
  data() {
    return {
      clients: [],
      search: '',
      filter: 'all',
      currentClient: {},
      selectedClient: {},
      modalTitle: '',
      modalAction: ''
    }
  },
  computed: {
    filteredClients() {
      return this.clients.filter(client => {
        const matchesSearch = client.full_name_ar.toLowerCase().includes(this.search.toLowerCase()) ||
                            client.phone.includes(this.search) ||
                            client.email.toLowerCase().includes(this.search.toLowerCase())

        if (this.filter === 'all') return matchesSearch
        if (this.filter === 'active') return matchesSearch && client.is_active
        if (this.filter === 'vip') return matchesSearch && client.loyalty_points >= 1000

        return matchesSearch
      })
    }
  },
  methods: {
    async fetchClients() {
      try {
        const response = await axios.get('/api/clients/')
        this.clients = response.data
      } catch (error) {
        console.error('Error fetching clients:', error)
        // Show error notification
      }
    },
    showAddModal() {
      this.currentClient = {}
      this.modalTitle = '{% trans "إضافة عميل جديد" %}'
      this.modalAction = '{% trans "إضافة" %}'
      new bootstrap.Modal(document.getElementById('clientModal')).show()
    },
    showEditModal(client) {
      this.currentClient = { ...client }
      this.modalTitle = '{% trans "تعديل بيانات العميل" %}'
      this.modalAction = '{% trans "حفظ" %}'
      new bootstrap.Modal(document.getElementById('clientModal')).show()
    },
    showViewModal(client) {
      this.selectedClient = { ...client }
      new bootstrap.Modal(document.getElementById('viewModal')).show()
    },
    async saveClient() {
      try {
        if (this.currentClient.id) {
          await axios.put(`/api/clients/${this.currentClient.id}/`, this.currentClient)
        } else {
          await axios.post('/api/clients/', this.currentClient)
        }
        await this.fetchClients()
        bootstrap.Modal.getInstance(document.getElementById('clientModal')).hide()
        // Show success notification
      } catch (error) {
        console.error('Error saving client:', error)
        // Show error notification
      }
    },
    async confirmDelete(client) {
      if (confirm('{% trans "هل أنت متأكد من حذف هذا العميل؟" %}')) {
        try {
          await axios.delete(`/api/clients/${client.id}/`)
          await this.fetchClients()
          // Show success notification
        } catch (error) {
          console.error('Error deleting client:', error)
          // Show error notification
        }
      }
    },
    formatDate(date) {
      return new Date(date).toLocaleDateString('ar-MA')
    },
    getStatusBadgeClass(status) {
      return {
        'pending': 'badge bg-warning',
        'confirmed': 'badge bg-success',
        'cancelled': 'badge bg-danger'
      }[status] || 'badge bg-secondary'
    }
  },
  mounted() {
    this.fetchClients()
  }
})

app.mount('#client-app')
</script>
{% endblock %}
