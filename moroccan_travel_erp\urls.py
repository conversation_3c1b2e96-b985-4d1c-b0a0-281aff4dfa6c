"""
Simplified URL configuration for Moroccan Travel Agency ERP project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

from apps.core.views import QuickStatsView, SystemSettingsView

# Error handlers - using uppercase for constants
HANDLER_404 = 'apps.core.error_handlers.handler404'
HANDLER_500 = 'apps.core.error_handlers.handler500'
HANDLER_403 = 'apps.core.error_handlers.handler403'
HANDLER_400 = 'apps.core.error_handlers.handler400'

handler404 = HANDLER_404
handler500 = HANDLER_500
handler403 = HANDLER_403
handler400 = HANDLER_400

urlpatterns = [
    # Admin
    path('admin/', admin.site.urls),

    # Authentication
    path('accounts/', include('apps.accounts.urls')),

    # Core app with namespace
    path('', include('apps.core.urls', namespace='core')),

    # Quick stats with proper import
    path('stats/', QuickStatsView.as_view(), name='quick_stats'),

    # CRM
    path('crm/', include('apps.crm.urls')),

    # Tours
    path('tours/', include('apps.tours.urls')),

    # Reports
    path('reports/', include('apps.reports.urls')),

    # Finance
    path('finance/', include('apps.finance.urls')),

    # Reservations
    path('reservations/', include('apps.reservations.urls')),

    # HR
    path('hr/', include('apps.hr.urls')),

    # Suppliers
    path('suppliers/', include('apps.suppliers.urls')),

    # Settings with proper import
    path('settings/', SystemSettingsView.as_view(), name='system_settings'),

    # API URLs
    path('api/finance/', include('apps.finance.api_urls')),

    # API
    path('api/', include('api_urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(
        settings.MEDIA_URL,
        document_root=settings.MEDIA_ROOT
    )
    urlpatterns += static(
        settings.STATIC_URL,
        document_root=settings.STATIC_ROOT
    )
