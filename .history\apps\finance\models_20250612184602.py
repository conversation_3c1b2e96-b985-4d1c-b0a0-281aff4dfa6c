"""
Finance models for managing invoices, payments, and financial transactions.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from djmoney.models.fields import MoneyField
from apps.core.models import AuditModel


class Invoice(AuditModel):
    """Invoice model for billing clients."""
    
    STATUS_CHOICES = [
        ('draft', _('مسودة')),
        ('sent', _('مرسلة')),
        ('paid', _('مدفوعة')),
        ('overdue', _('متأخرة')),
        ('cancelled', _('ملغية')),
    ]
    
    # Basic Information
    invoice_number = models.CharField(_('رقم الفاتورة'), max_length=20, unique=True)
    reservation = models.ForeignKey('reservations.Reservation', on_delete=models.CASCADE, verbose_name=_('الحجز'))
    client = models.ForeignKey('crm.Client', on_delete=models.CASCADE, verbose_name=_('العميل'))
    
    # Dates
    issue_date = models.DateField(_('تاريخ الإصدار'))
    due_date = models.DateField(_('تاريخ الاستحقاق'))
    
    # Amounts
    subtotal = MoneyField(_('المجموع الفرعي'), max_digits=10, decimal_places=2, default_currency='MAD')
    tax_rate = models.DecimalField(_('معدل الضريبة'), max_digits=5, decimal_places=2, default=20.00)
    tax_amount = MoneyField(_('مبلغ الضريبة'), max_digits=10, decimal_places=2, default_currency='MAD')
    discount_amount = MoneyField(_('مبلغ الخصم'), max_digits=10, decimal_places=2, default_currency='MAD', default=0)
    total_amount = MoneyField(_('المبلغ الإجمالي'), max_digits=10, decimal_places=2, default_currency='MAD')
    
    # Status
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='draft')
    
    # Notes
    notes = models.TextField(_('ملاحظات'), blank=True)
    terms_and_conditions = models.TextField(_('الشروط والأحكام'), blank=True)
    
    # Internal
    created_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, verbose_name=_('أنشئ بواسطة'))
    
    class Meta:
        verbose_name = _('فاتورة')
        verbose_name_plural = _('الفواتير')
        ordering = ['-issue_date']
        indexes = [
            models.Index(fields=['invoice_number']),
            models.Index(fields=['client', 'status']),
            models.Index(fields=['due_date']),
        ]
    
    def __str__(self):
        return f"{self.invoice_number} - {self.client.full_name_ar}"
    
    @property
    def paid_amount(self):
        """Calculate total paid amount."""
        return sum(payment.amount.amount for payment in self.payments.filter(status='completed'))
    
    @property
    def remaining_amount(self):
        """Calculate remaining amount."""
        return self.total_amount.amount - self.paid_amount
    
    @property
    def is_overdue(self):
        """Check if invoice is overdue."""
        from django.utils import timezone
        return self.due_date < timezone.now().date() and self.status != 'paid'
    
    def save(self, *args, **kwargs):
        if not self.invoice_number:
            # Generate invoice number
            last_invoice = Invoice.objects.filter(invoice_number__startswith='INV').order_by('-id').first()
            if last_invoice:
                last_number = int(last_invoice.invoice_number[3:])
                self.invoice_number = f"INV{last_number + 1:06d}"
            else:
                self.invoice_number = "INV000001"
        
        # Calculate tax and total
        self.tax_amount = self.subtotal * (self.tax_rate / 100)
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount
        
        super().save(*args, **kwargs)


class InvoiceItem(AuditModel):
    """Individual items in an invoice."""
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='items', verbose_name=_('الفاتورة'))
    
    description = models.CharField(_('الوصف'), max_length=200)
    quantity = models.PositiveIntegerField(_('الكمية'), default=1)
    unit_price = MoneyField(_('سعر الوحدة'), max_digits=10, decimal_places=2, default_currency='MAD')
    total_price = MoneyField(_('السعر الإجمالي'), max_digits=10, decimal_places=2, default_currency='MAD')
    
    class Meta:
        verbose_name = _('عنصر الفاتورة')
        verbose_name_plural = _('عناصر الفاتورة')
        ordering = ['id']
    
    def __str__(self):
        return f"{self.description} - {self.invoice.invoice_number}"
    
    def save(self, *args, **kwargs):
        self.total_price = self.unit_price * self.quantity
        super().save(*args, **kwargs)


class Payment(AuditModel):
    """Payment model for tracking payments."""
    
    PAYMENT_METHOD_CHOICES = [
        ('cash', _('نقدي')),
        ('card', _('بطاقة')),
        ('bank_transfer', _('تحويل بنكي')),
        ('check', _('شيك')),
        ('online', _('دفع إلكتروني')),
    ]
    
    STATUS_CHOICES = [
        ('pending', _('في الانتظار')),
        ('completed', _('مكتمل')),
        ('failed', _('فاشل')),
        ('cancelled', _('ملغي')),
        ('refunded', _('مسترد')),
    ]
    
    # Basic Information
    payment_number = models.CharField(_('رقم الدفعة'), max_length=20, unique=True)
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='payments', verbose_name=_('الفاتورة'))
    reservation = models.ForeignKey('reservations.Reservation', on_delete=models.CASCADE, related_name='payments', verbose_name=_('الحجز'))
    
    # Payment Details
    amount = MoneyField(_('المبلغ'), max_digits=10, decimal_places=2, default_currency='MAD')
    payment_method = models.CharField(_('طريقة الدفع'), max_length=20, choices=PAYMENT_METHOD_CHOICES)
    payment_date = models.DateTimeField(_('تاريخ الدفع'))
    
    # Status
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # Transaction Details
    transaction_id = models.CharField(_('رقم المعاملة'), max_length=100, blank=True)
    reference_number = models.CharField(_('الرقم المرجعي'), max_length=100, blank=True)
    
    # Bank/Card Details
    bank_name = models.CharField(_('اسم البنك'), max_length=100, blank=True)
    card_last_four = models.CharField(_('آخر أربعة أرقام من البطاقة'), max_length=4, blank=True)
    
    # Notes
    notes = models.TextField(_('ملاحظات'), blank=True)
    
    # Internal
    processed_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, verbose_name=_('تم المعالجة بواسطة'))
    
    class Meta:
        verbose_name = _('دفعة')
        verbose_name_plural = _('الدفعات')
        ordering = ['-payment_date']
        indexes = [
            models.Index(fields=['payment_number']),
            models.Index(fields=['invoice', 'status']),
            models.Index(fields=['payment_date']),
        ]
    
    def __str__(self):
        return f"{self.payment_number} - {self.amount}"
    
    def save(self, *args, **kwargs):
        if not self.payment_number:
            # Generate payment number
            last_payment = Payment.objects.filter(payment_number__startswith='PAY').order_by('-id').first()
            if last_payment:
                last_number = int(last_payment.payment_number[3:])
                self.payment_number = f"PAY{last_number + 1:06d}"
            else:
                self.payment_number = "PAY000001"
        
        super().save(*args, **kwargs)


class Expense(AuditModel):
    """Expense model for tracking business expenses."""
    
    CATEGORY_CHOICES = [
        ('transport', _('نقل')),
        ('accommodation', _('إقامة')),
        ('meals', _('وجبات')),
        ('fuel', _('وقود')),
        ('maintenance', _('صيانة')),
        ('marketing', _('تسويق')),
        ('office', _('مكتب')),
        ('utilities', _('مرافق')),
        ('insurance', _('تأمين')),
        ('taxes', _('ضرائب')),
        ('salaries', _('رواتب')),
        ('other', _('أخرى')),
    ]
    
    STATUS_CHOICES = [
        ('draft', _('مسودة')),
        ('pending', _('في الانتظار')),
        ('approved', _('موافق عليه')),
        ('rejected', _('مرفوض')),
        ('paid', _('مدفوع')),
    ]
    
    # Basic Information
    expense_number = models.CharField(_('رقم المصروف'), max_length=20, unique=True)
    title = models.CharField(_('العنوان'), max_length=200)
    description = models.TextField(_('الوصف'), blank=True)
    category = models.CharField(_('الفئة'), max_length=20, choices=CATEGORY_CHOICES)
    
    # Amount
    amount = MoneyField(_('المبلغ'), max_digits=10, decimal_places=2, default_currency='MAD')
    tax_amount = MoneyField(_('مبلغ الضريبة'), max_digits=10, decimal_places=2, default_currency='MAD', default=0)
    
    # Dates
    expense_date = models.DateField(_('تاريخ المصروف'))
    
    # Status
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='draft')
    
    # Vendor
    vendor = models.ForeignKey('suppliers.Supplier', on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_('المورد'))
    vendor_name = models.CharField(_('اسم المورد'), max_length=100, blank=True)
    
    # Receipt
    receipt = models.FileField(_('الإيصال'), upload_to='expenses/receipts/', blank=True)
    
    # Internal
    submitted_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, related_name='submitted_expenses', verbose_name=_('قدم بواسطة'))
    approved_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_expenses', verbose_name=_('وافق عليه'))
    approval_date = models.DateTimeField(_('تاريخ الموافقة'), null=True, blank=True)
    
    class Meta:
        verbose_name = _('مصروف')
        verbose_name_plural = _('المصروفات')
        ordering = ['-expense_date']
        indexes = [
            models.Index(fields=['expense_number']),
            models.Index(fields=['category', 'status']),
            models.Index(fields=['expense_date']),
        ]
    
    def __str__(self):
        return f"{self.expense_number} - {self.title}"
    
    @property
    def total_amount(self):
        """Calculate total amount including tax."""
        return self.amount + self.tax_amount
    
    def save(self, *args, **kwargs):
        if not self.expense_number:
            # Generate expense number
            last_expense = Expense.objects.filter(expense_number__startswith='EXP').order_by('-id').first()
            if last_expense:
                last_number = int(last_expense.expense_number[3:])
                self.expense_number = f"EXP{last_number + 1:06d}"
            else:
                self.expense_number = "EXP000001"
        
        super().save(*args, **kwargs)


class BankAccount(AuditModel):
    """Bank account model for managing company bank accounts."""
    account_name = models.CharField(_('اسم الحساب'), max_length=100)
    bank_name = models.CharField(_('اسم البنك'), max_length=100)
    account_number = models.CharField(_('رقم الحساب'), max_length=50)
    iban = models.CharField(_('IBAN'), max_length=34, blank=True)
    swift_code = models.CharField(_('رمز SWIFT'), max_length=11, blank=True)
    
    # Balance
    current_balance = MoneyField(_('الرصيد الحالي'), max_digits=15, decimal_places=2, default_currency='MAD', default=0)
    
    # Status
    is_active = models.BooleanField(_('نشط'), default=True)
    is_default = models.BooleanField(_('افتراضي'), default=False)
    
    # Contact Information
    branch_name = models.CharField(_('اسم الفرع'), max_length=100, blank=True)
    contact_person = models.CharField(_('الشخص المسؤول'), max_length=100, blank=True)
    phone = models.CharField(_('الهاتف'), max_length=20, blank=True)
    
    class Meta:
        verbose_name = _('حساب بنكي')
        verbose_name_plural = _('الحسابات البنكية')
        ordering = ['bank_name', 'account_name']
    
    def __str__(self):
        return f"{self.bank_name} - {self.account_name}"
