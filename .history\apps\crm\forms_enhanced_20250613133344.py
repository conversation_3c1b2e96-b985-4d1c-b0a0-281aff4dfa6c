"""
Enhanced forms for CRM module with modern UI components.
"""
from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from django.utils import timezone
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Div, Row, Column, Field, HTML, Submit, Button
from crispy_forms.bootstrap import <PERSON>b<PERSON><PERSON><PERSON>, Tab, <PERSON><PERSON>, Modal
from .models import Client


class EnhancedClientForm(forms.ModelForm):
    """Enhanced client form with modern UI and tabs."""

    # Additional fields
    confirm_email = forms.EmailField(
        label=_('تأكيد البريد الإلكتروني'),
        required=False,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': _('أعد إدخال البريد الإلكتروني للتأكيد')
        })
    )

    # Meta configuration
    class Meta:
        model = Client
        fields = [
            'first_name_ar', 'last_name_ar', 'first_name_fr', 'last_name_fr',
            'email', 'phone', 'whatsapp', 'secondary_phone',
            'gender', 'date_of_birth', 'nationality',
            'passport_number', 'passport_expiry', 'national_id',
            'address', 'city', 'postal_code',
            'client_type', 'company_name', 'tax_number',
            'preferred_language', 'special_requirements', 'dietary_restrictions',
            'vip_status', 'marketing_consent', 'newsletter_subscription',
            'notes', 'assigned_agent'
        ]
        widgets = {
            'first_name_ar': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('أدخل الاسم الأول بالعربية'),
                'dir': 'rtl'
            }),
            'last_name_ar': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('أدخل اسم العائلة بالعربية'),
                'dir': 'rtl'
            }),
            'first_name_fr': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('Prénom en français'),
                'dir': 'ltr'
            }),
            'last_name_fr': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('Nom de famille en français'),
                'dir': 'ltr'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': _('<EMAIL>')
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('رقم الهاتف'),
                'data-mask': '+212-000-000-000'
            }),
            'whatsapp': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('رقم الواتساب'),
                'data-mask': '+212-000-000-000'
            }),
            'date_of_birth': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'passport_expiry': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': _('العنوان الكامل')
            }),
            'special_requirements': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': _('أي متطلبات خاصة للعميل')
            }),
            'dietary_restrictions': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': _('قيود غذائية أو حساسية')
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': _('ملاحظات إضافية')
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        self.helper.attrs = {
            'novalidate': '',
            'data-offline-form': 'true',
            'data-form-type': 'client'
        }

        # Required fields
        required_fields = [
            'first_name_ar', 'last_name_ar', 'email',
            'phone', 'nationality', 'client_type'
        ]
        for field in required_fields:
            self.fields[field].required = True

        # Add data-mask attributes for phone fields
        phone_fields = ['phone', 'whatsapp', 'secondary_phone']
        for field in phone_fields:
            self.fields[field].widget.attrs.update({
                'data-mask': '+212-000-000-000',
                'pattern': r'^\+212-[0-9]{3}-[0-9]{3}-[0-9]{3}$'
            })

        # Layout configuration
        self.helper.layout = Layout(
            # Alert for form instructions
            Alert(
                content=_('يرجى ملء جميع الحقول المطلوبة بعناية. الحقول المميزة بـ * إجبارية.'),
                css_class='alert-info mb-4'
            ),

            # Tab holder for organized sections
            TabHolder(
                Tab(
                    _('المعلومات الأساسية'),
                    Row(
                        Column('first_name_ar', css_class='form-group col-md-6 mb-3'),
                        Column('last_name_ar', css_class='form-group col-md-6 mb-3'),
                        css_class='form-row'
                    ),
                    Row(
                        Column('first_name_fr', css_class='form-group col-md-6 mb-3'),
                        Column('last_name_fr', css_class='form-group col-md-6 mb-3'),
                        css_class='form-row'
                    ),
                    Row(
                        Column('email', css_class='form-group col-md-6 mb-3'),
                        Column('preferred_language', css_class='form-group col-md-6 mb-3'),
                        css_class='form-row'
                    ),
                    Row(
                        Column('gender', css_class='form-group col-md-4 mb-3'),
                        Column('date_of_birth', css_class='form-group col-md-4 mb-3'),
                        Column('nationality', css_class='form-group col-md-4 mb-3'),
                        css_class='form-row'
                    ),
                    css_class='tab-pane fade show active'
                ),

                Tab(
                    _('معلومات الاتصال'),
                    Row(
                        Column('phone', css_class='form-group col-md-6 mb-3'),
                        Column('whatsapp', css_class='form-group col-md-6 mb-3'),
                        css_class='form-row'
                    ),
                    Row(
                        Column('secondary_phone', css_class='form-group col-md-6 mb-3'),
                        Column('postal_code', css_class='form-group col-md-6 mb-3'),
                        css_class='form-row'
                    ),
                    Row(
                        Column('address', css_class='form-group col-md-8 mb-3'),
                        Column('city', css_class='form-group col-md-4 mb-3'),
                        css_class='form-row'
                    ),
                ),

                Tab(
                    _('الوثائق والهوية'),
                    Row(
                        Column('passport_number', css_class='form-group col-md-6 mb-3'),
                        Column('passport_expiry', css_class='form-group col-md-6 mb-3'),
                        css_class='form-row'
                    ),
                    Row(
                        Column('national_id', css_class='form-group col-md-6 mb-3'),
                        css_class='form-row'
                    ),
                ),

                Tab(
                    _('معلومات العمل'),
                    Row(
                        Column('client_type', css_class='form-group col-md-6 mb-3'),
                        Column('assigned_agent', css_class='form-group col-md-6 mb-3'),
                        css_class='form-row'
                    ),
                    Row(
                        Column('company_name', css_class='form-group col-md-6 mb-3'),
                        Column('tax_number', css_class='form-group col-md-6 mb-3'),
                        css_class='form-row'
                    ),
                ),

                Tab(
                    _('التفضيلات والملاحظات'),
                    'special_requirements',
                    'dietary_restrictions',
                    Row(
                        Column(
                            Field('vip_status', css_class='form-check-input'),
                            css_class='form-group col-md-4 mb-3'
                        ),
                        Column(
                            Field('marketing_consent', css_class='form-check-input'),
                            css_class='form-group col-md-4 mb-3'
                        ),
                        Column(
                            Field('newsletter_subscription', css_class='form-check-input'),
                            css_class='form-group col-md-4 mb-3'
                        ),
                        css_class='form-row'
                    ),
                    'notes',
                ),
            ),

            # Action buttons
            Div(
                Submit('submit', _('حفظ العميل'), css_class='btn btn-primary btn-lg me-2'),
                Button('cancel', _('إلغاء'), css_class='btn btn-secondary btn-lg', onclick='history.back()'),
                css_class='d-grid gap-2 d-md-flex justify-content-md-end mt-4'
            )
        )

    def clean_email(self):
        """Enhanced email validation."""
        email = self.cleaned_data.get('email')
        if email:
            # Convert to lowercase
            email = email.lower()

            # Check uniqueness excluding current instance
            qs = Client.objects.filter(email=email)
            if self.instance.pk:
                qs = qs.exclude(pk=self.instance.pk)

            if qs.exists():
                raise ValidationError(_('هذا البريد الإلكتروني مسجل بالفعل'))

            # Basic format validation (can be extended)
            if not '@' in email or not '.' in email:
                raise ValidationError(_('صيغة البريد الإلكتروني غير صحيحة'))

        return email

    def clean_phone(self):
        """Enhanced phone validation."""
        phone = self.cleaned_data.get('phone')
        if phone:
            # Remove non-digit characters
            phone = ''.join(filter(str.isdigit, phone))

            # Validate Moroccan phone format
            if not phone.startswith('212') or len(phone) != 12:
                raise ValidationError(_('يجب أن يكون رقم الهاتف مغربياً صحيحاً'))

            # Format number
            phone = f'+{phone[:3]}-{phone[3:6]}-{phone[6:9]}-{phone[9:]}'

            # Check uniqueness excluding current instance
            qs = Client.objects.filter(phone=phone)
            if self.instance.pk:
                qs = qs.exclude(pk=self.instance.pk)

            if qs.exists():
                raise ValidationError(_('هذا الرقم مسجل بالفعل'))

        return phone

    def clean_passport_number(self):
        """Enhanced passport validation."""
        passport = self.cleaned_data.get('passport_number')
        if passport:
            # Standardize format (remove spaces and convert to uppercase)
            passport = passport.replace(' ', '').upper()

            # Check uniqueness excluding current instance
            qs = Client.objects.filter(passport_number=passport)
            if self.instance.pk:
                qs = qs.exclude(pk=self.instance.pk)

            if qs.exists():
                raise ValidationError(_('رقم جواز السفر مسجل بالفعل'))

            # Basic format validation (can be customized per country)
            if len(passport) < 5:
                raise ValidationError(_('رقم جواز السفر قصير جداً'))

        return passport

    def clean(self):
        """Additional cross-field validation."""
        cleaned_data = super().clean()

        # Validate passport expiry
        passport_number = cleaned_data.get('passport_number')
        passport_expiry = cleaned_data.get('passport_expiry')

        if passport_number and not passport_expiry:
            self.add_error('passport_expiry', _('تاريخ انتهاء جواز السفر مطلوب'))

        if passport_expiry and passport_expiry <= timezone.now().date():
            self.add_error('passport_expiry', _('يجب أن يكون تاريخ انتهاء جواز السفر في المستقبل'))

        # Validate company information for corporate clients
        client_type = cleaned_data.get('client_type')
        company_name = cleaned_data.get('company_name')
        tax_number = cleaned_data.get('tax_number')

        if client_type == 'corporate':
            if not company_name:
                self.add_error('company_name', _('اسم الشركة مطلوب للعملاء من نوع شركة'))
            if not tax_number:
                self.add_error('tax_number', _('الرقم الضريبي مطلوب للعملاء من نوع شركة'))

        # Confirm email validation
        email = cleaned_data.get('email')
        confirm_email = cleaned_data.get('confirm_email')

        if email and confirm_email and email != confirm_email:
            self.add_error('confirm_email', _('البريد الإلكتروني غير متطابق'))

        return cleaned_data


class ClientSearchForm(forms.Form):
    """Advanced client search form."""

    search_query = forms.CharField(
        label=_('البحث'),
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('ابحث بالاسم، البريد، الهاتف، أو رقم العميل'),
            'autocomplete': 'off'
        })
    )

    client_type = forms.ChoiceField(
        label=_('نوع العميل'),
        choices=[('', _('جميع الأنواع'))] + Client.CLIENT_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    vip_status = forms.ChoiceField(
        label=_('حالة VIP'),
        choices=[
            ('', _('الكل')),
            ('true', _('عميل مميز')),
            ('false', _('عميل عادي'))
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    date_from = forms.DateField(
        label=_('من تاريخ'),
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )

    date_to = forms.DateField(
        label=_('إلى تاريخ'),
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.helper = FormHelper()
        self.helper.form_method = 'get'
        self.helper.form_class = 'search-form'

        self.helper.layout = Layout(
            Row(
                Column('search_query', css_class='form-group col-md-4 mb-3'),
                Column('client_type', css_class='form-group col-md-2 mb-3'),
                Column('vip_status', css_class='form-group col-md-2 mb-3'),
                Column(
                    Submit('search', _('بحث'), css_class='btn btn-primary'),
                    css_class='form-group col-md-2 mb-3 d-flex align-items-end'
                ),
                css_class='form-row'
            ),
            Row(
                Column('date_from', css_class='form-group col-md-3 mb-3'),
                Column('date_to', css_class='form-group col-md-3 mb-3'),
                Column(
                    Button('reset', _('إعادة تعيين'), css_class='btn btn-outline-secondary', onclick='resetForm()'),
                    css_class='form-group col-md-3 mb-3 d-flex align-items-end'
                ),
                css_class='form-row'
            )
        )
