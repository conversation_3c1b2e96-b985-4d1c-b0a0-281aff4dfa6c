# Generated by Django 4.2 on 2025-06-14 09:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0003_remove_user_account_locked_until_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='usersession',
            name='ip_address',
            field=models.GenericIPAddressField(help_text='IP address used for this session', verbose_name='عنوان IP'),
        ),
        migrations.AlterField(
            model_name='usersession',
            name='is_active',
            field=models.BooleanField(default=True, help_text='Whether this session is currently active', verbose_name='نشط'),
        ),
        migrations.AlterField(
            model_name='usersession',
            name='last_activity',
            field=models.DateTimeField(auto_now=True, help_text='Last activity timestamp', verbose_name='آخر نشاط'),
        ),
        migrations.AlterField(
            model_name='usersession',
            name='login_time',
            field=models.DateTimeField(auto_now_add=True, help_text='When the user logged in', verbose_name='وقت تسجيل الدخول'),
        ),
        migrations.AlterField(
            model_name='usersession',
            name='logout_time',
            field=models.DateTimeField(blank=True, help_text='When the user logged out', null=True, verbose_name='وقت تسجيل الخروج'),
        ),
        migrations.AlterField(
            model_name='usersession',
            name='session_key',
            field=models.CharField(help_text='Unique identifier for this session', max_length=40, unique=True, verbose_name='مفتاح الجلسة'),
        ),
        migrations.AlterField(
            model_name='usersession',
            name='user_agent',
            field=models.TextField(help_text='Browser and system information', verbose_name='معلومات المتصفح'),
        ),
    ]
