"""
Django admin configuration for HR app.
"""
from django.contrib import admin
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from .models import Department, Position, Schedule, Leave, Attendance, Payroll


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    """Admin interface for Department model."""
    list_display = ['name_ar', 'name_fr', 'manager', 'employee_count', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name_ar', 'name_fr', 'description']
    list_editable = ['is_active']
    readonly_fields = ['employee_count', 'created_at', 'updated_at']

    def employee_count(self, obj):
        """Count employees in department."""
        return obj.user_set.count()
    employee_count.short_description = _('عدد الموظفين')

    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('name_ar', 'name_fr', 'description')
        }),
        (_('الإدارة'), {
            'fields': ('manager', 'employee_count')
        }),
        (_('الحالة'), {
            'fields': ('is_active',)
        }),
        (_('معلومات النظام'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Position)
class PositionAdmin(admin.ModelAdmin):
    """Admin interface for Position model."""
    list_display = ['title_ar', 'title_fr', 'department', 'min_salary', 'max_salary', 'is_active']
    list_filter = ['is_active', 'department', 'created_at']
    search_fields = ['title_ar', 'title_fr', 'description']
    list_editable = ['is_active']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('title_ar', 'title_fr', 'description', 'department')
        }),
        (_('نطاق الراتب'), {
            'fields': ('min_salary', 'max_salary')
        }),
        (_('المتطلبات والمسؤوليات'), {
            'fields': ('requirements', 'responsibilities'),
            'classes': ('collapse',)
        }),
        (_('الحالة'), {
            'fields': ('is_active',)
        }),
        (_('معلومات النظام'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Schedule)
class ScheduleAdmin(admin.ModelAdmin):
    """Admin interface for Schedule model."""
    list_display = ['name', 'working_hours_summary', 'break_duration', 'is_active', 'is_default']
    list_filter = ['is_active', 'is_default']
    search_fields = ['name', 'description']
    list_editable = ['is_active', 'is_default']
    readonly_fields = ['working_hours_summary', 'created_at', 'updated_at']

    def working_hours_summary(self, obj):
        """Display working hours summary."""
        days = []
        if obj.monday_start and obj.monday_end:
            days.append(f"الاثنين: {obj.monday_start}-{obj.monday_end}")
        if obj.tuesday_start and obj.tuesday_end:
            days.append(f"الثلاثاء: {obj.tuesday_start}-{obj.tuesday_end}")
        # Add other days as needed
        return " | ".join(days[:2]) + ("..." if len(days) > 2 else "")
    working_hours_summary.short_description = _('ملخص ساعات العمل')

    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('name', 'description')
        }),
        (_('ساعات العمل - الأسبوع'), {
            'fields': (
                ('monday_start', 'monday_end'),
                ('tuesday_start', 'tuesday_end'),
                ('wednesday_start', 'wednesday_end'),
                ('thursday_start', 'thursday_end'),
                ('friday_start', 'friday_end'),
            )
        }),
        (_('ساعات العمل - عطلة نهاية الأسبوع'), {
            'fields': (
                ('saturday_start', 'saturday_end'),
                ('sunday_start', 'sunday_end'),
            ),
            'classes': ('collapse',)
        }),
        (_('الاستراحة'), {
            'fields': ('break_duration',)
        }),
        (_('الحالة'), {
            'fields': ('is_active', 'is_default')
        }),
        (_('معلومات النظام'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Leave)
class LeaveAdmin(admin.ModelAdmin):
    """Admin interface for Leave model."""
    list_display = ['employee', 'leave_type', 'start_date', 'end_date', 'days_requested', 'status', 'approved_by']
    list_filter = ['leave_type', 'status', 'start_date', 'created_at']
    search_fields = ['employee__first_name', 'employee__last_name', 'reason']
    list_editable = ['status']
    readonly_fields = ['days_requested', 'created_at', 'updated_at']
    date_hierarchy = 'start_date'

    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('employee', 'leave_type')
        }),
        (_('التواريخ'), {
            'fields': ('start_date', 'end_date', 'days_requested')
        }),
        (_('السبب'), {
            'fields': ('reason',)
        }),
        (_('الحالة'), {
            'fields': ('status',)
        }),
        (_('الموافقة'), {
            'fields': ('approved_by', 'approval_date', 'approval_notes'),
            'classes': ('collapse',)
        }),
        (_('الوثائق'), {
            'fields': ('supporting_document',),
            'classes': ('collapse',)
        }),
        (_('معلومات النظام'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Attendance)
class AttendanceAdmin(admin.ModelAdmin):
    """Admin interface for Attendance model."""
    list_display = ['employee', 'date', 'check_in_time', 'check_out_time', 'actual_hours', 'overtime_hours', 'status']
    list_filter = ['status', 'date', 'employee']
    search_fields = ['employee__first_name', 'employee__last_name']
    list_editable = ['status']
    readonly_fields = ['actual_hours', 'overtime_hours', 'created_at', 'updated_at']
    date_hierarchy = 'date'

    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('employee', 'date')
        }),
        (_('أوقات الحضور'), {
            'fields': ('check_in_time', 'check_out_time')
        }),
        (_('ساعات العمل'), {
            'fields': ('scheduled_hours', 'actual_hours', 'overtime_hours')
        }),
        (_('الحالة'), {
            'fields': ('status',)
        }),
        (_('ملاحظات'), {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
        (_('معلومات النظام'), {
            'fields': ('recorded_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Payroll)
class PayrollAdmin(admin.ModelAdmin):
    """Admin interface for Payroll model."""
    list_display = ['employee', 'pay_period_display', 'gross_salary', 'total_deductions', 'net_salary', 'status']
    list_filter = ['status', 'pay_period_end', 'pay_date']
    search_fields = ['employee__first_name', 'employee__last_name']
    list_editable = ['status']
    readonly_fields = ['overtime_amount', 'gross_salary', 'total_deductions', 'net_salary', 'created_at', 'updated_at']
    date_hierarchy = 'pay_period_end'

    def pay_period_display(self, obj):
        """Display pay period in readable format."""
        return f"{obj.pay_period_start} - {obj.pay_period_end}"
    pay_period_display.short_description = _('فترة الراتب')

    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('employee', 'pay_period_start', 'pay_period_end', 'pay_date')
        }),
        (_('الراتب الأساسي'), {
            'fields': ('basic_salary',)
        }),
        (_('البدلات'), {
            'fields': ('housing_allowance', 'transport_allowance', 'meal_allowance', 'other_allowances')
        }),
        (_('الساعات الإضافية'), {
            'fields': ('overtime_hours', 'overtime_rate', 'overtime_amount')
        }),
        (_('الخصومات'), {
            'fields': ('tax_deduction', 'social_security', 'insurance_deduction', 'loan_deduction', 'other_deductions')
        }),
        (_('الإجماليات'), {
            'fields': ('gross_salary', 'total_deductions', 'net_salary')
        }),
        (_('الحالة'), {
            'fields': ('status',)
        }),
        (_('الموافقة'), {
            'fields': ('approved_by', 'approval_date'),
            'classes': ('collapse',)
        }),
        (_('ملاحظات'), {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
        (_('معلومات النظام'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
