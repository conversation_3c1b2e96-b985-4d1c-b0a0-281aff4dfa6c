"""
Serializers for Finance app.
"""
from rest_framework import serializers
from .models import Invoice, Payment, Expense, BankAccount, InvoiceItem
# from apps.crm.serializers import ClientSerializer
# from apps.reservations.serializers import ReservationSerializer
# from apps.suppliers.serializers import SupplierSerializer


class InvoiceItemSerializer(serializers.ModelSerializer):
    """Serializer for invoice items."""

    class Meta:
        model = InvoiceItem
        fields = [
            'id', 'description', 'quantity', 'unit_price', 'total_price'
        ]


class InvoiceSerializer(serializers.ModelSerializer):
    """Serializer for invoices."""

    client = serializers.StringRelatedField(read_only=True)
    client_id = serializers.IntegerField(write_only=True)
    reservation = serializers.StringRelatedField(read_only=True)
    reservation_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    items = InvoiceItemSerializer(many=True, read_only=True)
    status_display = serializers.Char<PERSON>ield(source='get_status_display', read_only=True)
    is_overdue = serializers.BooleanField(read_only=True)
    days_until_due = serializers.SerializerMethodField()

    class Meta:
        model = Invoice
        fields = [
            'id', 'invoice_number', 'client', 'client_id', 'reservation', 'reservation_id',
            'issue_date', 'due_date', 'subtotal', 'tax_rate', 'tax_amount',
            'discount_amount', 'total_amount', 'status', 'status_display',
            'notes', 'items', 'is_overdue', 'days_until_due',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['invoice_number', 'created_at', 'updated_at']

    def get_days_until_due(self, obj):
        """Calculate days until due date."""
        if obj.due_date:
            from django.utils import timezone
            today = timezone.now().date()
            delta = obj.due_date - today
            return delta.days
        return None

    def validate(self, data):
        """Validate invoice data."""
        if data.get('due_date') and data.get('issue_date'):
            if data['due_date'] < data['issue_date']:
                raise serializers.ValidationError(
                    "تاريخ الاستحقاق يجب أن يكون بعد تاريخ الإصدار"
                )

        if data.get('total_amount', 0) < 0:
            raise serializers.ValidationError(
                "المبلغ الإجمالي يجب أن يكون أكبر من الصفر"
            )

        return data


class PaymentSerializer(serializers.ModelSerializer):
    """Serializer for payments."""

    invoice = InvoiceSerializer(read_only=True)
    invoice_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    reservation = ReservationSerializer(read_only=True)
    reservation_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    payment_method_display = serializers.CharField(source='get_payment_method_display', read_only=True)

    class Meta:
        model = Payment
        fields = [
            'id', 'payment_number', 'invoice', 'invoice_id', 'reservation', 'reservation_id',
            'amount', 'payment_date', 'payment_method', 'payment_method_display',
            'reference_number', 'status', 'status_display', 'notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['payment_number', 'created_at', 'updated_at']

    def validate(self, data):
        """Validate payment data."""
        if not data.get('invoice_id') and not data.get('reservation_id'):
            raise serializers.ValidationError(
                "يجب تحديد فاتورة أو حجز للدفع"
            )

        if data.get('amount', 0) <= 0:
            raise serializers.ValidationError(
                "مبلغ الدفع يجب أن يكون أكبر من الصفر"
            )

        # Validate payment amount against invoice
        if data.get('invoice_id'):
            try:
                invoice = Invoice.objects.get(id=data['invoice_id'])
                if data.get('amount', 0) > invoice.total_amount:
                    raise serializers.ValidationError(
                        "مبلغ الدفع لا يمكن أن يكون أكبر من مبلغ الفاتورة"
                    )
            except Invoice.DoesNotExist:
                raise serializers.ValidationError("الفاتورة غير موجودة")

        return data


class ExpenseSerializer(serializers.ModelSerializer):
    """Serializer for expenses."""

    vendor = SupplierSerializer(read_only=True)
    vendor_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    submitted_by = serializers.StringRelatedField(read_only=True)
    approved_by = serializers.StringRelatedField(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    category_display = serializers.CharField(source='get_category_display', read_only=True)

    class Meta:
        model = Expense
        fields = [
            'id', 'expense_number', 'vendor', 'vendor_id', 'category', 'category_display',
            'description', 'amount', 'expense_date', 'receipt_number',
            'status', 'status_display', 'submitted_by', 'approved_by',
            'approval_date', 'notes', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'expense_number', 'submitted_by', 'approved_by', 'approval_date',
            'created_at', 'updated_at'
        ]

    def validate(self, data):
        """Validate expense data."""
        if data.get('amount', 0) <= 0:
            raise serializers.ValidationError(
                "مبلغ المصروف يجب أن يكون أكبر من الصفر"
            )

        if data.get('expense_date'):
            from django.utils import timezone
            if data['expense_date'] > timezone.now().date():
                raise serializers.ValidationError(
                    "تاريخ المصروف لا يمكن أن يكون في المستقبل"
                )

        return data


class BankAccountSerializer(serializers.ModelSerializer):
    """Serializer for bank accounts."""

    currency_display = serializers.CharField(source='get_currency_display', read_only=True)
    formatted_balance = serializers.SerializerMethodField()

    class Meta:
        model = BankAccount
        fields = [
            'id', 'account_name', 'bank_name', 'account_number',
            'iban', 'swift_code', 'currency', 'currency_display',
            'balance', 'formatted_balance', 'is_active', 'notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_formatted_balance(self, obj):
        """Get formatted balance with currency."""
        return f"{obj.balance:,.2f} {obj.get_currency_display()}"

    def validate_balance(self, value):
        """Validate balance."""
        if value < 0:
            raise serializers.ValidationError(
                "الرصيد لا يمكن أن يكون سالباً"
            )
        return value

    def validate_account_number(self, value):
        """Validate account number uniqueness."""
        if self.instance:
            # Exclude current instance from uniqueness check
            if BankAccount.objects.exclude(
                id=self.instance.id
            ).filter(account_number=value).exists():
                raise serializers.ValidationError(
                    "رقم الحساب موجود مسبقاً"
                )
        else:
            if BankAccount.objects.filter(account_number=value).exists():
                raise serializers.ValidationError(
                    "رقم الحساب موجود مسبقاً"
                )
        return value


class FinancialSummarySerializer(serializers.Serializer):
    """Serializer for financial summary data."""

    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    total_expenses = serializers.DecimalField(max_digits=12, decimal_places=2)
    net_profit = serializers.DecimalField(max_digits=12, decimal_places=2)
    total_invoices = serializers.IntegerField()
    paid_invoices = serializers.IntegerField()
    pending_invoices = serializers.IntegerField()
    overdue_invoices = serializers.IntegerField()
    total_payments = serializers.IntegerField()
    pending_payments = serializers.IntegerField()
    total_expenses_count = serializers.IntegerField()
    pending_expenses = serializers.IntegerField()

    # Monthly data
    monthly_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    monthly_expenses = serializers.DecimalField(max_digits=12, decimal_places=2)
    monthly_profit = serializers.DecimalField(max_digits=12, decimal_places=2)
    monthly_invoices = serializers.IntegerField()
    monthly_payments = serializers.IntegerField()

    # Bank accounts
    total_bank_balance = serializers.DecimalField(max_digits=12, decimal_places=2)
    active_bank_accounts = serializers.IntegerField()


class InvoiceReportSerializer(serializers.Serializer):
    """Serializer for invoice reports."""

    period = serializers.CharField()
    total_invoices = serializers.IntegerField()
    total_amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    paid_amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    pending_amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    overdue_amount = serializers.DecimalField(max_digits=12, decimal_places=2)

    # Status breakdown
    draft_count = serializers.IntegerField()
    sent_count = serializers.IntegerField()
    paid_count = serializers.IntegerField()
    overdue_count = serializers.IntegerField()


class PaymentReportSerializer(serializers.Serializer):
    """Serializer for payment reports."""

    period = serializers.CharField()
    total_payments = serializers.IntegerField()
    total_amount = serializers.DecimalField(max_digits=12, decimal_places=2)

    # Payment method breakdown
    cash_amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    bank_transfer_amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    credit_card_amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    check_amount = serializers.DecimalField(max_digits=12, decimal_places=2)

    # Status breakdown
    completed_count = serializers.IntegerField()
    pending_count = serializers.IntegerField()
    failed_count = serializers.IntegerField()


class ExpenseReportSerializer(serializers.Serializer):
    """Serializer for expense reports."""

    period = serializers.CharField()
    total_expenses = serializers.IntegerField()
    total_amount = serializers.DecimalField(max_digits=12, decimal_places=2)

    # Category breakdown
    travel_amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    accommodation_amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    transportation_amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    marketing_amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    office_amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    other_amount = serializers.DecimalField(max_digits=12, decimal_places=2)

    # Status breakdown
    pending_count = serializers.IntegerField()
    approved_count = serializers.IntegerField()
    paid_count = serializers.IntegerField()
    rejected_count = serializers.IntegerField()
