"""
Tour package models for managing destinations, itineraries, and tour packages.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
# from djmoney.models.fields import MoneyField
from apps.core.models import TimeStampedModel


class Destination(TimeStampedModel):
    """Destination model for storing travel destinations."""
    name_ar = models.CharField(_('الاسم بالعربية'), max_length=100)
    name_fr = models.Char<PERSON>ield(_('الاسم بالفرنسية'), max_length=100)
    name_en = models.CharField(_('الاسم بالإنجليزية'), max_length=100)

    description_ar = models.TextField(_('الوصف بالعربية'), blank=True)
    description_fr = models.TextField(_('الوصف بالفرنسية'), blank=True)
    description_en = models.TextField(_('الوصف بالإنجليزية'), blank=True)

    country = models.ForeignKey('core.Country', on_delete=models.CASCADE, verbose_name=_('الدولة'))
    city = models.ForeignKey('core.City', on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_('المدينة'))

    # Geographic Information
    latitude = models.DecimalField(_('خط العرض'), max_digits=9, decimal_places=6, null=True, blank=True)
    longitude = models.DecimalField(_('خط الطول'), max_digits=9, decimal_places=6, null=True, blank=True)

    # Media
    main_image = models.ImageField(_('الصورة الرئيسية'), upload_to='destinations/', blank=True)
    gallery = models.JSONField(_('معرض الصور'), default=list, blank=True)

    # Characteristics
    climate = models.CharField(_('المناخ'), max_length=50, blank=True)
    best_time_to_visit = models.CharField(_('أفضل وقت للزيارة'), max_length=100, blank=True)
    activities = models.TextField(_('الأنشطة المتاحة'), blank=True)

    # Status
    is_active = models.BooleanField(_('نشط'), default=True)
    is_featured = models.BooleanField(_('مميز'), default=False)
    popularity_score = models.PositiveIntegerField(_('نقاط الشعبية'), default=0)

    class Meta:
        verbose_name = _('وجهة سياحية')
        verbose_name_plural = _('الوجهات السياحية')
        ordering = ['name_ar']

    def __str__(self):
        return f"{self.name_ar}, {self.country.name_ar}"


class TourCategory(TimeStampedModel):
    """Categories for organizing tour packages."""
    name_ar = models.CharField(_('الاسم بالعربية'), max_length=50)
    name_fr = models.CharField(_('الاسم بالفرنسية'), max_length=50)
    name_en = models.CharField(_('الاسم بالإنجليزية'), max_length=50)

    description = models.TextField(_('الوصف'), blank=True)
    icon = models.CharField(_('الأيقونة'), max_length=50, blank=True)
    color = models.CharField(_('اللون'), max_length=7, default='#007bff')

    is_active = models.BooleanField(_('نشط'), default=True)
    sort_order = models.PositiveIntegerField(_('ترتيب العرض'), default=0)

    class Meta:
        verbose_name = _('فئة الباقة')
        verbose_name_plural = _('فئات الباقات')
        ordering = ['sort_order', 'name_ar']

    def __str__(self):
        return self.name_ar


class TourPackage(AuditModel):
    """Main tour package model."""

    DIFFICULTY_CHOICES = [
        ('easy', _('سهل')),
        ('moderate', _('متوسط')),
        ('challenging', _('صعب')),
        ('extreme', _('شديد الصعوبة')),
    ]

    # Basic Information
    package_code = models.CharField(_('رمز الباقة'), max_length=20, unique=True)
    title_ar = models.CharField(_('العنوان بالعربية'), max_length=200)
    title_fr = models.CharField(_('العنوان بالفرنسية'), max_length=200)
    title_en = models.CharField(_('العنوان بالإنجليزية'), max_length=200)

    short_description_ar = models.TextField(_('وصف مختصر بالعربية'), max_length=500)
    short_description_fr = models.TextField(_('وصف مختصر بالفرنسية'), max_length=500)
    short_description_en = models.TextField(_('وصف مختصر بالإنجليزية'), max_length=500)

    detailed_description_ar = models.TextField(_('وصف مفصل بالعربية'))
    detailed_description_fr = models.TextField(_('وصف مفصل بالفرنسية'))
    detailed_description_en = models.TextField(_('وصف مفصل بالإنجليزية'))

    # Classification
    category = models.ForeignKey(TourCategory, on_delete=models.CASCADE, verbose_name=_('الفئة'))
    destinations = models.ManyToManyField(Destination, verbose_name=_('الوجهات'))

    # Duration and Timing
    duration_days = models.PositiveIntegerField(_('المدة بالأيام'))
    duration_nights = models.PositiveIntegerField(_('عدد الليالي'))

    # Pricing
    base_price = models.DecimalField(_('السعر الأساسي'), max_digits=10, decimal_places=2)
    child_price = models.DecimalField(_('سعر الطفل'), max_digits=10, decimal_places=2, null=True, blank=True)
    infant_price = models.DecimalField(_('سعر الرضيع'), max_digits=10, decimal_places=2, null=True, blank=True)

    # Capacity
    min_participants = models.PositiveIntegerField(_('الحد الأدنى للمشاركين'), default=1)
    max_participants = models.PositiveIntegerField(_('الحد الأقصى للمشاركين'))

    # Characteristics
    difficulty_level = models.CharField(_('مستوى الصعوبة'), max_length=20, choices=DIFFICULTY_CHOICES, default='easy')
    physical_rating = models.PositiveIntegerField(
        _('التقييم البدني'),
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        default=1
    )

    # Inclusions and Exclusions
    inclusions = models.TextField(_('المشمول في الباقة'))
    exclusions = models.TextField(_('غير المشمول في الباقة'))

    # Requirements
    requirements = models.TextField(_('المتطلبات'), blank=True)
    age_restrictions = models.CharField(_('قيود العمر'), max_length=100, blank=True)
    fitness_level = models.CharField(_('مستوى اللياقة المطلوب'), max_length=100, blank=True)

    # Media
    main_image = models.ImageField(_('الصورة الرئيسية'), upload_to='packages/', blank=True)
    gallery = models.JSONField(_('معرض الصور'), default=list, blank=True)
    video_url = models.URLField(_('رابط الفيديو'), blank=True)

    # SEO and Marketing
    meta_title = models.CharField(_('عنوان SEO'), max_length=60, blank=True)
    meta_description = models.CharField(_('وصف SEO'), max_length=160, blank=True)
    keywords = models.CharField(_('الكلمات المفتاحية'), max_length=200, blank=True)

    # Status and Availability
    is_active = models.BooleanField(_('نشط'), default=True)
    is_featured = models.BooleanField(_('مميز'), default=False)
    is_bestseller = models.BooleanField(_('الأكثر مبيعاً'), default=False)

    # Seasonal Information
    available_from = models.DateField(_('متاح من'), null=True, blank=True)
    available_to = models.DateField(_('متاح حتى'), null=True, blank=True)

    # Internal
    created_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, verbose_name=_('أنشئ بواسطة'))
    notes = models.TextField(_('ملاحظات داخلية'), blank=True)

    class Meta:
        verbose_name = _('باقة سياحية')
        verbose_name_plural = _('الباقات السياحية')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['package_code']),
            models.Index(fields=['is_active', 'is_featured']),
            models.Index(fields=['category']),
        ]

    def __str__(self):
        return f"{self.package_code} - {self.title_ar}"

    def save(self, *args, **kwargs):
        if not self.package_code:
            # Generate package code
            last_package = TourPackage.objects.filter(package_code__startswith='PKG').order_by('-id').first()
            if last_package:
                last_number = int(last_package.package_code[3:])
                self.package_code = f"PKG{last_number + 1:06d}"
            else:
                self.package_code = "PKG000001"
        super().save(*args, **kwargs)


class Itinerary(AuditModel):
    """Daily itinerary for tour packages."""
    package = models.ForeignKey(TourPackage, on_delete=models.CASCADE, related_name='itineraries', verbose_name=_('الباقة'))
    day_number = models.PositiveIntegerField(_('رقم اليوم'))

    title_ar = models.CharField(_('عنوان اليوم بالعربية'), max_length=200)
    title_fr = models.CharField(_('عنوان اليوم بالفرنسية'), max_length=200)
    title_en = models.CharField(_('عنوان اليوم بالإنجليزية'), max_length=200)

    description_ar = models.TextField(_('وصف اليوم بالعربية'))
    description_fr = models.TextField(_('وصف اليوم بالفرنسية'))
    description_en = models.TextField(_('وصف اليوم بالإنجليزية'))

    # Location
    location = models.ForeignKey(Destination, on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_('الموقع'))
    accommodation = models.CharField(_('الإقامة'), max_length=200, blank=True)

    # Meals
    breakfast_included = models.BooleanField(_('الإفطار مشمول'), default=False)
    lunch_included = models.BooleanField(_('الغداء مشمول'), default=False)
    dinner_included = models.BooleanField(_('العشاء مشمول'), default=False)

    # Activities
    activities = models.TextField(_('الأنشطة'), blank=True)
    optional_activities = models.TextField(_('الأنشطة الاختيارية'), blank=True)

    # Media
    image = models.ImageField(_('صورة اليوم'), upload_to='itinerary/', blank=True)

    class Meta:
        verbose_name = _('برنامج يومي')
        verbose_name_plural = _('البرامج اليومية')
        ordering = ['package', 'day_number']
        unique_together = ['package', 'day_number']

    def __str__(self):
        return f"{self.package.title_ar} - اليوم {self.day_number}"


class PackageAvailability(AuditModel):
    """Track package availability and pricing for specific dates."""
    package = models.ForeignKey(TourPackage, on_delete=models.CASCADE, related_name='availabilities', verbose_name=_('الباقة'))

    start_date = models.DateField(_('تاريخ البداية'))
    end_date = models.DateField(_('تاريخ النهاية'))

    # Pricing (can override package base price)
    adult_price = models.DecimalField(_('سعر البالغ'), max_digits=10, decimal_places=2, null=True, blank=True)
    child_price = models.DecimalField(_('سعر الطفل'), max_digits=10, decimal_places=2, null=True, blank=True)
    infant_price = models.DecimalField(_('سعر الرضيع'), max_digits=10, decimal_places=2, null=True, blank=True)

    # Availability
    available_spots = models.PositiveIntegerField(_('الأماكن المتاحة'))
    reserved_spots = models.PositiveIntegerField(_('الأماكن المحجوزة'), default=0)

    # Status
    is_available = models.BooleanField(_('متاح'), default=True)
    is_guaranteed = models.BooleanField(_('مضمون الانطلاق'), default=False)

    # Special offers
    discount_percentage = models.DecimalField(_('نسبة الخصم'), max_digits=5, decimal_places=2, default=0)
    early_bird_discount = models.DecimalField(_('خصم الحجز المبكر'), max_digits=5, decimal_places=2, default=0)

    notes = models.TextField(_('ملاحظات'), blank=True)

    class Meta:
        verbose_name = _('توفر الباقة')
        verbose_name_plural = _('توفر الباقات')
        ordering = ['start_date']
        unique_together = ['package', 'start_date']

    def __str__(self):
        return f"{self.package.title_ar} - {self.start_date}"

    @property
    def remaining_spots(self):
        """Calculate remaining available spots."""
        return max(0, self.available_spots - self.reserved_spots)
