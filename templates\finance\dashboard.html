{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}لوحة التحكم المالية{% endblock %}

{% block extra_css %}
<style>
.finance-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.finance-stat {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    margin-bottom: 15px;
}

.finance-stat h3 {
    font-size: 2rem;
    margin-bottom: 5px;
    color: #fff;
}

.finance-stat p {
    margin: 0;
    opacity: 0.9;
}

.revenue-card {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.expense-card {
    background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
}

.invoice-card {
    background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
}

.overdue-card {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.chart-container {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.table-responsive {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.btn-finance {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-finance:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">💰 لوحة التحكم المالية</h1>
                    <p class="text-muted">نظرة شاملة على الوضع المالي للوكالة</p>
                </div>
                <div>
                    <a href="{% url 'finance:invoice_list' %}" class="btn btn-finance me-2">
                        <i class="fas fa-file-invoice"></i> الفواتير
                    </a>
                    <a href="{% url 'finance:reports' %}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-bar"></i> التقارير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="finance-card revenue-card">
                <div class="finance-stat">
                    <h3>{{ stats.total_revenue|floatformat:0 }} درهم</h3>
                    <p><i class="fas fa-arrow-up"></i> إجمالي الإيرادات</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="finance-card expense-card">
                <div class="finance-stat">
                    <h3>{{ stats.total_expenses|floatformat:0 }} درهم</h3>
                    <p><i class="fas fa-arrow-down"></i> إجمالي المصروفات</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="finance-card invoice-card">
                <div class="finance-stat">
                    <h3>{{ stats.total_invoices }}</h3>
                    <p><i class="fas fa-file-invoice"></i> إجمالي الفواتير</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="finance-card overdue-card">
                <div class="finance-stat">
                    <h3>{{ stats.overdue_invoices }}</h3>
                    <p><i class="fas fa-exclamation-triangle"></i> فواتير متأخرة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Statistics -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="finance-card">
                <h5><i class="fas fa-calendar-month"></i> إحصائيات الشهر الحالي</h5>
                <div class="row">
                    <div class="col-6">
                        <div class="finance-stat">
                            <h4>{{ stats.monthly_revenue|floatformat:0 }} درهم</h4>
                            <p>إيرادات الشهر</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="finance-stat">
                            <h4>{{ stats.monthly_expenses|floatformat:0 }} درهم</h4>
                            <p>مصروفات الشهر</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="finance-card">
                <h5><i class="fas fa-credit-card"></i> حالة المدفوعات</h5>
                <div class="row">
                    <div class="col-6">
                        <div class="finance-stat">
                            <h4>{{ stats.pending_invoices }}</h4>
                            <p>فواتير معلقة</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="finance-stat">
                            <h4>{{ stats.pending_payments }}</h4>
                            <p>مدفوعات معلقة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Tables -->
    <div class="row">
        <!-- Revenue Chart -->
        <div class="col-lg-8">
            <div class="chart-container">
                <h5><i class="fas fa-chart-line"></i> اتجاه الإيرادات الشهرية</h5>
                <canvas id="revenueChart" height="100"></canvas>
            </div>
        </div>

        <!-- Recent Invoices -->
        <div class="col-lg-4">
            <div class="table-responsive">
                <h5><i class="fas fa-file-invoice"></i> أحدث الفواتير</h5>
                <div class="list-group list-group-flush">
                    {% for invoice in recent_invoices %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">{{ invoice.invoice_number }}</h6>
                            <p class="mb-1 text-muted">{{ invoice.client.full_name_ar }}</p>
                            <small>{{ invoice.issue_date }}</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-primary">{{ invoice.total_amount }} درهم</span>
                            <br>
                            <small class="badge bg-{{ invoice.status|yesno:'success,warning,danger' }}">
                                {{ invoice.get_status_display }}
                            </small>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p>لا توجد فواتير حديثة</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Overdue Invoices Alert -->
    {% if overdue_invoices %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-warning">
                <h5><i class="fas fa-exclamation-triangle"></i> تنبيه: فواتير متأخرة</h5>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>الأيام المتأخرة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in overdue_invoices %}
                            <tr>
                                <td>{{ invoice.invoice_number }}</td>
                                <td>{{ invoice.client.full_name_ar }}</td>
                                <td>{{ invoice.total_amount }} درهم</td>
                                <td>{{ invoice.due_date }}</td>
                                <td>
                                    <span class="badge bg-danger">
                                        {{ invoice.due_date|timesince }} متأخرة
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Chart
const ctx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: [{% for data in monthly_data %}'{{ data.month }}'{% if not forloop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'الإيرادات (درهم)',
            data: [{% for data in monthly_data %}{{ data.revenue }}{% if not forloop.last %},{% endif %}{% endfor %}],
            borderColor: 'rgb(102, 126, 234)',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' درهم';
                    }
                }
            }
        }
    }
});
</script>
{% endblock %}
