"""
API URL configuration for CRM app.
"""
from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from . import api_views

router = DefaultRouter()
router.register(r'clients', api_views.ClientViewSet)
router.register(r'communications', api_views.ClientCommunicationViewSet)
router.register(r'contacts', api_views.ClientContactViewSet)
router.register(r'preferences', api_views.ClientPreferenceViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('clients/<int:client_id>/communications/', api_views.ClientCommunicationListView.as_view(), name='client-communications'),
    path('clients/<int:client_id>/contacts/', api_views.ClientContactListView.as_view(), name='client-contacts'),
    path('search/', api_views.ClientSearchView.as_view(), name='client-search'),
]
