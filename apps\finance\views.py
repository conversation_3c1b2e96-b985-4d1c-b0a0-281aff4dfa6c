"""
Finance views for the Moroccan Travel Agency ERP system.
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Sum, Count, Q
from django.utils import timezone
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin
from datetime import datetime, timedelta
from .models import Invoice, Payment, Expense, BankAccount


@login_required
def finance_dashboard(request):
    """Finance dashboard view."""
    today = timezone.now().date()
    current_month = today.replace(day=1)

    # Financial statistics
    stats = {
        'total_invoices': Invoice.objects.count(),
        'pending_invoices': Invoice.objects.filter(status__in=['draft', 'sent']).count(),
        'overdue_invoices': Invoice.objects.filter(due_date__lt=today, status__ne='paid').count(),
        'total_revenue': Invoice.objects.filter(status='paid').aggregate(
            total=Sum('total_amount')
        )['total'] or 0,
        'monthly_revenue': Invoice.objects.filter(
            status='paid',
            issue_date__gte=current_month
        ).aggregate(total=Sum('total_amount'))['total'] or 0,
        'total_expenses': Expense.objects.filter(status='paid').aggregate(
            total=Sum('amount')
        )['total'] or 0,
        'monthly_expenses': Expense.objects.filter(
            status='paid',
            expense_date__gte=current_month
        ).aggregate(total=Sum('amount'))['total'] or 0,
        'pending_payments': Payment.objects.filter(status='pending').count(),
    }

    # Recent invoices
    recent_invoices = Invoice.objects.select_related('client').order_by('-created_at')[:5]

    # Recent payments
    recent_payments = Payment.objects.select_related('invoice__client').order_by('-payment_date')[:5]

    # Overdue invoices
    overdue_invoices = Invoice.objects.filter(
        due_date__lt=today,
        status__in=['draft', 'sent']
    ).select_related('client').order_by('due_date')[:5]

    # Monthly revenue chart data
    monthly_data = []
    for i in range(6):
        month_start = (current_month - timedelta(days=30*i)).replace(day=1)
        month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)
        revenue = Invoice.objects.filter(
            status='paid',
            issue_date__gte=month_start,
            issue_date__lte=month_end
        ).aggregate(total=Sum('total_amount'))['total'] or 0
        monthly_data.append({
            'month': month_start.strftime('%Y-%m'),
            'revenue': float(revenue)
        })

    context = {
        'stats': stats,
        'recent_invoices': recent_invoices,
        'recent_payments': recent_payments,
        'overdue_invoices': overdue_invoices,
        'monthly_data': list(reversed(monthly_data)),
    }

    return render(request, 'finance/dashboard.html', context)


class InvoiceListView(LoginRequiredMixin, ListView):
    """List view for invoices."""
    model = Invoice
    template_name = 'finance/invoice_list.html'
    context_object_name = 'invoices'
    paginate_by = 20

    def get_queryset(self):
        queryset = Invoice.objects.select_related('client', 'reservation').order_by('-issue_date')

        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Filter by date range
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        if date_from:
            queryset = queryset.filter(issue_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(issue_date__lte=date_to)

        # Search
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(invoice_number__icontains=search) |
                Q(client__first_name_ar__icontains=search) |
                Q(client__last_name_ar__icontains=search) |
                Q(client__email__icontains=search)
            )

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = Invoice.STATUS_CHOICES
        return context


class InvoiceDetailView(LoginRequiredMixin, DetailView):
    """Detail view for invoice."""
    model = Invoice
    template_name = 'finance/invoice_detail.html'
    context_object_name = 'invoice'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['payments'] = self.object.payments.all().order_by('-payment_date')
        context['items'] = self.object.items.all()
        return context


class PaymentListView(LoginRequiredMixin, ListView):
    """List view for payments."""
    model = Payment
    template_name = 'finance/payment_list.html'
    context_object_name = 'payments'
    paginate_by = 20

    def get_queryset(self):
        queryset = Payment.objects.select_related('invoice__client').order_by('-payment_date')

        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Filter by payment method
        method = self.request.GET.get('method')
        if method:
            queryset = queryset.filter(payment_method=method)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = Payment.STATUS_CHOICES
        context['method_choices'] = Payment.PAYMENT_METHOD_CHOICES
        return context


class ExpenseListView(LoginRequiredMixin, ListView):
    """List view for expenses."""
    model = Expense
    template_name = 'finance/expense_list.html'
    context_object_name = 'expenses'
    paginate_by = 20

    def get_queryset(self):
        queryset = Expense.objects.select_related('vendor', 'submitted_by').order_by('-expense_date')

        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Filter by category
        category = self.request.GET.get('category')
        if category:
            queryset = queryset.filter(category=category)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = Expense.STATUS_CHOICES
        context['category_choices'] = Expense.CATEGORY_CHOICES
        return context


@login_required
def financial_reports(request):
    """Financial reports view."""
    today = timezone.now().date()
    current_month = today.replace(day=1)

    # Revenue analysis
    revenue_data = {
        'total_revenue': Invoice.objects.filter(status='paid').aggregate(
            total=Sum('total_amount')
        )['total'] or 0,
        'monthly_revenue': Invoice.objects.filter(
            status='paid',
            issue_date__gte=current_month
        ).aggregate(total=Sum('total_amount'))['total'] or 0,
        'outstanding_amount': Invoice.objects.filter(
            status__in=['draft', 'sent']
        ).aggregate(total=Sum('total_amount'))['total'] or 0,
    }

    # Expense analysis
    expense_data = {
        'total_expenses': Expense.objects.filter(status='paid').aggregate(
            total=Sum('amount')
        )['total'] or 0,
        'monthly_expenses': Expense.objects.filter(
            status='paid',
            expense_date__gte=current_month
        ).aggregate(total=Sum('amount'))['total'] or 0,
        'pending_expenses': Expense.objects.filter(
            status__in=['pending', 'approved']
        ).aggregate(total=Sum('amount'))['total'] or 0,
    }

    # Profit calculation
    profit_data = {
        'monthly_profit': revenue_data['monthly_revenue'] - expense_data['monthly_expenses'],
        'total_profit': revenue_data['total_revenue'] - expense_data['total_expenses'],
    }

    context = {
        'revenue_data': revenue_data,
        'expense_data': expense_data,
        'profit_data': profit_data,
    }

    return render(request, 'finance/reports.html', context)
