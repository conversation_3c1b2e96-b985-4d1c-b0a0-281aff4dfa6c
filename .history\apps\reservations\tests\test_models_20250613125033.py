from django.test import TestCase
from django.utils import timezone
from django.core.exceptions import ValidationError
from apps.reservations.models import (
    Reservation,
    ReservationParticipant,
    ReservationService,
    ReservationDocument
)
from apps.accounts.models import User
from apps.crm.models import Client
from apps.tours.models import TourPackage
from djmoney.money import Money


class TestReservationModel(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test user
        cls.user = User.objects.create(username="testuser")

        # Create test client
        cls.client = Client.objects.create(
            client_code="CL123",
            first_name_ar="Test",
            last_name_ar="Client",
            phone="*********",
            email="<EMAIL>"
        )

        # Create test package
        cls.package = TourPackage.objects.create(
            title_ar="باقة اختبار",
            title_fr="Package Test",
            title_en="Test Package",
            description_ar="وصف الباقة",
            description_fr="Description du package",
            description_en="Package Description",
            duration_days=5,
            min_participants=2,
            max_participants=10
        )

    def test_create_reservation(self):
        # Test basic reservation creation
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD'),
            status='pending'
        )
        self.assertEqual(reservation.status, 'pending')
        self.assertEqual(reservation.adults, 2)
        self.assertIsNotNone(reservation.reservation_number)
        self.assertTrue(reservation.reservation_number.startswith('RES'))

    def test_reservation_total_participants(self):
        # Test participant count calculation
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            children=1,
            infants=1,
            adult_price=Money(1000, 'MAD'),
            child_price=Money(500, 'MAD'),
            infant_price=Money(100, 'MAD')
        )
        self.assertEqual(reservation.total_participants, 4)

    def test_reservation_str(self):
        # Test string representation
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adult_price=Money(1000, 'MAD')
        )
        expected_str = f"{reservation.reservation_number} - {self.client.full_name_ar}"
        self.assertEqual(str(reservation), expected_str)

    def test_reservation_amounts(self):
        # Test price calculations
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            children=1,
            infants=1,
            adult_price=Money(1000, 'MAD'),
            child_price=Money(500, 'MAD'),
            infant_price=Money(100, 'MAD'),
            discount_amount=Money(200, 'MAD'),
            tax_amount=Money(50, 'MAD')
        )
        # Verify subtotal calculation: (2 * 1000) + (1 * 500) + (1 * 100) = 2600
        self.assertEqual(float(reservation.subtotal.amount), 2600.0)
        # Verify total amount: 2600 - 200 + 50 = 2450
        self.assertEqual(float(reservation.total_amount.amount), 2450.0)

    def test_reservation_date_validation(self):
        # Test invalid dates
        with self.assertRaises(ValidationError):
            reservation = Reservation.objects.create(
                client=self.client,
                package=self.package,
                departure_date=timezone.now().date(),
                return_date=timezone.now().date() - timezone.timedelta(days=1),
                adult_price=Money(1000, 'MAD')
            )
            reservation.full_clean()

    def test_reservation_participants_validation(self):
        # Test invalid participant numbers
        with self.assertRaises(ValidationError):
            reservation = Reservation(
                client=self.client,
                package=self.package,
                departure_date=timezone.now().date(),
                return_date=timezone.now().date() + timezone.timedelta(days=5),
                adults=0,  # Invalid: must have at least one adult
                adult_price=Money(1000, 'MAD')
            )
            reservation.full_clean()

    def test_reservation_update(self):
        # Test reservation update operations
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD'),
            status='pending'
        )

        # Update status
        reservation.status = 'confirmed'
        reservation.save()
        self.assertEqual(Reservation.objects.get(id=reservation.id).status, 'confirmed')

        # Update prices
        reservation.adult_price = Money(1200, 'MAD')
        reservation.save()
        self.assertEqual(float(Reservation.objects.get(id=reservation.id).adult_price.amount), 1200.0)

    def test_add_reservation_participant(self):
        # Test adding participants to reservation
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD')
        )

        participant = ReservationParticipant.objects.create(
            reservation=reservation,
            first_name="John",
            last_name="Doe",
            date_of_birth=timezone.now().date() - timezone.timedelta(days=365*30),
            participant_type='adult',
            passport_number="A123456"
        )
        self.assertEqual(reservation.participants.count(), 1)
        self.assertEqual(participant.reservation, reservation)

    def test_add_reservation_service(self):
        # Test adding services to reservation
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD')
        )

        service = ReservationService.objects.create(
            reservation=reservation,
            service_type='transfer',
            name='Airport Transfer',
            unit_price=Money(200, 'MAD'),
            quantity=2
        )
        self.assertEqual(reservation.services.count(), 1)
        self.assertEqual(float(service.total_price.amount), 400.0)

    def test_add_reservation_document(self):
        # Test adding documents to reservation
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD')
        )

        document = ReservationDocument.objects.create(
            reservation=reservation,
            document_type='passport',
            title='Passport Copy',
            is_public=False
        )
        self.assertEqual(reservation.documents.count(), 1)
        self.assertEqual(document.document_type, 'passport')

    def test_reservation_status_transitions(self):
        # Test valid status transitions
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD'),
            status='pending'
        )

        # Test status transitions
        for new_status in ['confirmed', 'completed']:
            reservation.status = new_status
            reservation.save()
            reserved = Reservation.objects.get(id=reservation.id)
            self.assertEqual(reserved.status, new_status)

    def test_multilingual_notes(self):
        # Test notes in different languages
        notes = {
            'ar': "ملاحظات",
            'fr': "Notes en français",
            'en': "Notes in English"
        }

        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD'),
            notes_ar=notes['ar'],
            notes_fr=notes['fr'],
            notes_en=notes['en']
        )

        self.assertEqual(reservation.notes_ar, notes['ar'])
        self.assertEqual(reservation.notes_fr, notes['fr'])
        self.assertEqual(reservation.notes_en, notes['en'])

    def test_max_participants(self):
        # Test exceeding package max participants
        with self.assertRaises(ValidationError):
            res = Reservation(
                client=self.client,
                package=self.package,  # max_participants is 10
                departure_date=timezone.now().date(),
                return_date=timezone.now().date() + timezone.timedelta(days=5),
                adults=9,
                children=2,  # Total 11 participants > max 10
                adult_price=Money(1000, 'MAD')
            )
            res.full_clean()

    def test_edge_case_pricing(self):
        # Test edge cases with pricing
        prices = {
            'adult': 999.99,
            'child': 499.99,
            'infant': 0,  # Free infant
            'discount': 99.99,
            'tax': 199.99
        }

        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=1,
            children=1,
            infants=1,
            adult_price=Money(prices['adult'], 'MAD'),
            child_price=Money(prices['child'], 'MAD'),
            infant_price=Money(prices['infant'], 'MAD'),
            discount_amount=Money(prices['discount'], 'MAD'),
            tax_amount=Money(prices['tax'], 'MAD')
        )

        expected = {
            'subtotal': prices['adult'] + prices['child'] + prices['infant'],
            'total': 0
        }
        expected['total'] = (
            expected['subtotal'] - prices['discount'] + prices['tax']
        )

        self.assertAlmostEqual(
            float(reservation.subtotal.amount),
            expected['subtotal'],
            places=2
        )
        self.assertAlmostEqual(
            float(reservation.total_amount.amount),
            expected['total'],
            places=2
        )

    def test_participant_type_validation(self):
        # Test participant type validation
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=1,
            adult_price=Money(1000, 'MAD')
        )

        # Test dates for different age groups
        adult_dob = timezone.now().date() - timezone.timedelta(days=365*25)
        child_dob = timezone.now().date() - timezone.timedelta(days=365*8)
        infant_dob = timezone.now().date() - timezone.timedelta(days=180)

        # Valid adult participant
        adult = ReservationParticipant.objects.create(
            reservation=reservation,
            first_name="John",
            last_name="Doe",
            date_of_birth=adult_dob,
            participant_type='adult'
        )
        self.assertEqual(adult.participant_type, 'adult')

        # Valid child participant
        child = ReservationParticipant.objects.create(
            reservation=reservation,
            first_name="Jane",
            last_name="Doe",
            date_of_birth=child_dob,
            participant_type='child'
        )
        self.assertEqual(child.participant_type, 'child')

        # Valid infant participant
        infant = ReservationParticipant.objects.create(
            reservation=reservation,
            first_name="Baby",
            last_name="Doe",
            date_of_birth=infant_dob,
            participant_type='infant'
        )
        self.assertEqual(infant.participant_type, 'infant')

    def test_service_calculations(self):
        # Test complex service calculations
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD')
        )

        # Add multiple services
        services = [
            ('transfer', 'Airport Transfer', 200, 2),
            ('activity', 'Desert Tour', 500, 2),
            ('accommodation', 'Extra Night', 800, 1)
        ]

        for stype, name, price, qty in services:
            ReservationService.objects.create(
                reservation=reservation,
                service_type=stype,
                name=name,
                unit_price=Money(price, 'MAD'),
                quantity=qty
            )

        # Verify total services amount
        services_total = sum(
            float(service.total_price.amount)
            for service in reservation.services.all()
        )
        expected_total = (200 * 2) + (500 * 2) + (800 * 1)
        self.assertEqual(services_total, float(expected_total))

    def test_participant_types(self):
        # Test participant type validation
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=1,
            adult_price=Money(1000, 'MAD')
        )

        # Calculate birthdates for different age groups
        birthdates = {
            'adult': timezone.timedelta(days=365 * 25),
            'child': timezone.timedelta(days=365 * 8),
            'infant': timezone.timedelta(days=180)
        }

        participants = [
            ('John', 'Doe', 'adult'),
            ('Jane', 'Doe', 'child'),
            ('Baby', 'Doe', 'infant')
        ]

        for first, last, ptype in participants:
            dob = timezone.now().date() - birthdates[ptype]
            participant = ReservationParticipant.objects.create(
                reservation=reservation,
                first_name=first,
                last_name=last,
                date_of_birth=dob,
                participant_type=ptype
            )
            self.assertEqual(participant.participant_type, ptype)

    def test_service_calculations(self):
        # Test complex service calculations
        reservation = Reservation.objects.create(
            client=self.client,
            package=self.package,
            departure_date=timezone.now().date(),
            return_date=timezone.now().date() + timezone.timedelta(days=5),
            adults=2,
            adult_price=Money(1000, 'MAD')
        )

        # Define services with their prices and quantities
        services = [
            ('transfer', 'Airport Transfer', 200, 2),
            ('activity', 'Desert Tour', 500, 2),
            ('accommodation', 'Extra Night', 800, 1)
        ]

        # Add services and verify individual prices
        for stype, name, price, qty in services:
            service = ReservationService.objects.create(
                reservation=reservation,
                service_type=stype,
                name=name,
                unit_price=Money(price, 'MAD'),
                quantity=qty
            )
            self.assertEqual(
                float(service.total_price.amount),
                price * qty
            )

        # Verify total services amount
        total = sum(
            float(s.total_price.amount)
            for s in reservation.services.all()
        )
        expected = sum(price * qty for _, _, price, qty in services)
        self.assertEqual(total, expected)
