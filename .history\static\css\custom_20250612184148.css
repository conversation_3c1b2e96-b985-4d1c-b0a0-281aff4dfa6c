/* Custom CSS for Moroccan Travel Agency ERP */

/* Arabic Font Support */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

/* Root Variables */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --morocco-red: #c1272d;
    --morocco-green: #006233;
}

/* Body and Typography */
body {
    font-family: 'Noto Sans Arabic', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    background-color: #f8f9fa;
}

/* RTL Support */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .navbar-brand {
    margin-right: 0;
    margin-left: auto;
}

/* Custom Card Styles */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: rgba(13, 110, 253, 0.1);
    border-bottom: 1px solid rgba(13, 110, 253, 0.2);
    border-radius: 10px 10px 0 0 !important;
}

/* Statistics Cards */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.stats-card .stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card .stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Navigation Enhancements */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    transform: translateY(-1px);
}

/* Button Styles */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-morocco {
    background-color: var(--morocco-red);
    border-color: var(--morocco-red);
    color: white;
}

.btn-morocco:hover {
    background-color: #a01e23;
    border-color: #a01e23;
    color: white;
}

/* Form Styles */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* Table Styles */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.table thead th {
    background-color: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
    transform: scale(1.01);
}

/* Badge Styles */
.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
    border-radius: 6px;
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 10px;
    border-left: 4px solid;
}

.alert-success {
    border-left-color: var(--success-color);
    background-color: rgba(25, 135, 84, 0.1);
}

.alert-danger {
    border-left-color: var(--danger-color);
    background-color: rgba(220, 53, 69, 0.1);
}

.alert-warning {
    border-left-color: var(--warning-color);
    background-color: rgba(255, 193, 7, 0.1);
}

.alert-info {
    border-left-color: var(--info-color);
    background-color: rgba(13, 202, 240, 0.1);
}

/* Loading Spinner */
.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner-border-custom {
    width: 3rem;
    height: 3rem;
    border-width: 0.3em;
}

/* Sidebar (for future use) */
.sidebar {
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: white;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1rem;
    border-radius: 8px;
    margin-bottom: 0.25rem;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

/* Footer */
footer {
    margin-top: auto;
    background-color: #f8f9fa !important;
    border-top: 1px solid #dee2e6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }

    .stats-card .stats-number {
        font-size: 2rem;
    }

    .table-responsive {
        border-radius: 10px;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .sidebar,
    footer,
    .btn,
    .no-print {
        display: none !important;
    }

    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }

    body {
        background-color: white;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

/* Custom Utilities */
.text-morocco-red {
    color: var(--morocco-red) !important;
}

.text-morocco-green {
    color: var(--morocco-green) !important;
}

.bg-morocco-red {
    background-color: var(--morocco-red) !important;
}

.bg-morocco-green {
    background-color: var(--morocco-green) !important;
}

.border-morocco-red {
    border-color: var(--morocco-red) !important;
}

.border-morocco-green {
    border-color: var(--morocco-green) !important;
}
