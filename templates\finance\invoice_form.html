{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}
{% if form.instance.pk %}تعديل الفاتورة {{ form.instance.invoice_number }}{% else %}إنشاء فاتورة جديدة{% endif %}
{% endblock %}

{% block extra_css %}
<style>
.form-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
}

.btn-save {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-save:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(17, 153, 142, 0.4);
    color: white;
}

.btn-cancel {
    background: #6c757d;
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-cancel:hover {
    background: #5a6268;
    color: white;
}

.form-section {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 25px;
    border-left: 4px solid #667eea;
}

.form-section h5 {
    color: #667eea;
    margin-bottom: 20px;
}

.invoice-items-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.item-row {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    border: 1px solid #e9ecef;
}

.btn-add-item {
    background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
}

.btn-remove-item {
    background: #dc3545;
    border: none;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
}

.calculation-summary {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="form-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-2">
                    {% if form.instance.pk %}
                    ✏️ تعديل الفاتورة {{ form.instance.invoice_number }}
                    {% else %}
                    ➕ إنشاء فاتورة جديدة
                    {% endif %}
                </h1>
                <p class="mb-0 opacity-75">إدارة تفاصيل الفاتورة والعناصر</p>
            </div>
            <div>
                <a href="{% url 'finance:invoice_list' %}" class="btn btn-outline-light">
                    <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <form method="post" id="invoiceForm">
        {% csrf_token %}

        <div class="row">
            <!-- Main Form -->
            <div class="col-lg-8">
                <div class="form-container">
                    <!-- Basic Information -->
                    <div class="form-section">
                        <h5><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h5>
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.client|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.reservation|as_crispy_field }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.issue_date|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.due_date|as_crispy_field }}
                            </div>
                        </div>
                    </div>

                    <!-- Financial Details -->
                    <div class="form-section">
                        <h5><i class="fas fa-calculator me-2"></i>التفاصيل المالية</h5>
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.subtotal|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.tax_rate|as_crispy_field }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.tax_amount|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.discount_amount|as_crispy_field }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.total_amount|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.status|as_crispy_field }}
                            </div>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="form-section">
                        <h5><i class="fas fa-sticky-note me-2"></i>ملاحظات</h5>
                        {{ form.notes|as_crispy_field }}
                    </div>
                </div>

                <!-- Invoice Items -->
                <div class="invoice-items-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5><i class="fas fa-list me-2"></i>عناصر الفاتورة</h5>
                        <button type="button" class="btn btn-add-item" onclick="addInvoiceItem()">
                            <i class="fas fa-plus me-2"></i>إضافة عنصر
                        </button>
                    </div>

                    <div id="invoice-items">
                        <!-- Invoice items will be added here dynamically -->
                        <div class="item-row" id="item-template" style="display: none;">
                            <div class="row align-items-end">
                                <div class="col-md-4">
                                    <label class="form-label">الوصف</label>
                                    <input type="text" class="form-control item-description" name="item_description[]" placeholder="وصف الخدمة...">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">الكمية</label>
                                    <input type="number" class="form-control item-quantity" name="item_quantity[]" min="1" value="1" onchange="calculateItemTotal(this)">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">سعر الوحدة</label>
                                    <input type="number" class="form-control item-price" name="item_price[]" step="0.01" onchange="calculateItemTotal(this)">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">الإجمالي</label>
                                    <input type="number" class="form-control item-total" name="item_total[]" readonly>
                                </div>
                                <div class="col-md-2">
                                    <button type="button" class="btn btn-remove-item" onclick="removeInvoiceItem(this)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Calculation Summary -->
                    <div class="calculation-summary">
                        <div class="row">
                            <div class="col-md-6 offset-md-6">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>المجموع الفرعي:</span>
                                    <span id="subtotal-display">0.00 درهم</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الضريبة:</span>
                                    <span id="tax-display">0.00 درهم</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الخصم:</span>
                                    <span id="discount-display">0.00 درهم</span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <strong>المبلغ الإجمالي:</strong>
                                    <strong id="total-display" class="text-primary">0.00 درهم</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Quick Actions -->
                <div class="form-container">
                    <h5 class="mb-4"><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h5>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-save">
                            <i class="fas fa-save me-2"></i>
                            {% if form.instance.pk %}حفظ التعديلات{% else %}إنشاء الفاتورة{% endif %}
                        </button>

                        {% if form.instance.pk %}
                        <button type="button" class="btn btn-outline-primary" onclick="previewInvoice()">
                            <i class="fas fa-eye me-2"></i>معاينة
                        </button>
                        {% endif %}

                        <a href="{% url 'finance:invoice_list' %}" class="btn btn-cancel">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                    </div>
                </div>

                <!-- Help -->
                <div class="form-container">
                    <h6 class="mb-3"><i class="fas fa-question-circle me-2"></i>مساعدة</h6>
                    <ul class="list-unstyled small text-muted">
                        <li class="mb-2">
                            <i class="fas fa-info-circle me-2"></i>
                            اختر العميل والحجز المرتبط بالفاتورة
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-calculator me-2"></i>
                            سيتم حساب الضريبة والإجمالي تلقائياً
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-list me-2"></i>
                            أضف عناصر الفاتورة مع الأسعار
                        </li>
                        <li>
                            <i class="fas fa-save me-2"></i>
                            احفظ كمسودة أو أرسل مباشرة
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
let itemCounter = 0;

// Add invoice item
function addInvoiceItem() {
    const template = document.getElementById('item-template');
    const clone = template.cloneNode(true);
    clone.id = 'item-' + (++itemCounter);
    clone.style.display = 'block';

    // Update input names to include counter
    const inputs = clone.querySelectorAll('input');
    inputs.forEach(input => {
        input.name = input.name.replace('[]', '[' + itemCounter + ']');
    });

    document.getElementById('invoice-items').appendChild(clone);
}

// Remove invoice item
function removeInvoiceItem(button) {
    button.closest('.item-row').remove();
    calculateTotals();
}

// Calculate item total
function calculateItemTotal(input) {
    const row = input.closest('.item-row');
    const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
    const price = parseFloat(row.querySelector('.item-price').value) || 0;
    const total = quantity * price;

    row.querySelector('.item-total').value = total.toFixed(2);
    calculateTotals();
}

// Calculate all totals
function calculateTotals() {
    let subtotal = 0;

    // Sum all item totals
    document.querySelectorAll('.item-total').forEach(input => {
        subtotal += parseFloat(input.value) || 0;
    });

    // Get tax rate and discount
    const taxRate = parseFloat(document.querySelector('[name="tax_rate"]').value) || 0;
    const discountAmount = parseFloat(document.querySelector('[name="discount_amount"]').value) || 0;

    // Calculate tax and total
    const taxAmount = (subtotal * taxRate) / 100;
    const total = subtotal + taxAmount - discountAmount;

    // Update form fields
    document.querySelector('[name="subtotal"]').value = subtotal.toFixed(2);
    document.querySelector('[name="tax_amount"]').value = taxAmount.toFixed(2);
    document.querySelector('[name="total_amount"]').value = total.toFixed(2);

    // Update display
    document.getElementById('subtotal-display').textContent = subtotal.toFixed(2) + ' درهم';
    document.getElementById('tax-display').textContent = taxAmount.toFixed(2) + ' درهم';
    document.getElementById('discount-display').textContent = discountAmount.toFixed(2) + ' درهم';
    document.getElementById('total-display').textContent = total.toFixed(2) + ' درهم';
}

// Auto-calculate when tax rate or discount changes
document.querySelector('[name="tax_rate"]')?.addEventListener('input', calculateTotals);
document.querySelector('[name="discount_amount"]')?.addEventListener('input', calculateTotals);

// Add first item on page load
document.addEventListener('DOMContentLoaded', function() {
    addInvoiceItem();
});

// Preview invoice
function previewInvoice() {
    const invoiceId = '{{ form.instance.pk }}';
    if (invoiceId) {
        window.open(`/finance/invoices/${invoiceId}/`, '_blank');
    }
}

// Form validation
document.getElementById('invoiceForm').addEventListener('submit', function(e) {
    const items = document.querySelectorAll('.item-row:not(#item-template)');
    if (items.length === 0) {
        e.preventDefault();
        alert('يجب إضافة عنصر واحد على الأقل للفاتورة');
        return false;
    }

    // Validate that all items have description and price
    let valid = true;
    items.forEach(item => {
        const description = item.querySelector('.item-description').value.trim();
        const price = item.querySelector('.item-price').value;

        if (!description || !price) {
            valid = false;
        }
    });

    if (!valid) {
        e.preventDefault();
        alert('يجب ملء جميع تفاصيل العناصر (الوصف والسعر)');
        return false;
    }
});
</script>
{% endblock %}
