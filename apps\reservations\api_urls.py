"""
API URL configuration for reservations app.
"""
from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import api_views

router = DefaultRouter()
router.register(r'reservations', api_views.ReservationViewSet)
router.register(r'participants', api_views.ReservationParticipantViewSet)
router.register(r'services', api_views.ReservationServiceViewSet)
router.register(r'documents', api_views.ReservationDocumentViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('reservations/<int:reservation_id>/participants/', api_views.ReservationParticipantListView.as_view(), name='reservation-participants'),
    path('reservations/<int:reservation_id>/services/', api_views.ReservationServiceListView.as_view(), name='reservation-services'),
    path('reservations/<int:reservation_id>/documents/', api_views.ReservationDocumentListView.as_view(), name='reservation-documents'),
    path('availability-check/', api_views.AvailabilityCheckView.as_view(), name='availability-check'),
]
