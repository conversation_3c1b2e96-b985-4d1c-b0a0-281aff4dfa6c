{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}قائمة الفواتير{% endblock %}

{% block extra_css %}
<style>
.invoice-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border-left: 4px solid #667eea;
}

.invoice-card.paid {
    border-left-color: #38ef7d;
}

.invoice-card.overdue {
    border-left-color: #ff6b6b;
    background: linear-gradient(135deg, rgba(255,107,107,0.05) 0%, rgba(255,107,107,0.02) 100%);
}

.filter-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.btn-finance {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.btn-finance:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.status-draft {
    background: #f8f9fa;
    color: #6c757d;
}

.status-sent {
    background: #fff3cd;
    color: #856404;
}

.status-paid {
    background: #d1edff;
    color: #0c63e4;
}

.status-overdue {
    background: #f8d7da;
    color: #721c24;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">💰 قائمة الفواتير</h1>
                    <p class="text-muted">إدارة جميع الفواتير والمدفوعات</p>
                </div>
                <div>
                    <a href="{% url 'finance:dashboard' %}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
                    </a>
                    <button class="btn btn-finance">
                        <i class="fas fa-plus"></i> فاتورة جديدة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="filter-card">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">الحالة</label>
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if request.GET.status == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" name="date_from" class="form-control" value="{{ request.GET.date_from }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" name="date_to" class="form-control" value="{{ request.GET.date_to }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">بحث</label>
                        <div class="input-group">
                            <input type="text" name="search" class="form-control"
                                   placeholder="رقم الفاتورة أو اسم العميل..."
                                   value="{{ request.GET.search }}">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Invoices List -->
    <div class="row">
        {% for invoice in invoices %}
        <div class="col-lg-6 col-md-12">
            <div class="invoice-card {% if invoice.status == 'paid' %}paid{% elif invoice.is_overdue %}overdue{% endif %}">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h5 class="mb-1">{{ invoice.invoice_number }}</h5>
                        <p class="text-muted mb-0">{{ invoice.client.full_name_ar }}</p>
                    </div>
                    <div class="text-end">
                        <span class="status-badge status-{{ invoice.status }}">
                            {{ invoice.get_status_display }}
                        </span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">تاريخ الإصدار</small>
                        <p class="mb-0">{{ invoice.issue_date }}</p>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">تاريخ الاستحقاق</small>
                        <p class="mb-0 {% if invoice.is_overdue %}text-danger{% endif %}">
                            {{ invoice.due_date }}
                            {% if invoice.is_overdue %}
                                <i class="fas fa-exclamation-triangle text-danger ms-1"></i>
                            {% endif %}
                        </p>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">المبلغ الإجمالي</small>
                        <h4 class="mb-0 text-primary">{{ invoice.total_amount }} درهم</h4>
                    </div>
                    <div class="col-6">
                        {% if invoice.reservation %}
                        <small class="text-muted">الحجز</small>
                        <p class="mb-0">{{ invoice.reservation.reservation_number }}</p>
                        {% endif %}
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        {% if invoice.status == 'draft' %}
                        <button class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-paper-plane"></i> إرسال
                        </button>
                        {% elif invoice.status == 'sent' %}
                        <button class="btn btn-sm btn-outline-success">
                            <i class="fas fa-check"></i> تأكيد الدفع
                        </button>
                        {% endif %}
                    </div>
                    <div>
                        <a href="{% url 'finance:invoice_detail' invoice.pk %}" class="btn btn-sm btn-outline-info">
                            <i class="fas fa-eye"></i> عرض
                        </a>
                        <button class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-download"></i> PDF
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-file-invoice fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد فواتير</h4>
                <p class="text-muted">لم يتم العثور على أي فواتير تطابق معايير البحث</p>
                <button class="btn btn-finance">
                    <i class="fas fa-plus"></i> إنشاء فاتورة جديدة
                </button>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="صفحات الفواتير">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">
                            {{ num }}
                        </a>
                    </li>
                    {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}

    <!-- Summary -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="filter-card">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h5 class="text-primary">{{ page_obj.paginator.count }}</h5>
                        <p class="text-muted mb-0">إجمالي الفواتير</p>
                    </div>
                    <div class="col-md-3">
                        <h5 class="text-success">{{ invoices|length }}</h5>
                        <p class="text-muted mb-0">في هذه الصفحة</p>
                    </div>
                    <div class="col-md-3">
                        <h5 class="text-info">{{ page_obj.paginator.num_pages }}</h5>
                        <p class="text-muted mb-0">عدد الصفحات</p>
                    </div>
                    <div class="col-md-3">
                        <h5 class="text-warning">{{ page_obj.number }}</h5>
                        <p class="text-muted mb-0">الصفحة الحالية</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit form on filter change
document.querySelectorAll('select[name="status"]').forEach(function(select) {
    select.addEventListener('change', function() {
        this.form.submit();
    });
});

// Clear filters
function clearFilters() {
    window.location.href = '{% url "finance:invoice_list" %}';
}
</script>
{% endblock %}
