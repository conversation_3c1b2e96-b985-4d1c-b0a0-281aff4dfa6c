"""
Forms for Tours app.
"""
from django import forms
from django.core.exceptions import ValidationError
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Row, Column, Submit, HTML, Div, Field
from crispy_forms.bootstrap import TabHolder, Tab
from .models import TourPackage, TourCategory, Destination


class TourPackageForm(forms.ModelForm):
    """Form for creating and updating tour packages."""

    class Meta:
        model = TourPackage
        fields = [
            'title_ar', 'title_fr', 'title_en',
            'short_description_ar', 'short_description_fr', 'short_description_en',
            'detailed_description_ar', 'detailed_description_fr', 'detailed_description_en',
            'category', 'destinations', 'duration_days', 'duration_nights',
            'max_participants', 'min_participants',
            'base_price', 'child_price', 'infant_price',
            'inclusions', 'exclusions', 'requirements',
            'is_active', 'is_featured', 'is_bestseller',
            'difficulty_level', 'physical_rating', 'main_image'
        ]
        widgets = {
            'title_ar': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'عنوان الباقة بالعربية'}),
            'title_en': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Package Title in English'}),
            'description_ar': forms.Textarea(attrs={'rows': 4, 'class': 'form-control', 'placeholder': 'وصف الباقة بالعربية...'}),
            'description_en': forms.Textarea(attrs={'rows': 4, 'class': 'form-control', 'placeholder': 'Package description in English...'}),
            'duration': forms.NumberInput(attrs={'class': 'form-control', 'min': 1, 'max': 365}),
            'max_participants': forms.NumberInput(attrs={'class': 'form-control', 'min': 1, 'max': 100}),
            'price_per_person': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': 0}),
            'child_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': 0}),
            'infant_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': 0}),
            'includes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control', 'placeholder': 'ما يشمله السعر...'}),
            'excludes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control', 'placeholder': 'ما لا يشمله السعر...'}),
            'itinerary': forms.Textarea(attrs={'rows': 5, 'class': 'form-control', 'placeholder': 'البرنامج اليومي للرحلة...'}),
            'requirements': forms.Textarea(attrs={'rows': 3, 'class': 'form-control', 'placeholder': 'المتطلبات والشروط...'}),
            'cancellation_policy': forms.Textarea(attrs={'rows': 3, 'class': 'form-control', 'placeholder': 'سياسة الإلغاء...'}),
            'image': forms.FileInput(attrs={'class': 'form-control', 'accept': 'image/*'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Make required fields
        self.fields['title_ar'].required = True
        self.fields['category'].required = True
        self.fields['destination'].required = True
        self.fields['duration'].required = True
        self.fields['price_per_person'].required = True

        # Set up crispy forms
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_enctype = 'multipart/form-data'
        self.helper.form_class = 'form-horizontal'

        self.helper.layout = Layout(
            TabHolder(
                Tab('المعلومات الأساسية',
                    HTML('<h5 class="mb-4"><i class="fas fa-info-circle me-2"></i>معلومات الباقة</h5>'),
                    Row(
                        Column('title_ar', css_class='form-group col-md-6 mb-3'),
                        Column('title_en', css_class='form-group col-md-6 mb-3'),
                        css_class='form-row'
                    ),
                    'description_ar',
                    'description_en',
                    Row(
                        Column('category', css_class='form-group col-md-4 mb-3'),
                        Column('destination', css_class='form-group col-md-4 mb-3'),
                        Column('duration', css_class='form-group col-md-4 mb-3'),
                        css_class='form-row'
                    ),
                    'image',
                ),

                Tab('التسعير والمشاركين',
                    HTML('<h5 class="mb-4"><i class="fas fa-money-bill me-2"></i>معلومات التسعير</h5>'),
                    Row(
                        Column('price_per_person', css_class='form-group col-md-4 mb-3'),
                        Column('child_price', css_class='form-group col-md-4 mb-3'),
                        Column('infant_price', css_class='form-group col-md-4 mb-3'),
                        css_class='form-row'
                    ),
                    Row(
                        Column('max_participants', css_class='form-group col-md-6 mb-3'),
                        Column('difficulty_level', css_class='form-group col-md-6 mb-3'),
                        css_class='form-row'
                    ),
                    'season',
                ),

                Tab('تفاصيل الباقة',
                    HTML('<h5 class="mb-4"><i class="fas fa-list me-2"></i>تفاصيل الخدمات</h5>'),
                    'includes',
                    'excludes',
                    HTML('<h5 class="mb-4 mt-4"><i class="fas fa-route me-2"></i>البرنامج</h5>'),
                    'itinerary',
                ),

                Tab('الشروط والسياسات',
                    HTML('<h5 class="mb-4"><i class="fas fa-file-contract me-2"></i>المتطلبات والشروط</h5>'),
                    'requirements',
                    'cancellation_policy',
                    HTML('<h5 class="mb-4 mt-4"><i class="fas fa-cog me-2"></i>إعدادات الباقة</h5>'),
                    Row(
                        Column('is_active', css_class='form-group col-md-6 mb-3'),
                        Column('is_featured', css_class='form-group col-md-6 mb-3'),
                        css_class='form-row'
                    ),
                ),
            ),

            HTML('<hr>'),
            Div(
                Submit('submit', 'حفظ الباقة', css_class='btn btn-primary btn-lg me-2'),
                HTML('<a href="{% url "tours:package_list" %}" class="btn btn-secondary btn-lg">إلغاء</a>'),
                css_class='text-center mt-4'
            )
        )

    def clean_price_per_person(self):
        """Validate price per person."""
        price = self.cleaned_data.get('price_per_person')
        if price and price <= 0:
            raise ValidationError('سعر الشخص يجب أن يكون أكبر من صفر')
        return price

    def clean_child_price(self):
        """Validate child price."""
        child_price = self.cleaned_data.get('child_price')
        price_per_person = self.cleaned_data.get('price_per_person')

        if child_price and price_per_person and child_price > price_per_person:
            raise ValidationError('سعر الطفل لا يمكن أن يكون أكبر من سعر البالغ')

        return child_price

    def clean_infant_price(self):
        """Validate infant price."""
        infant_price = self.cleaned_data.get('infant_price')
        child_price = self.cleaned_data.get('child_price')

        if infant_price and child_price and infant_price > child_price:
            raise ValidationError('سعر الرضيع لا يمكن أن يكون أكبر من سعر الطفل')

        return infant_price

    def clean_duration(self):
        """Validate duration."""
        duration = self.cleaned_data.get('duration')
        if duration and duration <= 0:
            raise ValidationError('مدة الرحلة يجب أن تكون أكبر من صفر')
        return duration


class TourPackageSearchForm(forms.Form):
    """Form for searching tour packages."""

    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث بالعنوان أو الوصف...',
        })
    )

    category = forms.ModelChoiceField(
        queryset=TourCategory.objects.all(),
        required=False,
        empty_label='جميع الفئات',
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    destination = forms.ModelChoiceField(
        queryset=Destination.objects.all(),
        required=False,
        empty_label='جميع الوجهات',
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    is_active = forms.ChoiceField(
        choices=[('', 'الكل'), (True, 'نشطة'), (False, 'غير نشطة')],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.helper = FormHelper()
        self.helper.form_method = 'get'
        self.helper.form_class = 'form-inline'
        self.helper.layout = Layout(
            Row(
                Column('search', css_class='form-group col-md-4 mb-2'),
                Column('category', css_class='form-group col-md-2 mb-2'),
                Column('destination', css_class='form-group col-md-2 mb-2'),
                Column('is_active', css_class='form-group col-md-2 mb-2'),
                Column(
                    Submit('submit', 'بحث', css_class='btn btn-primary'),
                    css_class='form-group col-md-2 mb-2'
                ),
                css_class='form-row'
            )
        )


class TourPackageQuickAddForm(forms.ModelForm):
    """Quick form for adding basic package information."""

    class Meta:
        model = TourPackage
        fields = ['title_ar', 'category', 'destination', 'duration', 'price_per_person']
        widgets = {
            'title_ar': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'عنوان الباقة'}),
            'duration': forms.NumberInput(attrs={'class': 'form-control', 'min': 1}),
            'price_per_person': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': 0}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Make all fields required
        for field in self.fields:
            self.fields[field].required = True

        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            'title_ar',
            Row(
                Column('category', css_class='form-group col-md-6 mb-3'),
                Column('destination', css_class='form-group col-md-6 mb-3'),
                css_class='form-row'
            ),
            Row(
                Column('duration', css_class='form-group col-md-6 mb-3'),
                Column('price_per_person', css_class='form-group col-md-6 mb-3'),
                css_class='form-row'
            ),
            Submit('submit', 'إضافة باقة', css_class='btn btn-success btn-block mt-3')
        )
