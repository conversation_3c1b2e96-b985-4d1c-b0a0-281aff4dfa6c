"""
Admin configuration for accounts models.
"""
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _
from .models import User, Role


@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    """Admin interface for Role model."""
    list_display = ('name', 'description')
    search_fields = ('name', 'description')
    ordering = ('name',)


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Admin interface for User model with custom fields."""
    list_display = (
        'username',
        'full_name_ar',
        'email',
        'system_role',
        'is_active'
    )
    list_filter = ('roles', 'system_role', 'is_active', 'is_staff')
    search_fields = (
        'username',
        'first_name_ar',
        'last_name_ar',
        'email',
        'employee_id'
    )
    ordering = ('username',)

    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        (_('معلومات شخصية'), {
            'fields': (
                'first_name',
                'last_name',
                'first_name_ar',
                'last_name_ar',
                'email'
            )
        }),
        (_('معلومات الاتصال'), {
            'fields': ('phone',)
        }),
        (_('معلومات العمل'), {
            'fields': ('roles', 'system_role', 'employee_id')
        }),
        (_('التفضيلات'), {
            'fields': ('preferred_language',)
        }),
        (_('الصلاحيات'), {
            'fields': (
                'is_active',
                'is_staff',
                'is_superuser',
                'groups',
                'user_permissions'
            ),
        }),
        (_('تواريخ مهمة'), {'fields': ('last_login', 'date_joined')}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': (
                'username',
                'first_name_ar',
                'last_name_ar',
                'email',
                'password1',
                'password2',
                'roles',
                'system_role'
            ),
        }),
    )

# Simplified admin - complex models removed for basic setup
