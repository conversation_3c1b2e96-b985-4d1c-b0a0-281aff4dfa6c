import os

def fix_whitespace(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    fixed = False
    fixed_lines = []
    for line in lines:
        new_line = line.rstrip() + '\n' if line.strip() else '\n'
        if new_line != line:
            fixed = True
        fixed_lines.append(new_line)

    if fixed:
        print(f"Fixing whitespace in {file_path}")
        with open(file_path, 'w', encoding='utf-8', newline='\n') as f:
            f.writelines(fixed_lines)

def walk_dir(dir_path):
    for root, _, files in os.walk(dir_path):
        for file in files:
            if file.endswith(('.py', '.html', '.js', '.css')):
                file_path = os.path.join(root, file)
                fix_whitespace(file_path)

if __name__ == '__main__':
    base_dir = os.path.dirname(os.path.abspath(__file__))
    walk_dir(base_dir)
