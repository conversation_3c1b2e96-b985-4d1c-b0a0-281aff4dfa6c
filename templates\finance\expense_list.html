{% extends 'base.html' %}
{% load static %}

{% block title %}المصروفات{% endblock %}

{% block extra_css %}
<style>
    .expense-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: transform 0.3s ease;
    }
    
    .expense-card:hover {
        transform: translateY(-2px);
    }
    
    .expense-status {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: bold;
    }
    
    .status-approved {
        background: #d4edda;
        color: #155724;
    }
    
    .status-pending {
        background: #fff3cd;
        color: #856404;
    }
    
    .status-rejected {
        background: #f8d7da;
        color: #721c24;
    }
    
    .status-paid {
        background: #d1ecf1;
        color: #0c5460;
    }
    
    .expense-amount {
        font-size: 1.5rem;
        font-weight: bold;
        color: #dc3545;
    }
    
    .expense-category-icon {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 15px;
        font-size: 1.5rem;
    }
    
    .category-travel { background: #007bff; color: white; }
    .category-office { background: #28a745; color: white; }
    .category-marketing { background: #ffc107; color: black; }
    .category-utilities { background: #6f42c1; color: white; }
    .category-other { background: #6c757d; color: white; }
    
    .stats-row {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
    }
    
    .search-form {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .receipt-indicator {
        position: absolute;
        top: 10px;
        right: 10px;
        background: #28a745;
        color: white;
        border-radius: 50%;
        width: 25px;
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-receipt text-primary me-2"></i>
                        المصروفات
                    </h1>
                    <p class="text-muted mb-0">إدارة وعرض جميع المصروفات</p>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="alert('ميزة إضافة المصروفات قيد التطوير')">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مصروف جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-row">
        <div class="row text-center">
            <div class="col-md-3">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="me-3">
                        <i class="fas fa-receipt fa-2x text-primary"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">{{ expenses.count }}</h4>
                        <small class="text-muted">إجمالي المصروفات</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="me-3">
                        <i class="fas fa-check-circle fa-2x text-success"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">0</h4>
                        <small class="text-muted">معتمدة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="me-3">
                        <i class="fas fa-clock fa-2x text-warning"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">0</h4>
                        <small class="text-muted">معلقة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-flex align-items-center justify-content-center">
                    <div class="me-3">
                        <i class="fas fa-money-bill-wave fa-2x text-danger"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">0 درهم</h4>
                        <small class="text-muted">إجمالي المبلغ</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Form -->
    <div class="search-form">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       placeholder="ابحث في المصروفات..." value="{{ request.GET.search }}">
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="pending">معلقة</option>
                    <option value="approved">معتمدة</option>
                    <option value="rejected">مرفوضة</option>
                    <option value="paid">مدفوعة</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="category" class="form-label">الفئة</label>
                <select class="form-select" id="category" name="category">
                    <option value="">جميع الفئات</option>
                    <option value="travel">سفر</option>
                    <option value="office">مكتب</option>
                    <option value="marketing">تسويق</option>
                    <option value="utilities">مرافق</option>
                    <option value="other">أخرى</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="date_from" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="date_from" name="date_from" 
                       value="{{ request.GET.date_from }}">
            </div>
            <div class="col-md-2">
                <label for="date_to" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="date_to" name="date_to" 
                       value="{{ request.GET.date_to }}">
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Expenses List -->
    <div class="row">
        {% for expense in expenses %}
        <div class="col-lg-6 mb-4">
            <div class="expense-card position-relative">
                {% if expense.receipt %}
                    <div class="receipt-indicator" title="يوجد إيصال">
                        <i class="fas fa-paperclip"></i>
                    </div>
                {% endif %}
                
                <div class="d-flex align-items-center mb-3">
                    <div class="expense-category-icon category-{{ expense.category|default:'other' }}">
                        {% if expense.category == 'travel' %}
                            <i class="fas fa-plane"></i>
                        {% elif expense.category == 'office' %}
                            <i class="fas fa-building"></i>
                        {% elif expense.category == 'marketing' %}
                            <i class="fas fa-bullhorn"></i>
                        {% elif expense.category == 'utilities' %}
                            <i class="fas fa-bolt"></i>
                        {% else %}
                            <i class="fas fa-receipt"></i>
                        {% endif %}
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1">{{ expense.description|truncatechars:50 }}</h6>
                        <small class="text-muted">{{ expense.expense_date|date:"d/m/Y" }}</small>
                    </div>
                    <div>
                        <span class="expense-status status-{{ expense.status|default:'pending' }}">
                            {% if expense.status == 'approved' %}معتمدة
                            {% elif expense.status == 'pending' %}معلقة
                            {% elif expense.status == 'rejected' %}مرفوضة
                            {% elif expense.status == 'paid' %}مدفوعة
                            {% else %}{{ expense.get_status_display|default:'معلقة' }}
                            {% endif %}
                        </span>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">المبلغ</small>
                        <div class="expense-amount">{{ expense.amount|default:'0' }} درهم</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">الفئة</small>
                        <div>
                            {% if expense.category == 'travel' %}سفر
                            {% elif expense.category == 'office' %}مكتب
                            {% elif expense.category == 'marketing' %}تسويق
                            {% elif expense.category == 'utilities' %}مرافق
                            {% else %}أخرى
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                {% if expense.vendor %}
                <div class="mb-3">
                    <small class="text-muted">المورد</small>
                    <p class="mb-0">{{ expense.vendor }}</p>
                </div>
                {% endif %}
                
                {% if expense.notes %}
                <div class="mb-3">
                    <small class="text-muted">ملاحظات</small>
                    <p class="mb-0">{{ expense.notes|truncatewords:15 }}</p>
                </div>
                {% endif %}
                
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        {% if expense.created_by %}
                            <small class="text-muted">بواسطة: {{ expense.created_by.get_full_name|default:expense.created_by.username }}</small>
                        {% endif %}
                    </div>
                    <div class="btn-group" role="group">
                        <button class="btn btn-outline-primary btn-sm" onclick="alert('ميزة عرض التفاصيل قيد التطوير')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="alert('ميزة التعديل قيد التطوير')">
                            <i class="fas fa-edit"></i>
                        </button>
                        {% if expense.receipt %}
                        <button class="btn btn-outline-success btn-sm" onclick="viewReceipt({{ expense.pk }})">
                            <i class="fas fa-file-image"></i>
                        </button>
                        {% endif %}
                        <button class="btn btn-outline-info btn-sm" onclick="printExpense({{ expense.pk }})">
                            <i class="fas fa-print"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-receipt fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد مصروفات</h4>
                <p class="text-muted">ابدأ بإضافة مصروف جديد</p>
                <button class="btn btn-primary" onclick="alert('ميزة إضافة المصروفات قيد التطوير')">
                    <i class="fas fa-plus me-2"></i>إضافة مصروف جديد
                </button>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="row">
        <div class="col-12">
            <nav aria-label="صفحات المصروفات">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1">الأولى</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                        </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                    </li>
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function viewReceipt(expenseId) {
    window.open('/finance/expenses/' + expenseId + '/receipt/', '_blank');
}

function printExpense(expenseId) {
    window.open('/finance/expenses/' + expenseId + '/print/', '_blank');
}

$(document).ready(function() {
    console.log('Expenses list loaded');
});
</script>
{% endblock %}
