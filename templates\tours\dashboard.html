{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة تحكم الباقات السياحية{% endblock %}

{% block extra_css %}
<style>
    .dashboard-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: transform 0.3s ease;
    }
    
    .dashboard-card:hover {
        transform: translateY(-2px);
    }
    
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        text-align: center;
        margin-bottom: 20px;
    }
    
    .stat-card.success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }
    
    .stat-card.warning {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    }
    
    .stat-card.danger {
        background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .chart-container {
        height: 300px;
        position: relative;
    }
    
    .package-item {
        display: flex;
        align-items: center;
        padding: 15px;
        border-bottom: 1px solid #e9ecef;
        transition: background-color 0.3s ease;
    }
    
    .package-item:hover {
        background-color: #f8f9fa;
    }
    
    .package-item:last-child {
        border-bottom: none;
    }
    
    .package-image {
        width: 60px;
        height: 60px;
        border-radius: 10px;
        object-fit: cover;
        margin-left: 15px;
    }
    
    .package-placeholder {
        width: 60px;
        height: 60px;
        border-radius: 10px;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 15px;
    }
    
    .quick-actions {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }
    
    .quick-action-btn {
        flex: 1;
        min-width: 200px;
        padding: 15px;
        border-radius: 10px;
        text-decoration: none;
        color: white;
        text-align: center;
        transition: transform 0.3s ease;
    }
    
    .quick-action-btn:hover {
        transform: translateY(-2px);
        color: white;
    }
    
    .action-primary { background: linear-gradient(135deg, #007bff, #0056b3); }
    .action-success { background: linear-gradient(135deg, #28a745, #1e7e34); }
    .action-info { background: linear-gradient(135deg, #17a2b8, #138496); }
    .action-warning { background: linear-gradient(135deg, #ffc107, #e0a800); }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="fas fa-tachometer-alt text-primary me-2"></i>
                لوحة تحكم الباقات السياحية
            </h1>
            <p class="text-muted mb-0">نظرة عامة على الباقات السياحية والإحصائيات</p>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="stat-number">{{ total_packages }}</div>
                <div class="stat-label">إجمالي الباقات</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stat-card success">
                <div class="stat-number">{{ active_packages }}</div>
                <div class="stat-label">الباقات النشطة</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stat-card warning">
                <div class="stat-number">{{ featured_packages }}</div>
                <div class="stat-label">الباقات المميزة</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stat-card danger">
                <div class="stat-number">0</div>
                <div class="stat-label">الباقات المعطلة</div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="dashboard-card">
                <h4 class="mb-3">
                    <i class="fas fa-bolt text-primary me-2"></i>
                    إجراءات سريعة
                </h4>
                <div class="quick-actions">
                    <a href="{% url 'tours:package_add' %}" class="quick-action-btn action-primary">
                        <i class="fas fa-plus fa-2x mb-2"></i><br>
                        إضافة باقة جديدة
                    </a>
                    <a href="{% url 'tours:package_list' %}" class="quick-action-btn action-success">
                        <i class="fas fa-list fa-2x mb-2"></i><br>
                        عرض جميع الباقات
                    </a>
                    <a href="{% url 'tours:destination_list' %}" class="quick-action-btn action-info">
                        <i class="fas fa-map-marker-alt fa-2x mb-2"></i><br>
                        إدارة الوجهات
                    </a>
                    <a href="/reports/" class="quick-action-btn action-warning">
                        <i class="fas fa-chart-bar fa-2x mb-2"></i><br>
                        التقارير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Categories Breakdown -->
        <div class="col-lg-6">
            <div class="dashboard-card">
                <h4 class="mb-3">
                    <i class="fas fa-tags text-primary me-2"></i>
                    توزيع الفئات
                </h4>
                {% if category_stats %}
                    {% for category in category_stats %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>{{ category.name_ar }}</span>
                        <span class="badge bg-primary">{{ category.package_count }} باقة</span>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">لا توجد فئات متاحة</p>
                {% endif %}
            </div>
        </div>

        <!-- Top Destinations -->
        <div class="col-lg-6">
            <div class="dashboard-card">
                <h4 class="mb-3">
                    <i class="fas fa-globe text-primary me-2"></i>
                    أهم الوجهات
                </h4>
                {% if destination_stats %}
                    {% for destination in destination_stats %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>{{ destination.name_ar }}</span>
                        <span class="badge bg-success">{{ destination.package_count }} باقة</span>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">لا توجد وجهات متاحة</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Packages -->
        <div class="col-lg-6">
            <div class="dashboard-card">
                <h4 class="mb-3">
                    <i class="fas fa-clock text-primary me-2"></i>
                    الباقات الحديثة
                </h4>
                {% if recent_packages %}
                    {% for package in recent_packages %}
                    <div class="package-item">
                        {% if package.main_image %}
                            <img src="{{ package.main_image.url }}" class="package-image" alt="{{ package.title_ar }}">
                        {% else %}
                            <div class="package-placeholder">
                                <i class="fas fa-image text-muted"></i>
                            </div>
                        {% endif %}
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ package.title_ar }}</h6>
                            <small class="text-muted">{{ package.package_code }} - {{ package.created_at|date:"d/m/Y" }}</small>
                        </div>
                        <div>
                            <span class="badge bg-primary">{{ package.base_price }} درهم</span>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">لا توجد باقات حديثة</p>
                {% endif %}
            </div>
        </div>

        <!-- Popular Packages -->
        <div class="col-lg-6">
            <div class="dashboard-card">
                <h4 class="mb-3">
                    <i class="fas fa-fire text-primary me-2"></i>
                    الباقات الأكثر شعبية
                </h4>
                {% if popular_packages %}
                    {% for package in popular_packages %}
                    <div class="package-item">
                        {% if package.main_image %}
                            <img src="{{ package.main_image.url }}" class="package-image" alt="{{ package.title_ar }}">
                        {% else %}
                            <div class="package-placeholder">
                                <i class="fas fa-image text-muted"></i>
                            </div>
                        {% endif %}
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ package.title_ar }}</h6>
                            <small class="text-muted">{{ package.package_code }}</small>
                        </div>
                        <div>
                            <span class="badge bg-success">{{ package.reservation_count }} حجز</span>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">لا توجد بيانات حجوزات متاحة</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Monthly Trend Chart -->
    <div class="row">
        <div class="col-12">
            <div class="dashboard-card">
                <h4 class="mb-3">
                    <i class="fas fa-chart-line text-primary me-2"></i>
                    اتجاه إنشاء الباقات الشهري
                </h4>
                <div class="chart-container">
                    <canvas id="monthlyChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Monthly packages chart
    const monthlyData = {{ monthly_packages|safe }};
    
    if (monthlyData && monthlyData.length > 0) {
        const ctx = document.getElementById('monthlyChart').getContext('2d');
        
        const labels = monthlyData.map(item => {
            const date = new Date(item.month);
            return date.toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });
        });
        
        const data = monthlyData.map(item => item.count);
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'عدد الباقات المنشأة',
                    data: data,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    } else {
        document.getElementById('monthlyChart').innerHTML = '<p class="text-center text-muted mt-5">لا توجد بيانات متاحة للعرض</p>';
    }
    
    console.log('Tours dashboard loaded');
});
</script>
{% endblock %}
